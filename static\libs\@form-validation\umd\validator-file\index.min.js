/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/validator-file
 * @version 2.4.0
 */

!function(e,i){"object"==typeof exports&&"undefined"!=typeof module?module.exports=i():"function"==typeof define&&define.amd?define(i):((e="undefined"!=typeof globalThis?globalThis:e||self).FormValidation=e.FormValidation||{},e.FormValidation.validators=e.FormValidation.validators||{},e.FormValidation.validators.file=i())}(this,(function(){"use strict";var e=function(e){return-1===e.indexOf(".")?e:e.split(".").slice(0,-1).join(".")};return function(){return{validate:function(i){if(""===i.value)return{valid:!0};var t,n,o=i.options.extension?i.options.extension.toLowerCase().split(",").map((function(e){return e.trim()})):[],a=i.options.type?i.options.type.toLowerCase().split(",").map((function(e){return e.trim()})):[];if(window.File&&window.FileList&&window.FileReader){var r=i.element.files,s=r.length,l=0;if(i.options.maxFiles&&s>parseInt("".concat(i.options.maxFiles),10))return{meta:{error:"INVALID_MAX_FILES"},valid:!1};if(i.options.minFiles&&s<parseInt("".concat(i.options.minFiles),10))return{meta:{error:"INVALID_MIN_FILES"},valid:!1};for(var d={},m=0;m<s;m++){if(l+=r[m].size,d={ext:t=r[m].name.substr(r[m].name.lastIndexOf(".")+1),file:r[m],size:r[m].size,type:r[m].type},i.options.minSize&&r[m].size<parseInt("".concat(i.options.minSize),10))return{meta:Object.assign({},{error:"INVALID_MIN_SIZE"},d),valid:!1};if(i.options.maxSize&&r[m].size>parseInt("".concat(i.options.maxSize),10))return{meta:Object.assign({},{error:"INVALID_MAX_SIZE"},d),valid:!1};if(o.length>0&&-1===o.indexOf(t.toLowerCase()))return{meta:Object.assign({},{error:"INVALID_EXTENSION"},d),valid:!1};if(a.length>0&&r[m].type&&-1===a.indexOf(r[m].type.toLowerCase()))return{meta:Object.assign({},{error:"INVALID_TYPE"},d),valid:!1};if(i.options.validateFileName&&!i.options.validateFileName(e(r[m].name)))return{meta:Object.assign({},{error:"INVALID_NAME"},d),valid:!1}}if(i.options.maxTotalSize&&l>parseInt("".concat(i.options.maxTotalSize),10))return{meta:Object.assign({},{error:"INVALID_MAX_TOTAL_SIZE",totalSize:l},d),valid:!1};if(i.options.minTotalSize&&l<parseInt("".concat(i.options.minTotalSize),10))return{meta:Object.assign({},{error:"INVALID_MIN_TOTAL_SIZE",totalSize:l},d),valid:!1}}else{if(t=i.value.substr(i.value.lastIndexOf(".")+1),o.length>0&&-1===o.indexOf(t.toLowerCase()))return{meta:{error:"INVALID_EXTENSION",ext:t},valid:!1};if(n=e(i.value),i.options.validateFileName&&!i.options.validateFileName(n))return{meta:{error:"INVALID_NAME",name:n},valid:!1}}return{valid:!0}}}}}));
