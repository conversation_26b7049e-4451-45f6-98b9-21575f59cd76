// * Pricing
// *******************************************************************************

@use '../_bootstrap-extended/include' as light;
@use '../_bootstrap-extended/include-dark' as dark;
@import '../_custom-variables/pages';

.pricing-plans {
  ul {
    list-style-type: circle;
    li::marker {
      font-size: 1.4rem;
    }
  }
}

.pricing-plans-comparison {
  .table {
    tr {
      > th:first-child,
      > td:first-child {
        white-space: nowrap;
        text-align: start;
      }
    }
    tbody {
      tr:last-child {
        td {
          border-bottom: 0;
        }
      }
    }
  }
}
.price-yearly-toggle {
  position: absolute;
  margin: auto;
  left: 0;
  right: 0;
}

// To position illustration
@include light.media-breakpoint-up(lg) {
  .pricing-free-trial {
    img {
      position: absolute;
      bottom: 0;
      inset-inline-end: 0;
      height: 110%;
      width: auto;
    }
  }
}
