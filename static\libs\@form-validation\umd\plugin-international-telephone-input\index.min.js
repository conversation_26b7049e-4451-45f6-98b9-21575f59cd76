/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-international-telephone-input
 * @version 2.4.0
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("@form-validation/core")):"function"==typeof define&&define.amd?define(["@form-validation/core"],t):((e="undefined"!=typeof globalThis?globalThis:e||self).FormValidation=e.FormValidation||{},e.FormValidation.plugins=e.FormValidation.plugins||{},e.FormValidation.plugins.InternationalTelephoneInput=t(e.FormValidation))}(this,(function(e){"use strict";var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},t(e,n)};return function(e){function n(t){var n=e.call(this,t)||this;return n.intlTelInstances=new Map,n.countryChangeHandler=new Map,n.fieldElements=new Map,n.hiddenFieldElements=new Map,n.opts=Object.assign({},{autoPlaceholder:"polite",utilsScript:""},t),n.validatePhoneNumber=n.checkPhoneNumber.bind(n),n.fields="string"==typeof n.opts.field?n.opts.field.split(","):n.opts.field,n.hiddenFieldInputs=n.opts.hiddenPhoneInput?"string"==typeof n.opts.hiddenPhoneInput?n.opts.hiddenPhoneInput.split(","):n.opts.hiddenPhoneInput:[],n.onValidatorValidatedHandler=n.onValidatorValidated.bind(n),n}return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}(n,e),n.prototype.install=function(){var e=this;this.core.registerValidator(n.INT_TEL_VALIDATOR,this.validatePhoneNumber);var t=this.hiddenFieldInputs.length;this.fields.forEach((function(i,o){var a;e.core.addField(i,{validators:(a={},a[n.INT_TEL_VALIDATOR]={message:e.opts.message},a)});var l=e.core.getElements(i)[0],r=function(){return e.core.revalidateField(i)};if(l.addEventListener("countrychange",r),e.countryChangeHandler.set(i,r),e.fieldElements.set(i,l),e.intlTelInstances.set(i,intlTelInput(l,e.opts)),o<t&&e.hiddenFieldInputs[o]){var d=document.createElement("input");d.setAttribute("type","hidden"),d.setAttribute("name",e.hiddenFieldInputs[o]),e.core.getFormElement().appendChild(d),e.hiddenFieldElements.set(i,d)}})),t>0&&this.core.on("core.validator.validated",this.onValidatorValidatedHandler)},n.prototype.uninstall=function(){var e=this,t=this.hiddenFieldInputs.length;this.fields.forEach((function(i,o){var a=e.countryChangeHandler.get(i),l=e.fieldElements.get(i),r=e.getIntTelInstance(i);if(a&&l&&r&&(l.removeEventListener("countrychange",a),e.core.disableValidator(i,n.INT_TEL_VALIDATOR),r.destroy()),o<t&&e.hiddenFieldInputs[o]){var d=e.hiddenFieldElements.get(i);d&&e.core.getFormElement().removeChild(d)}})),t>0&&this.core.off("core.validator.validated",this.onValidatorValidatedHandler),this.fieldElements.clear(),this.hiddenFieldElements.clear()},n.prototype.getIntTelInstance=function(e){return this.intlTelInstances.get(e)},n.prototype.onEnabled=function(){var e=this;this.fields.forEach((function(t){e.core.enableValidator(t,n.INT_TEL_VALIDATOR)}))},n.prototype.onDisabled=function(){var e=this;this.fields.forEach((function(t){e.core.disableValidator(t,n.INT_TEL_VALIDATOR);var i=e.hiddenFieldElements.get(t);i&&(i.value="")}))},n.prototype.checkPhoneNumber=function(){var e=this;return{validate:function(t){var n=t.value,i=e.getIntTelInstance(t.field);return""!==n&&i?{valid:i.isValidNumber()}:{valid:!0}}}},n.prototype.onValidatorValidated=function(e){if(0!==this.hiddenFieldInputs.length&&e.validator===n.INT_TEL_VALIDATOR){var t=e.field,i=this.hiddenFieldElements.get(t);if(i)if(this.isEnabled&&e.result.valid){var o=this.getIntTelInstance(t).getNumber();i.value=o}else i.value=""}},n.INT_TEL_VALIDATOR="___InternationalTelephoneInputValidator",n}(e.Plugin)}));
