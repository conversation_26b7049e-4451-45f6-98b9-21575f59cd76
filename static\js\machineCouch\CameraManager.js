/**
 * Object to create and manage the cameras for the 3D, XZ, YX and YZ views.
 *
 * <AUTHOR>
 * @version 1.0
 */
machineCouch.CameraManager = function(world) {
	// Properties.
	this.world = world;
	this.camera3D;
	this.cameraXZ;
	this.cameraYX;
	this.cameraYZ;
	this.selectedView;
	this.selectedCamera;

	/**
	 * Initialize the clusters in the input data object.
	 */
	this.init = function() {
		try {
			this.camera3D = new fortes.webGL.env.Camera(this.world.u_viewMatrix, this.world.u_projMatrix);
			this.camera3D.setPerspective(this.world.gl, 30, this.world.canvas.width / this.world.canvas.height, 0.1, 100);
			this.camera3D.lookAt(3.0, 3.0, 3.0, 0.0, 0.0, 0.0);
			this.camera3D.activate(this.world.gl, this.world.canvas);
			this.overideSelectionHandlerCalls(this.world, this.camera3D);
			this.selectedView = "3D";
			this.selectedCamera = this.camera3D;
			
			this.cameraXZ = new fortes.webGL.env.Camera(this.world.u_viewMatrix, this.world.u_projMatrix, fortes.webGL.env.CAMERA_UP_VECTOR_AXIS_X);
			this.cameraXZ.setOrtho(this.world.gl, -0.1, 1.1, -0.1, 1.1, -2.0, 100);
			this.overideSelectionHandlerCalls(this.world, this.cameraXZ);
			this.cameraXZ.mouseHandler.rotationEnabled = false;
			this.cameraXZ.mouseHandler.panEnabled = false;
			this.cameraXZ.mouseHandler.zoomEnabled = false;
			
			this.cameraYX = new fortes.webGL.env.Camera(this.world.u_viewMatrix, this.world.u_projMatrix);
			this.cameraYX.setOrtho(this.world.gl, -0.1, 1.1, -0.1, 1.1, -2.0, 100);
			this.overideSelectionHandlerCalls(this.world, this.cameraYX);
			this.cameraYX.mouseHandler.rotationEnabled = false;
			this.cameraYX.mouseHandler.panEnabled = false;
			this.cameraYX.mouseHandler.zoomEnabled = false;
			
			this.cameraYZ = new fortes.webGL.env.Camera(this.world.u_viewMatrix, this.world.u_projMatrix);
			this.cameraYZ.setOrtho(this.world.gl, -1.1, 0.1, -0.1, 1.1, -2.0, 100);
			this.overideSelectionHandlerCalls(this.world, this.cameraYZ);
			this.cameraYZ.mouseHandler.rotationEnabled = false;
			this.cameraYZ.mouseHandler.panEnabled = false;
			this.cameraYZ.mouseHandler.zoomEnabled = false;
			this.cameraYZ.yaw(90.0);
		} catch (exp) {
			console.log(exp);
		}
	};
	
	/**
	 * Select the camera for the passed view.
	 *
	 * @param view The view name: 3D, XZ, YX or YZ.
	 */
	this.selectView = function(view) {
		if (view == "3D") {
			this.selectedCamera.deactivate(this.world.canvas);
			this.camera3D.activate(this.world.gl, this.world.canvas);
			this.selectedCamera = this.camera3D;
			this.selectedView = view;
		} else if (view == "XZ") {
			this.selectedCamera.deactivate(this.world.canvas);
			this.cameraXZ.activate(this.world.gl, this.world.canvas);
			this.selectedCamera = this.cameraXZ;
			this.selectedView = view;
		} else if (view == "YX") {
			this.selectedCamera.deactivate(this.world.canvas);
			this.cameraYX.activate(this.world.gl, this.world.canvas);
			this.selectedCamera = this.cameraYX;
			this.selectedView = view;
		} else if (view == "YZ") {
			this.selectedCamera.deactivate(this.world.canvas);
			this.cameraYZ.activate(this.world.gl, this.world.canvas);
			this.selectedCamera = this.cameraYZ;
			this.selectedView = view;
		}
	};

	/**
	 * Assign the camera function to deal with the point selection.
	 *
	 * @param camera	The camera object to be assigned the function.
	 */
	this.overideSelectionHandlerCalls = function(world, camera) {
		var data = machineCouch.InputData.clusters.data;
		
		camera.mouseHandler.selectionEnabled = true;
		
		camera.selectionHandler.selectPointCall = function(event) {
			var selectionHandler = camera.selectionHandler;
			var pointSize = machineCouch.InputData.pointSize;
			var clusterSelectedIndex = -1;
			var pointSelectedIndex = -1;
			var selectedPoint = null;
			var points, point;
			var calulatedPoint;
			var deltaX, deltaY;
			var pointRadius = (pointSize / 2) / selectionHandler.origenX;
			var selectedZValue = 0.0;
			var i, j;

			for (i in world.clusters) {
				points = world.clusters[i].pointsData;
				
				for (j in points) {
					point = points[j];

					calulatedPoint = selectionHandler.getViewProjPointPosition([point[1], point[2], point[3], 1.0]);
					deltaX = selectionHandler.selectedPointX - calulatedPoint[0];
					deltaY = selectionHandler.selectedPointY - calulatedPoint[1];

					if (Math.sqrt(deltaX * deltaX + deltaY * deltaY) <= pointRadius) {
						if (selectedPoint == null || selectedZValue > calulatedPoint[2]) {
							clusterSelectedIndex = world.clusters[i].index;
							pointSelectedIndex = j;
							selectedPoint = point;
							selectedZValue = calulatedPoint[2];
						}
					}
				}
			}
			
			if (clusterSelectedIndex != -1 && pointSelectedIndex != -1) {
				world.clusterSelectedIndex = clusterSelectedIndex;
				world.pointSelectedIndex = pointSelectedIndex;
				world.studentPopupPos.x = event.offsetX + pointSize;
				world.studentPopupPos.y = event.offsetY + pointSize;
				world.inputData.getStudentData(selectedPoint[0]);
			}
		};
		
		camera.selectionHandler.cleanCall = function(event) {
			world.clusterSelectedIndex = world.pointSelectedIndex = -1;
			
			if (world.studentPopup != null) {
				world.studentPopup.style.display = "none";
			}
		};
	};
};


