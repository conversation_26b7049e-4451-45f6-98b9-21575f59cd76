/**
 * Program 3D World context.
 *
 * <AUTHOR>
 * @version 2.0
 */
machineCouch.MachineCouchWorld = function() {
	// Properties.
	this.inputData = machineCouch.InputData;	// The input data object.
	this.gl = null;								// The webGL context object.
	this.canvas = null;							// The browser canvas to work out.
	this.shaderProgram = null;					// The main shader program. In this program  we're going to use only one.
	this.studentPopup = null;					// The reference to the student popup panel.
	this.studentPopupPos = {x: 0, y: 0};		// Student popup current position coordinate.
	this.clusterSelectedIndex = -1;				// Variable to store the selected cluster index.
	this.pointSelectedIndex = -1;				// Variable to store the selected point index.
	this.models = [];							// Storage array for all the models to render.
	this.clusters = [];							// An array to keep the clusters references.
	this.cameraManager;							// Camara views manager.
	this.selectedViewButton = null;				// The selected view button in the page.

	// GLSL variable references.
	this.a_position = null;				// Vertex position attribute.
	this.u_color = null;				// Vertex color.
	this.u_clusterIndex = null;			// Cluster index.
	this.u_isClusterVS = null;			// Cluster model flag for the vertex shader.
	this.u_isClusterFS = null;			// Cluster model flag for the fragment shader.
	this.u_clusterSelectedIndex = null;	// Cluster selected index.
	this.a_pointIndex = null;			// Cluster point index.
	this.a_pointColor = null;			// Cluster point color.
	this.u_pointSize = null;			// Vertex point size.
	this.u_pointSelectedIndex = null;	// Cluster point selected index.
	this.u_modelMatrix = null;			// The model matrix.
	this.u_viewMatrix = null;			// The view matrix.
	this.u_projMatrix = null;			// The projection matrix.
	
	
	this.loadStudentsData = function() {
        try {
            var spanContent = document.getElementById("studentsResult");
            
            if (spanContent !== undefined && spanContent !== null && spanContent.innerText.length > 0) {
                var dataObj = JSON.parse(spanContent.innerText);
                var finalData = [];
                var item;
                
                for (var i in dataObj.data) {
                    item = dataObj.data[i];
                    
                    finalData.push(item[0], item[1], item[2], item[3]);
                }
                
                machineCouch.InputData.clusters.data = finalData;
            }
        } catch (exp) {
			console.log(exp);
		}
    };

	/**
	* Initialize the browser canvas to render and the WebGL context.
	*/
	this.initContext = function() {
		try {
			this.canvas = document.getElementById("wgl_machineCouchWorldCanvas");

			if (!this.canvas) {
				throw new Error("Cannot find the canvas element for the world display.");
			}

			this.gl = this.canvas.getContext("experimental-webgl");

			if (!this.gl) {
				throw new Error("Cannot initialize the GL context object.");
			}

			this.studentPopup = document.getElementById("wgl_studentPopup");
			this.selectedViewButton = document.getElementById("wgl_view3DButton");
		} catch (exp) {
			console.log(exp);

			return false;
		}

		return true;
	};

	/**
	* Initialize the vertex and fragment shaders. Then link them in a shader program.
	*
	* @param vertexShaderCode Vertex shader code as a string.
	* @param fragmentShaderCode Fragment shader code as a string.
	*/
	this.initShaders = function(vertexShaderCode, fragmentShaderCode) {
		try {
			// Create a vertex shader object.
			var vertexShader = this.gl.createShader(this.gl.VERTEX_SHADER);

			// Attach vertex shader source code.
			this.gl.shaderSource(vertexShader, vertexShaderCode);

			// Compile the vertex shader.
			this.gl.compileShader(vertexShader);

			// Create fragment shader object.
			var fragmentShader = this.gl.createShader(this.gl.FRAGMENT_SHADER);

			// Attach fragment shader source code.
			this.gl.shaderSource(fragmentShader, fragmentShaderCode);

			// Compile the fragment shader.
			this.gl.compileShader(fragmentShader);

			// Create the shader program object to store combined shader program.
			this.shaderProgram = this.gl.createProgram();

			// Attach a vertex shader.
			this.gl.attachShader(this.shaderProgram, vertexShader); 

			// Attach a fragment shader.
			this.gl.attachShader(this.shaderProgram, fragmentShader);

			// Link both programs.
			this.gl.linkProgram(this.shaderProgram);

			// Use the combined shader program object.
			this.gl.useProgram(this.shaderProgram);

			if (this.shaderProgram == null) {
				return false;
			}
		} catch (exp) {
			console.log(exp);

			return false;
		}

		return true;
	};

	/**
	* Initialize GLSL variables.
	*/
	this.initGLSLProperties = function() {
		try {
			// Attributes.
			this.a_position = this.gl.getAttribLocation(this.shaderProgram, "a_position");
			this.a_pointIndex = this.gl.getAttribLocation(this.shaderProgram, "a_pointIndex");
			this.a_pointColor = this.gl.getAttribLocation(this.shaderProgram, "a_pointColor");

			// Uniforms.
			this.u_clusterIndex = this.gl.getUniformLocation(this.shaderProgram, "u_clusterIndex");
			this.u_clusterSelectedIndex = this.gl.getUniformLocation(this.shaderProgram, "u_clusterSelectedIndex");
			this.u_isClusterVS = this.gl.getUniformLocation(this.shaderProgram, "u_isClusterVS");
			this.u_isClusterFS = this.gl.getUniformLocation(this.shaderProgram, "u_isClusterFS");
			this.u_color = this.gl.getUniformLocation(this.shaderProgram, "u_color");
			this.u_pointSize = this.gl.getUniformLocation(this.shaderProgram, "u_pointSize");
			this.u_pointSelectedIndex= this.gl.getUniformLocation(this.shaderProgram, "u_pointSelectedIndex");
			this.u_modelMatrix = this.gl.getUniformLocation(this.shaderProgram, 'u_modelMatrix');
			this.u_viewMatrix = this.gl.getUniformLocation(this.shaderProgram, 'u_viewMatrix');
			this.u_projMatrix = this.gl.getUniformLocation(this.shaderProgram, 'u_projMatrix');
			
			this.gl.uniform1f(this.u_pointSize, this.inputData.pointSize);
		} catch (exp) {
			console.log(exp);

			return false;
		}

		return true;
	};

	/**
	* Initialize the environment models. Lights and cameras.
	*/
	this.initEnvironment = function() {
		try {
			// Initialize the Cameras objects.
			this.cameraManager = new machineCouch.CameraManager(this);
			this.cameraManager.init();
		} catch (exp) {
			console.log(exp);

			return false;
		}

		return true;
	};

	/**
	* Initialize all the models in the program.
	*/
	this.initModels = function() {
		try {
			// Create Cluster models.
			var clusterManager = new machineCouch.ClusterManager();
			var clusterModels = clusterManager.init(this.gl);
			var i;

			for (i in clusterModels) {
				if (clusterModels[i].isLoaded === true) {
					this.models.push(clusterModels[i]);
					this.clusters.push(clusterModels[i]);
				}
			}

			// Create Environment model.
			var envModel = new machineCouch.model.EnvModel();
			envModel.initSubModels(this);
			this.models.push(envModel);
		} catch (exp) {
			console.log(exp);

			return false;
		}

		return true;
	};

	/**
	* Enable/disable webGL flags.
	*/
	this.initWebGLFlags = function() {
		this.gl.enable(this.gl.DEPTH_TEST);
	};

	/**
	* Method to show the student popup loaded with the income data.
	*
	* @param data The student data.
	*/
	this.showPopup = function(data) {
		var domElements = null;   // The popup children fields.
		var dataFields = null;    // The data fields.
		var i = 0, j = 0;

		if (this.studentPopup != null) {
			domElements = this.studentPopup.getElementsByTagName("DIV");

			for (i in domElements) {
				for (j in data) {
					if (j == domElements[i].id) {
						domElements[i].innerHTML = data[j];
						break;
					}
				}
			}

			this.studentPopup.style.left = this.studentPopupPos.x + "px";
			this.studentPopup.style.top = this.studentPopupPos.y + "px";
			this.studentPopup.style.display = "block";
		}
	};
	
	/**
	 * Select the camera for the passed view. Change also the selected element.
	 *
	 * @param viewButton	The view button element.
	 * @param view 			The view name: 3D, XZ, YX or YZ.
	 */
	this.selectView = function(viewButton, view) {
		if (this.selectedViewButton != null) {
			this.selectedViewButton.style.color = "#000000";
			this.selectedViewButton.style.backgroundColor = "#ffffff";
		}
			
		this.selectedViewButton = viewButton;
		this.selectedViewButton.style.color = "#ffffff";
		this.selectedViewButton.style.backgroundColor = "#252525";
		this.cameraManager.selectView(view);
	};

	/**
	* Render all the models.
	*
	* @param time  The timestamp.
	*/
	this.render = function(time) {
		var camera = this.cameraManager.selectedCamera;
		
		// Set the cluster and the point selected Indices.
		this.gl.uniform1f(this.u_clusterSelectedIndex, this.clusterSelectedIndex);
		this.gl.uniform1f(this.u_pointSelectedIndex, this.pointSelectedIndex);

		// Set the camera position.
		camera.move(this.gl);

		// Set canvas background color.
		this.gl.clearColor(0.15, 0.15, 0.15, 1.0);

		// Set the viewport size.
		this.gl.viewport(0.0, 0.0, this.canvas.width, this.canvas.height);

		// Clear canvas background color and depth buffer.
		this.gl.clear(this.gl.COLOR_BUFFER_BIT | this.gl.DEPTH_BUFFER_BIT);

		// Render each model whether visible.
		for (var i in this.models) {
			if (this.models[i].visible) {
				this.models[i].move(this);
				this.models[i].render(this);
			}
		}
	};
	
	this.refreshClusters = function() {
		try {
		    this.loadStudentsData();
			var clusterManager = new machineCouch.ClusterManager();
			clusterManager.destroy(this.clusters);
			var clusterModels = clusterManager.init(this.gl);
			this.clusters = [];
			var i = 0;
			var length = this.models.length;
			
			for (; i < length; ++i) {
    			if (this.models[i].type == "cluster") {
    				this.models.splice(i, 1);
    				--i;
    			}
    		}

			for (i in clusterModels) {
				if (clusterModels[i].isLoaded === true) {
					this.models.push(clusterModels[i]);
					this.clusters.push(clusterModels[i]);
				}
			}
		} catch (exp) {
			console.log(exp);

			return false;
		}

		return true;
	};
};





