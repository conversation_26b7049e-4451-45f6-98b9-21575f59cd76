/**
 * Class to manage the view object, or the camera position.
 
 * <AUTHOR>
 * @version	1.0
 * @date	01/2019
 *
 * @param u_viewMatrix		The pointer for the view matrix in the GLSL.
 * @param u_projMatrix		The pointer for the projection matrix in the GLSL.
 * @param upVectorAxis		The up vector axis: CAMERA_UP_VECTOR_AXIS_X, CAMERA_UP_VECTOR_AXIS_Y, CAMERA_UP_VECTOR_AXIS_Z.
 */
fortes.webGL.env.Camera = function(u_viewMatrix, u_projMatrix, upVectorAxis) {
	// GLSL variables.
	this.u_viewMatrix = u_viewMatrix;	// The pointer for the view matrix in the GLSL.
	this.u_projMatrix = u_projMatrix;	// The pointer for the projection matrix in the GLSL.
	
	// Camara properties.
	this.upVectorAxis = (upVectorAxis === undefined || upVectorAxis < 1 || upVectorAxis > 3) ?
		fortes.webGL.env.CAMERA_UP_VECTOR_AXIS_Y : upVectorAxis;
	this.moveX = 0.0;										// The absolute moving value in X axis.
	this.moveY = 0.0;										// The absolute moving value in Y axis.
	this.moveZ = 0.0;										// The absolute moving value in X axis.
	this.angleX = 0.0;										// The absolute rotation angle around the X axis.
	this.angleY = 0.0;										// The absolute rotation angle around the Y axis.
	this.angleZ = 0.0;										// The absolute rotation angle around the Z axis.
	this.moveFromTarget = 0.0;								// The absolute moving value between the camera position and the target.
	this.isActive = false;									// A flag to set the camera as active or inactive.
	this.viewMatrix = new fortes.webGL.util.Matrix4x4();	// The view matrix.
	this.projMatrix = new fortes.webGL.util.Matrix4x4();	// The projection matrix.

	// Event handlers.
	if (fortes.webGL.env.camera.MouseHandler !== undefined) {
		this.mouseHandler = new fortes.webGL.env.camera.MouseHandler(this);			// The mouse event handler.
	}
	
	if (fortes.webGL.env.camera.PanZoomHandler !== undefined) {
		this.panZoomHandler = new fortes.webGL.env.camera.PanZoomHandler();			// The pan and zoom event handlers.
	}
	
	if (fortes.webGL.env.camera.SelectionHandler !== undefined) {
		this.selectionHandler = new fortes.webGL.env.camera.SelectionHandler(this);	// The selection event handler.
	}
	
	/**
	 * Activate the camera setting up the mouse events in the canvas.
	 * The camera projection matrix is passed to GPU.
	 *
	 * @gl		The webGL context to regist the projection matrix.
	 * @canvas	The canvas to set the mouse events.
	 */
	this.activate = function(gl, canvas) {
		try {
			if (this.mouseHandler !== undefined) {
				this.mouseHandler.addEvents(canvas);
			}
			
			gl.uniformMatrix4fv(this.u_projMatrix, false, this.projMatrix.elements);
			this.isActive = true;
		} catch (exp) {
			console.log(exp);
    	}
	};
	
	/**
	 * Deactivate the camera setting up the mouse events in the canvas.
	 *
	 * @canvas		The canvas to set the mouse events.
	 */
	this.deactivate = function(canvas) {
		try {
			if (this.mouseHandler !== undefined) {
				this.mouseHandler.removeEvents(canvas);
			}
			
			this.isActive = false;
		} catch (exp) {
			console.log(exp);
    	}
	};
	
	/**
	 * Initialize the projection matrix, with a orthographic frustom, for the camera.
	 *
	 * @param gl The webGL context object.
	 * @param left The coordinate of the left of clipping plane.
	 * @param right The coordinate of the right of clipping plane.
	 * @param bottom The coordinate of the bottom of clipping plane.
	 * @param top The coordinate of the top top clipping plane.
	 * @param near The distances to the nearer depth clipping plane. This value is minus if the plane is to be behind the viewer.
	 * @param far The distances to the farther depth clipping plane. This value is minus if the plane is to be behind the viewer.
	 */
	this.setOrtho = function(gl, left, right, bottom, top, near, far) {
		try {
			// Calculate the projection matrix and pass it to the GPU.
			this.projMatrix.setOrtho(left, right, bottom, top, near, far);
		} catch (exp) {
			console.log(exp);
		}
	};
	
	/**
	 * Initialize the projection matrix with a perspective frustom, for the camera.
	 *
	 * @param gl The webGL context object.
	 * @param fovy The angle between the upper and lower sides of the frustum.
	 * @param aspect The aspect ratio of the frustum. (width/height).
	 * @param near The distances to the nearer depth clipping plane. This value must be plus value.
	 * @param far The distances to the farther depth clipping plane. This value must be plus value.
	 */
	this.setPerspective = function(gl, fovy, aspect, near, far) {
		try {
			// Calculate the projection matrix and pass it to the GPU.
			this.projMatrix.setPerspective(fovy, aspect, near, far);
			
		} catch (exp) {
			console.log(exp);
		}
	};
	
	/**
	 * Set the camera position looking at a target point.
	 *
	 * @param eyeX			The X axis eye position.
	 * @param eyeY			The Y axis eye position.
	 * @param eyeZ			The Z axis eye position.
	 * @param targetX		The X axis target position.
	 * @param targetY		The Y axis target position.
	 * @param targetZ		The Z axis target position.
	 */
	this.lookAt = function(eyeX, eyeY, eyeZ, targetX, targetY, targetZ) {
		try {
			var deltaX = eyeX - targetX;
			var deltaY = eyeY - targetY;
			var deltaZ = eyeZ - targetZ;
			var pkg = fortes.webGL.env;

			if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_X) {
				this.calculateAnglesForUpVectorAxisX(deltaX, deltaY, deltaZ);
			} else if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_Y) {
				this.calculateAnglesForUpVectorAxisY(deltaX, deltaY, deltaZ);
			} else if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_Z) {
				this.calculateAnglesForUpVectorAxisZ(deltaX, deltaY, deltaZ);
			}

			this.moveFromTarget = Math.sqrt(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ) * (-1);
			this.setTranslate(targetX, targetY, targetZ);
		} catch (exp) {
			console.log(exp);
		}
	};
	
	/**
	 * Set the value of the moveFromTarget variable.
	 */
	this.setMoveFromTarget = function(value) {
		moveFromTarget = value * (-1);
	}
	
	/**
	 * Translate relatively the camera position.
	 *
	 * @param x	The X axis value.
	 * @param y	The Y axis value.
	 * @param z	The Z axis value.
	 */
	this.translate = function(x, y, z) {
		this.moveX -= x;
		this.moveY -= y;
		this.moveZ -= z;
	};
	
	/**
	 * Set the absolute camera position.
	 *
	 * @param x	The X axis value.
	 * @param y	The Y axis value.
	 * @param z	The Z axis value.
	 */
	this.setTranslate = function(x, y, z) {
		this.moveX = x * (-1);
		this.moveY = y * (-1);
		this.moveZ = z * (-1);
	};
	
	/**
	 * Roll the camera relatively according to the up vector.
	 *
	 * @param angle	The angle in celcius degree.
	 */
	this.roll = function(angle) {
		var pkg = fortes.webGL.env;
		
		if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_X) {
			this.angleY = this.clumpAngle(this.angleY + angle, -360, 360);
		} else if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_Y) {
			this.angleZ = this.clumpAngle(this.angleZ + angle, -360, 360);
		} else if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_Z) {
			this.angleX = this.clumpAngle(this.angleX + angle, -360, 360);
		}
	};
	
	/**
	 * Yaw the camera relatively according to the up vector.
	 *
	 * @param angle	The angle in celcius degree.
	 */
	this.yaw = function(angle) {
		var pkg = fortes.webGL.env;
		
		if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_X) {
			this.angleX = this.clumpAngle(this.angleX - angle, -360, 360);
		} else if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_Y) {
			this.angleY = this.clumpAngle(this.angleY - angle, -360, 360);
		} else if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_Z) {
			this.angleZ = this.clumpAngle(this.angleZ - angle, -360, 360);
		}
	};
	
	/**
	 * Pitch the camera relatively according to the up vector.
	 *
	 * @param angle	The angle in celcius degree.
	 */
	this.pitch = function(angle) {
		var pkg = fortes.webGL.env;
		
		if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_X) {
			this.angleZ = this.clumpAngle(this.angleZ + angle, -360, 360);
		} else if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_Y) {
			this.angleX = this.clumpAngle(this.angleX + angle, -360, 360);
		} else if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_Z) {
			this.angleY = this.clumpAngle(this.angleY + angle, -360, 360);
		}
	};
	
	/**
	 * Move the camera (translation, rotation, zoom and panning).
	 * Set up the view matrix and pass it to the GPU.
	 *
	 * @param gl The webGL context object.
	 */
	this.move = function(gl) {
		try {
			var pkg = fortes.webGL.env;
			
			// Initialize the view matrix.
			this.viewMatrix.setIdentity();
			
			// Translate the distance between the camera and the target.
			this.viewMatrix.translate(0.0, 0.0, this.moveFromTarget);
			
			if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_X) {
				this.rotateWithUpVectorAxisX();
			} else if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_Y) {
				this.rotateWithUpVectorAxisY();
			} else if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_Z) {
				this.rotateWithUpVectorAxisZ();
			}
			
			// Translate the camera out of the origin.
			this.viewMatrix.translate(this.moveX, this.moveY, this.moveZ);
			
			// Pan and zoom.
			this.panZoomHandler.move();
			this.viewMatrix.multiplyLeft(this.panZoomHandler.panZoomMatrix);

			// Send it to the GPS.
			gl.uniformMatrix4fv(this.u_viewMatrix, false, this.viewMatrix.elements);
		} catch (exp) {
			console.log(exp);
		}
	};
	
	/**
	 * Calculate the angles in the axis X and Y when the up vector axis is X.
	 *
	 * @param deltaX	The X vector lenght.
	 * @param deltaY	The Y vector lenght.
	 * @param deltaZ	The Z vector lenght.
	 */
	this.calculateAnglesForUpVectorAxisX = function(deltaX, deltaY, deltaZ) {
		var lenghtXY = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
		var lenghtYZ = Math.sqrt(deltaY * deltaY + deltaZ * deltaZ);
		var angleX = lenghtYZ > 0.0 ? (Math.acos(deltaY / lenghtYZ) * 180 * (-1)) / Math.PI : 0.0;
		var angleZ = lenghtXY > 0.0 ? (Math.acos(Math.abs(deltaY) / lenghtXY) * 180) / Math.PI : 0.0;
		
		if (deltaZ < 0.0) {
			this.angleX = angleX * (-1);
		} else {
			this.angleX = angleX;
		}
		
		if (deltaX < 0.0) {
			this.angleZ = angleZ * (-1);
		} else {
			this.angleZ = angleZ;
		}
	};
	
	/**
	 * Calculate the angles in the axis X and Y when the up vector axis is Y.
	 *
	 * @param deltaX	The X vector lenght.
	 * @param deltaY	The Y vector lenght.
	 * @param deltaZ	The Z vector lenght.
	 */
	this.calculateAnglesForUpVectorAxisY = function(deltaX, deltaY, deltaZ) {
		var lenghtYZ = Math.sqrt(deltaY * deltaY + deltaZ * deltaZ);
		var lenghtXZ = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
		var angleX = lenghtYZ > 0.0 ? (Math.acos(Math.abs(deltaZ) / lenghtYZ) * 180) / Math.PI : 0.0;
		var angleY = lenghtXZ > 0.0 ? (Math.acos(deltaZ / lenghtXZ) * 180 * (-1)) / Math.PI : 0.0;
		
		if (deltaY < 0.0) {
			this.angleX = angleX * (-1);
		} else {
			this.angleX = angleX;
		}
		
		if (deltaX < 0.0) {
			this.angleY = angleY * (-1);
		} else {
			this.angleY = angleY;
		}
	};
	
	/**
	 * Calculate the angles in the axis Y and Z when the up vector axis is Z.
	 *
	 * @param deltaX	The X vector lenght.
	 * @param deltaY	The Y vector lenght.
	 * @param deltaZ	The Z vector lenght.
	 */
	this.calculateAnglesForUpVectorAxisZ = function(deltaX, deltaY, deltaZ) {
		var lenghtXY = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
		var lenghtXZ = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
		var angleY = lenghtXZ > 0.0 ? (Math.acos(Math.abs(deltaX) / lenghtXZ) * 180) / Math.PI : 0.0;
		var angleZ = lenghtXY > 0.0 ? (Math.acos(deltaX / lenghtXY) * 180 * (-1)) / Math.PI : 0.0;
		
		if (deltaZ < 0.0) {
			this.angleY = angleY * (-1);
		} else {
			this.angleY = angleY;
		}
		
		if (deltaY < 0.0) {
			this.angleZ = angleZ * (-1);
		} else {
			this.angleZ = angleZ;
		}
	};
	
	this.clumpAngle = function(angle, min, max) {
		if (angle > max) {
			angle -= max;
		} else if (angle < min) {
			angle += max;
		}
		
		return angle;
	};
	
	/**
	 * Rotate the camera with the X axis as the up vector.
	 */
	this.rotateWithUpVectorAxisX = function() {
		// Set the initial rotations in the space.
		this.viewMatrix.rotate(90, 0.0, 0.0, 1.0);
		this.viewMatrix.rotate(90, 1.0, 0.0, 0.0);

		// Roll rotation.
		if (this.angleY !== 0.0) {
			this.viewMatrix.rotate(this.angleY, 0.0, 1.0, 0.0);
		}

		// Pitch rotation.
		if (this.angleZ !== 0.0) {
			this.viewMatrix.rotate(this.angleZ, 0.0, 0.0, 1.0);
		}

		// Yaw rotation.
		if (this.angleX !== 0.0) {
			this.viewMatrix.rotate(this.angleX, 1.0, 0.0, 0.0);
		}
	};
	
	/**
	 * Rotate the camera with the Y axis as the up vector.
	 */
	this.rotateWithUpVectorAxisY = function() {
		// Roll rotation.
		if (this.angleZ !== 0.0) {
			this.viewMatrix.rotate(this.angleZ, 0.0, 0.0, 1.0);
		}

		// Pitch rotation.
		if (this.angleX !== 0.0) {
			this.viewMatrix.rotate(this.angleX, 1.0, 0.0, 0.0);
		}

		// Yaw rotation.
		if (this.angleY !== 0.0) {
			this.viewMatrix.rotate(this.angleY, 0.0, 1.0, 0.0);
		}
	};
	
	/**
	 * Rotate the camera with the Z axis as the up vector.
	 */
	this.rotateWithUpVectorAxisZ = function() {
		// Set the initial rotations in the space.
		this.viewMatrix.rotate(-90, 1.0, 0.0, 0.0);
		this.viewMatrix.rotate(-90, 0.0, 0.0, 1.0);

		// Roll rotation.
		if (this.angleX !== 0.0) {
			this.viewMatrix.rotate(this.angleX, 1.0, 0.0, 0.0);
		}

		// Pitch rotation.
		if (this.angleY !== 0.0) {
			this.viewMatrix.rotate(this.angleY, 0.0, 1.0, 0.0);
		}

		// Yaw rotation.
		if (this.angleZ !== 0.0) {
			this.viewMatrix.rotate(this.angleZ, 0.0, 0.0, 1.0);
		}
	};
	
	/**
	 * Method to pass away all the properties and methods.
	 *
	 * @param	The child object extending this object.
	 */
	this.extendeTo = function(child) {
		var i;
		
		for (i in this) {
			child[i] = this[i];
		}
	}
};