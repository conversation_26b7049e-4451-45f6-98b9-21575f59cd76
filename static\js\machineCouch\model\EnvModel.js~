/**
 * Environment Model class. It contains a lot of submodels to assembly the environment.
 *
 * <AUTHOR>
 * @version 2.0
 */
machineCouch.model.EnvModel = function(parent) {
	// Inheritance.
	fortes.webGL.util.BasicModel.extendTo(this);

	// Properties.
	this.parent = parent;                  // The parent model.
	this.submodels = [];                   // The submodels array.

	/**
	 * Initialize all the submodels that assembly the environment.
	 *
	 * @param world The world object with shared data and the context.
	 */
	this.initSubModels = function(world) {
		var pkg = machineCouch.model.env;

		// Environment submodels.
		this.createSubModel(new pkg.AxeXModel(this), world);
		this.createSubModel(new pkg.AxeYModel(this), world);
		this.createSubModel(new pkg.AxeZModel(this), world);
		/*this.createSubModel(new pkg.GridsModel(this), world);
		this.createSubModel(new pkg.XAxeLegendModel(this), world);
		this.createSubModel(new pkg.YAxeLegendModel(this), world);
		this.createSubModel(new pkg.ZAxeLegendModel(this), world);
		this.createSubModel(new pkg.PercentageLegendeModel(this), world);*/
	}

	/**
	 * Creates a single submodel, following the basic pattern.
	 *
	 * @param model The model to be created and inserted into the models list.
	 * @param world The world object with shared data and the context.
	 */
	this.createSubModel = function(model, world) {
		model.createBuffers(world.gl);
		this.submodels.push(model);
	}

	/**
	 * Render the object and its submodels.
	 *
	 * @param world The world object with shared data and the context.
	 */
	this.render = function(world) {
		for (var i in this.submodels) {
			if (this.submodels[i].visible) {
				this.submodels[i].move(world);
				this.submodels[i].render(world);
			}
		}
	}
}