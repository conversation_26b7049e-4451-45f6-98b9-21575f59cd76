@use '../../scss/_bootstrap-extended/include' as light;
@use '../../scss/_bootstrap-extended/include-dark' as dark;
@use '../../scss/_components/include' as comp;

@import '../../scss/_custom-variables/libs';
@import '../../node_modules/datatables.net-select-bs5/css/select.bootstrap5';

// Light style
@if $enable-light-style {
  .light-style {
    table.dataTable tbody > tr.selected,
    table.dataTable tbody > tr > .selected {
      background-color: light.$table-active-bg;
    }
    table.dataTable tbody > tr.selected > *,
    table.dataTable tbody > tr > .selected > * {
      box-shadow: inset 0 0 0 light.$gray-50;
      color: light.$table-active-color;
    }
    table.dataTable tbody tr.selected,
    table.dataTable tbody th.selected,
    table.dataTable tbody td.selected {
      color: light.$table-active-color;
    }
  }
}

// Dark Style
@if $enable-dark-style {
  .dark-style {
    table.dataTable tbody > tr.selected,
    table.dataTable tbody > tr > .selected {
      background-color: dark.$table-active-bg;
      td {
        border-color: dark.$gray-300;
      }
    }
    table.dataTable tbody tr.selected,
    table.dataTable tbody th.selected,
    table.dataTable tbody td.selected {
      color: inherit;
    }
  }
}
