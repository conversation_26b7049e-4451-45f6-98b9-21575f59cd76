/**
 * Class to manage the camera keyboard events.
 
 * <AUTHOR>
 * @version 1.0
 * @date 	22/03/2019
 *
 * @param camera	The camera object.
 */
fortes.webGL.env.camera.KeyHandler = function(camera) {
	// Properties.
	this.camera = camera;			// The camera object.
	this.rotationStep = 1.0;		// The step for each rotation iteration.
	this.walkStep = 0.05;			// The step for each walk iteration.
	
	/**
	 * Add the keyboard events for the passed canvas.
	 *
	 * @param canvas	The canvas to set the mouse events.
	 */
	this.addEvents = function(canvas) {
		var _this = this;

		if (canvas != null) {
			if (canvas.getAttribute("tabindex") == null) {
				canvas.setAttribute("tabindex", "0");
			}
			
			canvas.focus();
			canvas.onkeydown = function(event) { _this.keyDown(event); };
		}
	};
	
	/**
	 * Remove the keyboard events from the passed canvas.
	 *
	 * @canvas	The canvas to set the mouse events.
	 */
	this.removeEvents = function(canvas) {
		if (canvas != null) {
			canvas.onkeydown = null;
		}
	};
	
	/**
	 * Canvas keyboard down event handler.
	 * Move the camera forward and backword;
	 * Rotate to the left and to right;
	 * Rotate up and down.
	 *
	 * @param event		The keyboard event object.
	 */
	this.keyDown = function(event) {
		try {
			if (event.keyCode == 38) {
				this.forward(event);
			} else if (event.keyCode == 40) {
				this.backward(event);
			} else if (event.keyCode == 37) {
				this.leftward();
			} else if (event.keyCode == 39) {
				this.rightward();
			}
		} catch (exp) {
			console.log(exp);
		}
	};
	
	/**
	 * Move forward or look down if ctrl key is pressed.
	 *
	 * @param event	The keyboard event object.
	 */
	this.forward = function(event) {
		var camera = this.camera;
		
		if (event.ctrlKey === true) {
			camera.pitch(-this.rotationStep);
		} else {
			if (camera.action === camera.ROTATING) {
				camera.action = camera.WALKING;
			}

			camera.walk(this.walkStep);
		}
	};
	
	/**
	 * Move backward or look up if ctrl key is pressed.
	 *
	 * @param event	The keyboard event object.
	 */
	this.backward = function(event) {
		var camera = this.camera;
		
		if (event.ctrlKey === true) {
			camera.pitch(this.rotationStep);
		} else {
			if (camera.action === camera.ROTATING) {
				camera.action = camera.WALKING;
			}

			camera.walk(this.walkStep * (-1));
		}
	};
	
	/**
	 * Look leftward.
	 */
	this.leftward = function() {
		var camera = this.camera;
		
		if (camera.action === camera.WALKING) {
			camera.setPreviousAngles();
			camera.calculatePosition = true;
			camera.action = camera.ROTATING;
		}

		camera.yaw(this.rotationStep);
	};
	
	/**
	 * Look rightward.
	 */
	this.rightward = function() {
		var camera = this.camera;
		
		if (camera.action === camera.WALKING) {
			camera.setPreviousAngles();
			camera.calculatePosition = true;
			camera.action = camera.ROTATING;
		}

		camera.yaw(-this.rotationStep);
	};
};