/**
 * Machine Couch Program WebGL view.
 * The main and start object.
 *
 * <AUTHOR>
 * @version 2.0
 */
(machineCouch.MachineCouchMain = function() {
	// Function to start and initialize the objects and loop the application. 
	function start() {
		var mcWorld = new machineCouch.MachineCouchWorld();
		
		machineCouch.machineCouchWorld = mcWorld;
		mcWorld.loadStudentsData();

		/**
		 * Animation loop function. To render the 3D world objects in a loop.
		 *
		 * @param time  The timestamp.
		 */
		var loop = function(time) {
			try {
				mcWorld.render(time);
				window.requestAnimationFrame(loop);
			} catch (exp) {
				console.log(exp);
			}
		}

		if (mcWorld.initContext() && 
				mcWorld.initShaders(machineCouch.shader.VERTEX_SHADER_CODE, machineCouch.shader.FRAGMENT_SHADER_CODE) &&
				mcWorld.initGLSLProperties() &&
				mcWorld.initEnvironment() &&
				mcWorld.initModels()) {
			mcWorld.initWebGLFlags();
			loop(0);
		}
	};

	// Start the view when the page is loaded.
	window.addEventListener("load", start);
})();