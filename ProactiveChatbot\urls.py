"""
URL configuration for ProactiveChatbot project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path
from django.urls import include, re_path
from main_app import views
from django.conf import settings
from django.conf.urls.static import static

urlpatterns = [
    re_path(r'^$',views.index,name='index'),
    path("admin/", admin.site.urls),
    re_path(r'^chat/', include('chat.urls', namespace='chat')),
    re_path(r'^main_app/',include('main_app.urls')),
    # re_path(r'^logout/$', views.user_logout, name='logout'),
] + static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
 