/**
 * Object to filter, create and manage the clusters from the centroids and the full data.
 *
 * <AUTHOR> <PERSON>
 * @version 1.0
 */
machineCouch.ClusterManager = function() {
	// Properties.
	/**
	 * Initialize the clusters in the input data object.
	 */
	this.init = function(gl) {
		var clusterModels;               // Cluster models array.
		var clustersData;                // The ids and coordenates of all the clusters.

		try {
			clusterModels = this.createClusterModels();
			clustersData = this.getClustersData();
			
			if (clusterModels.length > 0 && clustersData.length > 0) {
				this.filterClustersData(clustersData, clusterModels);
				this.populateClusters(gl, clusterModels);
			}
		} catch (exp) {
			console.log(exp);
		}

		return clusterModels;
	};

	/**
	 * Create the array of clusters models from the input data centroids array.
	 */
	this.createClusterModels = function() {
		var clusterModels = [];   // Cluster models array.
		var clusterModel;         // Cluster model object.
		var inputData = machineCouch.InputData;
		var i;

		for (i in inputData.clusters.centroids) {
			clusterModel = new machineCouch.model.ClusterModel(i);

			clusterModel.centroid = inputData.clusters.centroids[i];
			clusterModels.push(clusterModel);
		}

		return clusterModels;
	};

	/**
	 * Populate the Ids and coordinate data into an float32 array.
	 */
	this.getClustersData = function() {
		var clustersData;
		var clustersDomData;
		var splitData;
		var inputData = machineCouch.InputData;
		var length;
		var i;

		if (inputData.clusters.data === undefined) {
			clustersDomData = document.getElementById(inputData.clusters.name);

			if (!clustersDomData) {
				throw new Error("Failed to get cluster with ID " + inputData.clusters.name);
			}

			splitData = clustersDomData.innerHTML.split(",");
			length = splitData.length;
			clustersData = new Float32Array(length);

			for (i = 0; i < length; i++) {
				clustersData[i] = parseFloat(splitData[i].trim());
			}
		} else {
			clustersData = inputData.clusters.data;
		}

		return clustersData;
	};

	/**
	 * Arrange and insert the array of float32 into the the nearest cluster buffer data.
	 */
	this.filterClustersData = function(clustersData, clusterModels) {
		var nearestClusters, nearestCluster;
		var distances;
		var point;
		var length = clustersData.length;
		var i;

		for (i = 0; i < length; ) {
			point = [];
			point.push(clustersData[i++]); // Id
			point.push(clustersData[i++]); // X
			point.push(clustersData[i++]); // Y
			point.push(clustersData[i++]); // Z
			point.push(0.0); // R
			point.push(0.0); // G
			point.push(0.0); // B
			
			nearestClusters = this.getThe3NearestClusters(point, clusterModels);
			nearestCluster = nearestClusters[0].clusterModel;
			this.setPointColor(point, nearestClusters);
			nearestCluster.pointsData.push(point);
		}
	};

	this.populateClusters = function(gl, clusterModels) {
		var clusterModel;
		var vertices;
		var pointsData;
		var point;
		var i, j, count;

		for (i in clusterModels) {
			count = 0;
			clusterModel = clusterModels[i];
			pointsData = clusterModel.pointsData;

			if (pointsData.length > 0) {
				clusterModel.numberVertices = pointsData.length;
				vertices = new Float32Array(pointsData.length * 7);
				clusterModel.vertices = vertices;

				for (j in pointsData) {
					point = pointsData[j];
					vertices[count++] = j;
					vertices[count++] = point[1];
					vertices[count++] = point[2];
					vertices[count++] = point[3];
					vertices[count++] = point[4];
					vertices[count++] = point[5];
					vertices[count++] = point[6];
				}

				clusterModel.createBuffers(gl);
				clusterModel.isLoaded = true;
			} else {
				clusterModel.isLoaded = false;
			}
		}
	};
	
	/**
	 * Get the 3 nearest clusters to the point. 
	 *
	 * @param point			The point in the space to be measured.
	 * @param clusterModels	The array of cluster models.
	 */
	this.getThe3NearestClusters = function(point, clusterModels) {
		var clustersColorBlending = machineCouch.InputData.clusters.clustersColorBlending;
		var nearestClusters = [];
		var distances = [];
		var minDistance, minIndex;
		var i, j;

		for (i in clusterModels) {
			distances.push(this.getDistance(point, clusterModels[i].centroid.position));
		}
		
		for (i = 0; i < distances.length; ) {
			if (distances[i] == -1.0) {
				++i;
				continue;
			}
			
			minDistance = distances[i];
			minIndex = i;

			for (j = 1; j < distances.length; ++j) {
				if (distances[j] == -1.0) {
					continue;
				}

				if (minDistance > distances[j]) {
					minDistance = distances[j];
					minIndex = j;
				}
			}

			nearestClusters.push({clusterModel: clusterModels[minIndex], distance: minDistance});
			distances[minIndex] = -1.0;

			if (nearestClusters.length == 3 || clustersColorBlending === false) {
				break;
			}
			
			i = 0;
		}

		return nearestClusters;
	};
	
	/**
	 * Get the nearest cluster model from the point. 
	 *
	 * @param point	      The space point. It started with the Id.
	 * @param centroid     The centroid point.
	 */
	this.getDistance = function(point, centroid) {
		var deltaX = point[1] - centroid[0];
		var deltaY = point[2] - centroid[1];
		var deltaZ = point[3] - centroid[2];

		return Math.sqrt(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ);
	};
	
	/**
	 * Set the color of the point based on the weights of the 3 closest cluster.
	 *
	 * @param point			The point in the space to be measured.
	 * @param nearestClusters	At most the 3 closest clusters to the point.
	 */
	this.setPointColor = function(point, nearestClusters) {
		var flatColor = machineCouch.InputData.clusters.flatColor;
		var clustersColorBlending = machineCouch.InputData.clusters.clustersColorBlending;
		var influenceRadius = machineCouch.InputData.clusters.influenceRadius;
		var minColorPercentage = machineCouch.InputData.clusters.minColorPercentage;
		var gradientThresholdRadius = machineCouch.InputData.clusters.gradientThresholdRadius;
		var clusterModel;
		var centroidColor;
		var distance;
		var percentage;
		var weights = (clustersColorBlending === true) ? this.calculateWeights(nearestClusters) : null;
		var i;
		
		for (i in nearestClusters) {
			clusterModel = nearestClusters[i].clusterModel;
			distance = nearestClusters[i].distance;
			centroidColor = clusterModel.centroid.color;
			
			if (flatColor !== true) {
				percentage = 1 - ((distance * (1 - minColorPercentage)) / gradientThresholdRadius);
			
				if (percentage < minColorPercentage) {
					percentage = minColorPercentage;
				}
			} else {
				percentage = 1.0;
			}
			
			if (distance > influenceRadius && clustersColorBlending === true) {
				point[4] += centroidColor[0] * percentage * weights[i];
				point[5] += centroidColor[1] * percentage * weights[i];
				point[6] += centroidColor[2] * percentage * weights[i];
			} else {
				point[4] = centroidColor[0] * percentage;
				point[5] = centroidColor[1] * percentage;
				point[6] = centroidColor[2] * percentage;
				break;
			}
		}
	};

	/**
	 * Calculate the weight of each cluster based on its distance..
	 *
	 * @param nearestClusters	At most the 3 closest clusters to the point.
	 */
	this.calculateWeights = function(nearestClusters) {
		var initialWeight = 1.0 / nearestClusters.length;
		var weights = [];
		var minDistance = nearestClusters[0].distance;
		var weightDiff;
		var i;
		
		for (i in nearestClusters) {
			weights.push(initialWeight);
		}
		
		for (i = 1; i < nearestClusters.length; ++i) {
			weightDiff = 1 - minDistance / nearestClusters[i].distance;
			weights[0] += initialWeight * weightDiff;
			weights[i] -= initialWeight * weightDiff;
		}

		return weights;
	};
};


