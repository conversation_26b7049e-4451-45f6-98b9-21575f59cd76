/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-wizard
 * @version 2.4.0
 */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("@form-validation/core"),require("@form-validation/plugin-excluded")):"function"==typeof define&&define.amd?define(["@form-validation/core","@form-validation/plugin-excluded"],e):((t="undefined"!=typeof globalThis?globalThis:t||self).FormValidation=t.FormValidation||{},t.FormValidation.plugins=t.FormValidation.plugins||{},t.FormValidation.plugins.Wizard=e(t.FormValidation,t.FormValidation.plugins))}(this,(function(t,e){"use strict";var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},n(t,e)};var i=t.utils.classSet;return function(t){function s(e){var n=t.call(this,e)||this;return n.currentStep=0,n.numSteps=0,n.stepIndexes=[],n.opts=Object.assign({},{activeStepClass:"fv-plugins-wizard--active",onStepActive:function(){},onStepInvalid:function(){},onStepValid:function(){},onValid:function(){},stepClass:"fv-plugins-wizard--step"},e),n.prevStepHandler=n.onClickPrev.bind(n),n.nextStepHandler=n.onClickNext.bind(n),n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}(s,t),s.prototype.install=function(){var t,n=this;this.core.registerPlugin(s.EXCLUDED_PLUGIN,this.opts.isFieldExcluded?new e.Excluded({excluded:this.opts.isFieldExcluded}):new e.Excluded);var o=this.core.getFormElement();this.steps=[].slice.call(o.querySelectorAll(this.opts.stepSelector)),this.numSteps=this.steps.length,this.steps.forEach((function(t){var e;i(t,((e={})[n.opts.stepClass]=!0,e))})),i(this.steps[0],((t={})[this.opts.activeStepClass]=!0,t)),this.stepIndexes=Array(this.numSteps).fill(0).map((function(t,e){return e})),this.prevButton="string"==typeof this.opts.prevButton?"#"===this.opts.prevButton.substring(0,1)?document.getElementById(this.opts.prevButton.substring(1)):o.querySelector(this.opts.prevButton):this.opts.prevButton,this.nextButton="string"==typeof this.opts.nextButton?"#"===this.opts.nextButton.substring(0,1)?document.getElementById(this.opts.nextButton.substring(1)):o.querySelector(this.opts.nextButton):this.opts.nextButton,this.prevButton.addEventListener("click",this.prevStepHandler),this.nextButton.addEventListener("click",this.nextStepHandler)},s.prototype.uninstall=function(){this.core.deregisterPlugin(s.EXCLUDED_PLUGIN),this.prevButton.removeEventListener("click",this.prevStepHandler),this.nextButton.removeEventListener("click",this.nextStepHandler),this.stepIndexes.length=0},s.prototype.getCurrentStep=function(){return this.currentStep},s.prototype.goToPrevStep=function(){var t=this;if(this.isEnabled){var e=this.currentStep-1;if(!(e<0)){var n=this.opts.isStepSkipped?this.stepIndexes.slice(0,this.currentStep).reverse().find((function(e,n){return!t.opts.isStepSkipped({currentStep:t.currentStep,numSteps:t.numSteps,targetStep:e})})):e;this.goToStep(n),this.onStepActive()}}},s.prototype.goToNextStep=function(){var t=this;this.isEnabled&&this.core.validate().then((function(e){if("Valid"===e){var n=t.currentStep+1;if(n>=t.numSteps)t.currentStep=t.numSteps-1;else n=t.opts.isStepSkipped?t.stepIndexes.slice(n,t.numSteps).find((function(e,n){return!t.opts.isStepSkipped({currentStep:t.currentStep,numSteps:t.numSteps,targetStep:e})})):n,t.goToStep(n);t.onStepActive(),t.onStepValid(),n===t.numSteps&&t.onValid()}else"Invalid"===e&&t.onStepInvalid()}))},s.prototype.goToStep=function(t){var e,n;this.isEnabled&&(i(this.steps[this.currentStep],((e={})[this.opts.activeStepClass]=!1,e)),i(this.steps[t],((n={})[this.opts.activeStepClass]=!0,n)),this.currentStep=t)},s.prototype.onEnabled=function(){this.core.enablePlugin(s.EXCLUDED_PLUGIN)},s.prototype.onDisabled=function(){this.core.disablePlugin(s.EXCLUDED_PLUGIN)},s.prototype.onClickPrev=function(){this.goToPrevStep()},s.prototype.onClickNext=function(){this.goToNextStep()},s.prototype.onStepActive=function(){var t={numSteps:this.numSteps,step:this.currentStep};this.core.emit("plugins.wizard.step.active",t),this.opts.onStepActive(t)},s.prototype.onStepValid=function(){var t={numSteps:this.numSteps,step:this.currentStep};this.core.emit("plugins.wizard.step.valid",t),this.opts.onStepValid(t)},s.prototype.onStepInvalid=function(){var t={numSteps:this.numSteps,step:this.currentStep};this.core.emit("plugins.wizard.step.invalid",t),this.opts.onStepInvalid(t)},s.prototype.onValid=function(){var t={numSteps:this.numSteps};this.core.emit("plugins.wizard.valid",t),this.opts.onValid(t)},s.EXCLUDED_PLUGIN="___wizardExcluded",s}(t.Plugin)}));
