/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/validator-id
 * @version 2.4.0
 */

!function(a,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("@form-validation/core")):"function"==typeof define&&define.amd?define(["@form-validation/core"],t):((a="undefined"!=typeof globalThis?globalThis:a||self).FormValidation=a.FormValidation||{},a.FormValidation.validators=a.FormValidation.validators||{},a.FormValidation.validators.id=t(a.FormValidation))}(this,(function(a){"use strict";function t(a,t){if(!/^\d{13}$/.test(a))return!1;var r=parseInt(a.substr(0,2),10),e=parseInt(a.substr(2,2),10),s=parseInt(a.substr(7,2),10),n=parseInt(a.substr(12,1),10);if(r>31||e>12)return!1;for(var i=0,u=0;u<6;u++)i+=(7-u)*(parseInt(a.charAt(u),10)+parseInt(a.charAt(u+6),10));if(10!==(i=11-i%11)&&11!==i||(i=0),i!==n)return!1;switch(t.toUpperCase()){case"BA":return 10<=s&&s<=19;case"MK":return 41<=s&&s<=49;case"ME":return 20<=s&&s<=29;case"RS":return 70<=s&&s<=99;case"SI":return 50<=s&&s<=59;default:return!0}}var r=a.utils.isValidDate;var e=a.utils.isValidDate;var s=a.utils.isValidDate;function n(a){if(!/^\d{9,10}$/.test(a))return{meta:{},valid:!1};var t=1900+parseInt(a.substr(0,2),10),r=parseInt(a.substr(2,2),10)%50%20,e=parseInt(a.substr(4,2),10);if(9===a.length){if(t>=1980&&(t-=100),t>1953)return{meta:{},valid:!1}}else t<1954&&(t+=100);if(!s(t,r,e))return{meta:{},valid:!1};if(10===a.length){var n=parseInt(a.substr(0,9),10)%11;return t<1985&&(n%=10),{meta:{},valid:"".concat(n)===a.substr(9,1)}}return{meta:{},valid:!0}}var i=a.utils.isValidDate;var u=a.utils.isValidDate;var c=a.algorithms.mod11And10;var l=a.algorithms.verhoeff;var v=a.algorithms.luhn;var d=a.utils.isValidDate;var f=a.utils.isValidDate;var o=a.utils.isValidDate;function A(a){if(!/^[0-9]{11}$/.test(a))return{meta:{},valid:!1};var t=parseInt(a.charAt(0),10),r=parseInt(a.substr(1,2),10),e=parseInt(a.substr(3,2),10),s=parseInt(a.substr(5,2),10);if(!o(r=100*(t%2==0?17+t/2:17+(t+1)/2)+r,e,s,!0))return{meta:{},valid:!1};var n,i=[1,2,3,4,5,6,7,8,9,1],u=0;for(n=0;n<10;n++)u+=parseInt(a.charAt(n),10)*i[n];if(10!==(u%=11))return{meta:{},valid:"".concat(u)===a.charAt(10)};for(u=0,i=[3,4,5,6,7,8,9,1,2,3],n=0;n<10;n++)u+=parseInt(a.charAt(n),10)*i[n];return 10===(u%=11)&&(u=0),{meta:{},valid:"".concat(u)===a.charAt(10)}}var p=a.utils.isValidDate;var b=a.utils.isValidDate;var m=a.utils.isValidDate;var I=a.utils.isValidDate;var h=a.algorithms.luhn,O=a.utils.isValidDate;var C=a.algorithms.luhn,k=a.utils.isValidDate;var g=a.utils.format,E=a.utils.removeUndefined;return function(){var a=["AR","BA","BG","BR","CH","CL","CN","CO","CZ","DK","EE","ES","FI","FR","HK","HR","ID","IE","IL","IS","KR","LT","LV","ME","MK","MX","MY","NL","NO","PE","PL","RO","RS","SE","SI","SK","SM","TH","TR","TW","UY","ZA"];return{validate:function(s){if(""===s.value)return{valid:!0};var o=Object.assign({},{message:""},E(s.options)),$=s.value.substr(0,2);if($="function"==typeof o.country?o.country.call(this):o.country,-1===a.indexOf($))return{valid:!0};var K,M,V={meta:{},valid:!0};switch($.toLowerCase()){case"ar":K=s.value,M=K.replace(/\./g,""),V={meta:{},valid:/^\d{7,8}$/.test(M)};break;case"ba":V=function(a){return{meta:{},valid:t(a,"BA")}}(s.value);break;case"bg":V=function(a){if(!/^\d{10}$/.test(a)&&!/^\d{6}\s\d{3}\s\d{1}$/.test(a))return{meta:{},valid:!1};var t=a.replace(/\s/g,""),e=parseInt(t.substr(0,2),10)+1900,s=parseInt(t.substr(2,2),10),n=parseInt(t.substr(4,2),10);if(s>40?(e+=100,s-=40):s>20&&(e-=100,s-=20),!r(e,s,n))return{meta:{},valid:!1};for(var i=0,u=[2,4,8,5,10,9,7,3,6],c=0;c<9;c++)i+=parseInt(t.charAt(c),10)*u[c];return{meta:{},valid:"".concat(i=i%11%10)===t.substr(9,1)}}(s.value);break;case"br":V=function(a){var t=a.replace(/\D/g,"");if(!/^\d{11}$/.test(t)||/^1{11}|2{11}|3{11}|4{11}|5{11}|6{11}|7{11}|8{11}|9{11}|0{11}$/.test(t))return{meta:{},valid:!1};var r,e=0;for(r=0;r<9;r++)e+=(10-r)*parseInt(t.charAt(r),10);if(10!=(e=11-e%11)&&11!==e||(e=0),"".concat(e)!==t.charAt(9))return{meta:{},valid:!1};var s=0;for(r=0;r<10;r++)s+=(11-r)*parseInt(t.charAt(r),10);return 10!=(s=11-s%11)&&11!==s||(s=0),{meta:{},valid:"".concat(s)===t.charAt(10)}}(s.value);break;case"ch":V=function(a){if(!/^756[.]{0,1}[0-9]{4}[.]{0,1}[0-9]{4}[.]{0,1}[0-9]{2}$/.test(a))return{meta:{},valid:!1};for(var t=a.replace(/\D/g,"").substr(3),r=t.length,e=8===r?[3,1]:[1,3],s=0,n=0;n<r-1;n++)s+=parseInt(t.charAt(n),10)*e[n%2];return{meta:{},valid:"".concat(s=10-s%10)===t.charAt(r-1)}}(s.value);break;case"cl":V=function(a){if(!/^\d{7,8}[-]{0,1}[0-9K]$/i.test(a))return{meta:{},valid:!1};for(var t=a.replace(/-/g,"");t.length<9;)t="0".concat(t);for(var r=[3,2,7,6,5,4,3,2],e=0,s=0;s<8;s++)e+=parseInt(t.charAt(s),10)*r[s];var n="".concat(e=11-e%11);return 11===e?n="0":10===e&&(n="K"),{meta:{},valid:n===t.charAt(8).toUpperCase()}}(s.value);break;case"cn":V=function(a){var t=a.trim();if(!/^\d{15}$/.test(t)&&!/^\d{17}[\dXx]{1}$/.test(t))return{meta:{},valid:!1};var r={11:{0:[0],1:[[0,9],[11,17]],2:[0,28,29]},12:{0:[0],1:[[0,16]],2:[0,21,23,25]},13:{0:[0],1:[[0,5],7,8,21,[23,33],[81,85]],2:[[0,5],[7,9],[23,25],27,29,30,81,83],3:[[0,4],[21,24]],4:[[0,4],6,21,[23,35],81],5:[[0,3],[21,35],81,82],6:[[0,4],[21,38],[81,84]],7:[[0,3],5,6,[21,33]],8:[[0,4],[21,28]],9:[[0,3],[21,30],[81,84]],10:[[0,3],[22,26],28,81,82],11:[[0,2],[21,28],81,82]},14:{0:[0],1:[0,1,[5,10],[21,23],81],2:[[0,3],11,12,[21,27]],3:[[0,3],11,21,22],4:[[0,2],11,21,[23,31],81],5:[[0,2],21,22,24,25,81],6:[[0,3],[21,24]],7:[[0,2],[21,29],81],8:[[0,2],[21,30],81,82],9:[[0,2],[21,32],81],10:[[0,2],[21,34],81,82],11:[[0,2],[21,30],81,82],23:[[0,3],22,23,[25,30],32,33]},15:{0:[0],1:[[0,5],[21,25]],2:[[0,7],[21,23]],3:[[0,4]],4:[[0,4],[21,26],[28,30]],5:[[0,2],[21,26],81],6:[[0,2],[21,27]],7:[[0,3],[21,27],[81,85]],8:[[0,2],[21,26]],9:[[0,2],[21,29],81],22:[[0,2],[21,24]],25:[[0,2],[22,31]],26:[[0,2],[24,27],[29,32],34],28:[0,1,[22,27]],29:[0,[21,23]]},21:{0:[0],1:[[0,6],[11,14],[22,24],81],2:[[0,4],[11,13],24,[81,83]],3:[[0,4],11,21,23,81],4:[[0,4],11,[21,23]],5:[[0,5],21,22],6:[[0,4],24,81,82],7:[[0,3],11,26,27,81,82],8:[[0,4],11,81,82],9:[[0,5],11,21,22],10:[[0,5],11,21,81],11:[[0,3],21,22],12:[[0,2],4,21,23,24,81,82],13:[[0,3],21,22,24,81,82],14:[[0,4],21,22,81]},22:{0:[0],1:[[0,6],12,22,[81,83]],2:[[0,4],11,21,[81,84]],3:[[0,3],22,23,81,82],4:[[0,3],21,22],5:[[0,3],21,23,24,81,82],6:[[0,2],4,5,[21,23],25,81],7:[[0,2],[21,24],81],8:[[0,2],21,22,81,82],24:[[0,6],24,26]},23:{0:[0],1:[[0,12],21,[23,29],[81,84]],2:[[0,8],21,[23,25],27,[29,31],81],3:[[0,7],21,81,82],4:[[0,7],21,22],5:[[0,3],5,6,[21,24]],6:[[0,6],[21,24]],7:[[0,16],22,81],8:[[0,5],11,22,26,28,33,81,82],9:[[0,4],21],10:[[0,5],24,25,81,[83,85]],11:[[0,2],21,23,24,81,82],12:[[0,2],[21,26],[81,83]],27:[[0,4],[21,23]]},31:{0:[0],1:[0,1,[3,10],[12,20]],2:[0,30]},32:{0:[0],1:[[0,7],11,[13,18],24,25],2:[[0,6],11,81,82],3:[[0,5],11,12,[21,24],81,82],4:[[0,2],4,5,11,12,81,82],5:[[0,9],[81,85]],6:[[0,2],11,12,21,23,[81,84]],7:[0,1,3,5,6,[21,24]],8:[[0,4],11,26,[29,31]],9:[[0,3],[21,25],28,81,82],10:[[0,3],11,12,23,81,84,88],11:[[0,2],11,12,[81,83]],12:[[0,4],[81,84]],13:[[0,2],11,[21,24]]},33:{0:[0],1:[[0,6],[8,10],22,27,82,83,85],2:[0,1,[3,6],11,12,25,26,[81,83]],3:[[0,4],22,24,[26,29],81,82],4:[[0,2],11,21,24,[81,83]],5:[[0,3],[21,23]],6:[[0,2],21,24,[81,83]],7:[[0,3],23,26,27,[81,84]],8:[[0,3],22,24,25,81],9:[[0,3],21,22],10:[[0,4],[21,24],81,82],11:[[0,2],[21,27],81]},34:{0:[0],1:[[0,4],11,[21,24],81],2:[[0,4],7,8,[21,23],25],3:[[0,4],11,[21,23]],4:[[0,6],21],5:[[0,4],6,[21,23]],6:[[0,4],21],7:[[0,3],11,21],8:[[0,3],11,[22,28],81],10:[[0,4],[21,24]],11:[[0,3],22,[24,26],81,82],12:[[0,4],21,22,25,26,82],13:[[0,2],[21,24]],14:[[0,2],[21,24]],15:[[0,3],[21,25]],16:[[0,2],[21,23]],17:[[0,2],[21,23]],18:[[0,2],[21,25],81]},35:{0:[0],1:[[0,5],11,[21,25],28,81,82],2:[[0,6],[11,13]],3:[[0,5],22],4:[[0,3],21,[23,30],81],5:[[0,5],21,[24,27],[81,83]],6:[[0,3],[22,29],81],7:[[0,2],[21,25],[81,84]],8:[[0,2],[21,25],81],9:[[0,2],[21,26],81,82]},36:{0:[0],1:[[0,5],11,[21,24]],2:[[0,3],22,81],3:[[0,2],13,[21,23]],4:[[0,3],21,[23,30],81,82],5:[[0,2],21],6:[[0,2],22,81],7:[[0,2],[21,35],81,82],8:[[0,3],[21,30],81],9:[[0,2],[21,26],[81,83]],10:[[0,2],[21,30]],11:[[0,2],[21,30],81]},37:{0:[0],1:[[0,5],12,13,[24,26],81],2:[[0,3],5,[11,14],[81,85]],3:[[0,6],[21,23]],4:[[0,6],81],5:[[0,3],[21,23]],6:[[0,2],[11,13],34,[81,87]],7:[[0,5],24,25,[81,86]],8:[[0,2],11,[26,32],[81,83]],9:[[0,3],11,21,23,82,83],10:[[0,2],[81,83]],11:[[0,3],21,22],12:[[0,3]],13:[[0,2],11,12,[21,29]],14:[[0,2],[21,28],81,82],15:[[0,2],[21,26],81],16:[[0,2],[21,26]],17:[[0,2],[21,28]]},41:{0:[0],1:[[0,6],8,22,[81,85]],2:[[0,5],11,[21,25]],3:[[0,7],11,[22,29],81],4:[[0,4],11,[21,23],25,81,82],5:[[0,3],5,6,22,23,26,27,81],6:[[0,3],11,21,22],7:[[0,4],11,21,[24,28],81,82],8:[[0,4],11,[21,23],25,[81,83]],9:[[0,2],22,23,[26,28]],10:[[0,2],[23,25],81,82],11:[[0,4],[21,23]],12:[[0,2],21,22,24,81,82],13:[[0,3],[21,30],81],14:[[0,3],[21,26],81],15:[[0,3],[21,28]],16:[[0,2],[21,28],81],17:[[0,2],[21,29]],90:[0,1]},42:{0:[0],1:[[0,7],[11,17]],2:[[0,5],22,81],3:[[0,3],[21,25],81],5:[[0,6],[25,29],[81,83]],6:[[0,2],6,7,[24,26],[82,84]],7:[[0,4]],8:[[0,2],4,21,22,81],9:[[0,2],[21,23],81,82,84],10:[[0,3],[22,24],81,83,87],11:[[0,2],[21,27],81,82],12:[[0,2],[21,24],81],13:[[0,3],21,81],28:[[0,2],22,23,[25,28]],90:[0,[4,6],21]},43:{0:[0],1:[[0,5],11,12,21,22,24,81],2:[[0,4],11,21,[23,25],81],3:[[0,2],4,21,81,82],4:[0,1,[5,8],12,[21,24],26,81,82],5:[[0,3],11,[21,25],[27,29],81],6:[[0,3],11,21,23,24,26,81,82],7:[[0,3],[21,26],81],8:[[0,2],11,21,22],9:[[0,3],[21,23],81],10:[[0,3],[21,28],81],11:[[0,3],[21,29]],12:[[0,2],[21,30],81],13:[[0,2],21,22,81,82],31:[0,1,[22,27],30]},44:{0:[0],1:[[0,7],[11,16],83,84],2:[[0,5],21,22,24,29,32,33,81,82],3:[0,1,[3,8]],4:[[0,4]],5:[0,1,[6,15],23,82,83],6:[0,1,[4,8]],7:[0,1,[3,5],81,[83,85]],8:[[0,4],11,23,25,[81,83]],9:[[0,3],23,[81,83]],12:[[0,3],[23,26],83,84],13:[[0,3],[22,24],81],14:[[0,2],[21,24],26,27,81],15:[[0,2],21,23,81],16:[[0,2],[21,25]],17:[[0,2],21,23,81],18:[[0,3],21,23,[25,27],81,82],19:[0],20:[0],51:[[0,3],21,22],52:[[0,3],21,22,24,81],53:[[0,2],[21,23],81]},45:{0:[0],1:[[0,9],[21,27]],2:[[0,5],[21,26]],3:[[0,5],11,12,[21,32]],4:[0,1,[3,6],11,[21,23],81],5:[[0,3],12,21],6:[[0,3],21,81],7:[[0,3],21,22],8:[[0,4],21,81],9:[[0,3],[21,24],81],10:[[0,2],[21,31]],11:[[0,2],[21,23]],12:[[0,2],[21,29],81],13:[[0,2],[21,24],81],14:[[0,2],[21,25],81]},46:{0:[0],1:[0,1,[5,8]],2:[0,1],3:[0,[21,23]],90:[[0,3],[5,7],[21,39]]},50:{0:[0],1:[[0,19]],2:[0,[22,38],[40,43]],3:[0,[81,84]]},51:{0:[0],1:[0,1,[4,8],[12,15],[21,24],29,31,32,[81,84]],3:[[0,4],11,21,22],4:[[0,3],11,21,22],5:[[0,4],21,22,24,25],6:[0,1,3,23,26,[81,83]],7:[0,1,3,4,[22,27],81],8:[[0,2],11,12,[21,24]],9:[[0,4],[21,23]],10:[[0,2],11,24,25,28],11:[[0,2],[11,13],23,24,26,29,32,33,81],13:[[0,4],[21,25],81],14:[[0,2],[21,25]],15:[[0,3],[21,29]],16:[[0,3],[21,23],81],17:[[0,3],[21,25],81],18:[[0,3],[21,27]],19:[[0,3],[21,23]],20:[[0,2],21,22,81],32:[0,[21,33]],33:[0,[21,38]],34:[0,1,[22,37]]},52:{0:[0],1:[[0,3],[11,15],[21,23],81],2:[0,1,3,21,22],3:[[0,3],[21,30],81,82],4:[[0,2],[21,25]],5:[[0,2],[21,27]],6:[[0,3],[21,28]],22:[0,1,[22,30]],23:[0,1,[22,28]],24:[0,1,[22,28]],26:[0,1,[22,36]],27:[[0,2],22,23,[25,32]]},53:{0:[0],1:[[0,3],[11,14],21,22,[24,29],81],3:[[0,2],[21,26],28,81],4:[[0,2],[21,28]],5:[[0,2],[21,24]],6:[[0,2],[21,30]],7:[[0,2],[21,24]],8:[[0,2],[21,29]],9:[[0,2],[21,27]],23:[0,1,[22,29],31],25:[[0,4],[22,32]],26:[0,1,[21,28]],27:[0,1,[22,30]],28:[0,1,22,23],29:[0,1,[22,32]],31:[0,2,3,[22,24]],34:[0,[21,23]],33:[0,21,[23,25]],35:[0,[21,28]]},54:{0:[0],1:[[0,2],[21,27]],21:[0,[21,29],32,33],22:[0,[21,29],[31,33]],23:[0,1,[22,38]],24:[0,[21,31]],25:[0,[21,27]],26:[0,[21,27]]},61:{0:[0],1:[[0,4],[11,16],22,[24,26]],2:[[0,4],22],3:[[0,4],[21,24],[26,31]],4:[[0,4],[22,31],81],5:[[0,2],[21,28],81,82],6:[[0,2],[21,32]],7:[[0,2],[21,30]],8:[[0,2],[21,31]],9:[[0,2],[21,29]],10:[[0,2],[21,26]]},62:{0:[0],1:[[0,5],11,[21,23]],2:[0,1],3:[[0,2],21],4:[[0,3],[21,23]],5:[[0,3],[21,25]],6:[[0,2],[21,23]],7:[[0,2],[21,25]],8:[[0,2],[21,26]],9:[[0,2],[21,24],81,82],10:[[0,2],[21,27]],11:[[0,2],[21,26]],12:[[0,2],[21,28]],24:[0,21,[24,29]],26:[0,21,[23,30]],29:[0,1,[21,27]],30:[0,1,[21,27]]},63:{0:[0],1:[[0,5],[21,23]],2:[0,2,[21,25]],21:[0,[21,23],[26,28]],22:[0,[21,24]],23:[0,[21,24]],25:[0,[21,25]],26:[0,[21,26]],27:[0,1,[21,26]],28:[[0,2],[21,23]]},64:{0:[0],1:[0,1,[4,6],21,22,81],2:[[0,3],5,[21,23]],3:[[0,3],[21,24],81],4:[[0,2],[21,25]],5:[[0,2],21,22]},65:{0:[0],1:[[0,9],21],2:[[0,5]],21:[0,1,22,23],22:[0,1,22,23],23:[[0,3],[23,25],27,28],28:[0,1,[22,29]],29:[0,1,[22,29]],30:[0,1,[22,24]],31:[0,1,[21,31]],32:[0,1,[21,27]],40:[0,2,3,[21,28]],42:[[0,2],21,[23,26]],43:[0,1,[21,26]],90:[[0,4]],27:[[0,2],22,23]},71:{0:[0]},81:{0:[0]},82:{0:[0]}},s=parseInt(t.substr(0,2),10),n=parseInt(t.substr(2,2),10),i=parseInt(t.substr(4,2),10);if(!r[s]||!r[s][n])return{meta:{},valid:!1};var u,c,l=!1,v=r[s][n];for(u=0;u<v.length;u++)if(Array.isArray(v[u])&&v[u][0]<=i&&i<=v[u][1]||!Array.isArray(v[u])&&i===v[u]){l=!0;break}if(!l)return{meta:{},valid:!1};c=18===t.length?t.substr(6,8):"19".concat(t.substr(6,6));var d=parseInt(c.substr(0,4),10),f=parseInt(c.substr(4,2),10),o=parseInt(c.substr(6,2),10);if(!e(d,f,o))return{meta:{},valid:!1};if(18===t.length){var A=[7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2],p=0;for(u=0;u<17;u++)p+=parseInt(t.charAt(u),10)*A[u];return p=(12-p%11)%11,{meta:{},valid:("X"!==t.charAt(17).toUpperCase()?parseInt(t.charAt(17),10):10)===p}}return{meta:{},valid:!0}}(s.value);break;case"co":V=function(a){var t=a.replace(/\./g,"").replace("-","");if(!/^\d{8,16}$/.test(t))return{meta:{},valid:!1};for(var r=t.length,e=[3,7,13,17,19,23,29,37,41,43,47,53,59,67,71],s=0,n=r-2;n>=0;n--)s+=parseInt(t.charAt(n),10)*e[n];return(s%=11)>=2&&(s=11-s),{meta:{},valid:"".concat(s)===t.substr(r-1)}}(s.value);break;case"cz":case"sk":V=n(s.value);break;case"dk":V=function(a){if(!/^[0-9]{6}[-]{0,1}[0-9]{4}$/.test(a))return{meta:{},valid:!1};var t=a.replace(/-/g,""),r=parseInt(t.substr(0,2),10),e=parseInt(t.substr(2,2),10),s=parseInt(t.substr(4,2),10);switch(!0){case-1!=="5678".indexOf(t.charAt(6))&&s>=58:s+=1800;break;case-1!=="0123".indexOf(t.charAt(6)):case-1!=="49".indexOf(t.charAt(6))&&s>=37:s+=1900;break;default:s+=2e3}return{meta:{},valid:i(s,e,r)}}(s.value);break;case"ee":case"lt":V=A(s.value);break;case"es":V=function(a){var t=/^[0-9]{8}[-]{0,1}[A-HJ-NP-TV-Z]$/.test(a),r=/^[XYZ][-]{0,1}[0-9]{7}[-]{0,1}[A-HJ-NP-TV-Z]$/.test(a),e=/^[A-HNPQS][-]{0,1}[0-9]{7}[-]{0,1}[0-9A-J]$/.test(a);if(!t&&!r&&!e)return{meta:{},valid:!1};var s,n,i=a.replace(/-/g,"");if(t||r){n="DNI";var u="XYZ".indexOf(i.charAt(0));return-1!==u&&(i=u+i.substr(1)+"",n="NIE"),{meta:{type:n},valid:(s="TRWAGMYFPDXBNJZSQVHLCKE"[(s=parseInt(i.substr(0,8),10))%23])===i.substr(8,1)}}s=i.substr(1,7),n="CIF";for(var c=i[0],l=i.substr(-1),v=0,d=0;d<s.length;d++)if(d%2!=0)v+=parseInt(s[d],10);else{var f=""+2*parseInt(s[d],10);v+=parseInt(f[0],10),2===f.length&&(v+=parseInt(f[1],10))}var o=v-10*Math.floor(v/10);return 0!==o&&(o=10-o),{meta:{type:n},valid:-1!=="KQS".indexOf(c)?l==="JABCDEFGHI"[o]:-1!=="ABEH".indexOf(c)?l===""+o:l===""+o||l==="JABCDEFGHI"[o]}}(s.value);break;case"fi":V=function(a){if(!/^[0-9]{6}[-+A][0-9]{3}[0-9ABCDEFHJKLMNPRSTUVWXY]$/.test(a))return{meta:{},valid:!1};var t=parseInt(a.substr(0,2),10),r=parseInt(a.substr(2,2),10),e=parseInt(a.substr(4,2),10);if(e={"+":1800,"-":1900,A:2e3}[a.charAt(6)]+e,!u(e,r,t))return{meta:{},valid:!1};if(parseInt(a.substr(7,3),10)<2)return{meta:{},valid:!1};var s=parseInt(a.substr(0,6)+a.substr(7,3)+"",10);return{meta:{},valid:"0123456789ABCDEFHJKLMNPRSTUVWXY".charAt(s%31)===a.charAt(10)}}(s.value);break;case"fr":V=function(a){var t=a.toUpperCase();if(!/^(1|2)\d{2}\d{2}(\d{2}|\d[A-Z]|\d{3})\d{2,3}\d{3}\d{2}$/.test(t))return{meta:{},valid:!1};var r=t.substr(5,2);switch(!0){case/^\d{2}$/.test(r):t=a;break;case"2A"===r:t="".concat(a.substr(0,5),"19").concat(a.substr(7));break;case"2B"===r:t="".concat(a.substr(0,5),"18").concat(a.substr(7));break;default:return{meta:{},valid:!1}}var e=97-parseInt(t.substr(0,13),10)%97;return{meta:{},valid:(e<10?"0".concat(e):"".concat(e))===t.substr(13)}}(s.value);break;case"hk":V=function(a){var t=a.toUpperCase();if(!/^[A-MP-Z]{1,2}[0-9]{6}[0-9A]$/.test(t))return{meta:{},valid:!1};var r="ABCDEFGHIJKLMNOPQRSTUVWXYZ",e=t.charAt(0),s=t.charAt(1),n=0,i=t;/^[A-Z]$/.test(s)?(n+=9*(10+r.indexOf(e)),n+=8*(10+r.indexOf(s)),i=t.substr(2)):(n+=324,n+=8*(10+r.indexOf(e)),i=t.substr(1));for(var u=i.length,c=0;c<u-1;c++)n+=(7-c)*parseInt(i.charAt(c),10);var l=n%11;return{meta:{},valid:(0===l?"0":11-l==10?"A":"".concat(11-l))===i.charAt(u-1)}}(s.value);break;case"hr":V=function(a){return{meta:{},valid:/^[0-9]{11}$/.test(a)&&c(a)}}(s.value);break;case"id":V=function(a){if(!/^[2-9]\d{11}$/.test(a))return{meta:{},valid:!1};var t=a.split("").map((function(a){return parseInt(a,10)}));return{meta:{},valid:l(t)}}(s.value);break;case"ie":V=function(a){if(!/^\d{7}[A-W][AHWTX]?$/.test(a))return{meta:{},valid:!1};var t=function(a){for(var t=a;t.length<7;)t="0".concat(t);for(var r="WABCDEFGHIJKLMNOPQRSTUV",e=0,s=0;s<7;s++)e+=parseInt(t.charAt(s),10)*(8-s);return e+=9*r.indexOf(t.substr(7)),r[e%23]};return{meta:{},valid:9!==a.length||"A"!==a.charAt(8)&&"H"!==a.charAt(8)?a.charAt(7)===t(a.substr(0,7)):a.charAt(7)===t(a.substr(0,7)+a.substr(8)+"")}}(s.value);break;case"il":V=function(a){return/^\d{1,9}$/.test(a)?{meta:{},valid:v(a)}:{meta:{},valid:!1}}(s.value);break;case"is":V=function(a){if(!/^[0-9]{6}[-]{0,1}[0-9]{4}$/.test(a))return{meta:{},valid:!1};var t=a.replace(/-/g,""),r=parseInt(t.substr(0,2),10),e=parseInt(t.substr(2,2),10),s=parseInt(t.substr(4,2),10),n=parseInt(t.charAt(9),10);if(!d(s=9===n?1900+s:100*(20+n)+s,e,r,!0))return{meta:{},valid:!1};for(var i=[3,2,7,6,5,4,3,2],u=0,c=0;c<8;c++)u+=parseInt(t.charAt(c),10)*i[c];return{meta:{},valid:"".concat(u=11-u%11)===t.charAt(8)}}(s.value);break;case"kr":V=function(a){var t=a.replace("-","");if(!/^\d{13}$/.test(t))return{meta:{},valid:!1};var r=t.charAt(6),e=parseInt(t.substr(0,2),10),s=parseInt(t.substr(2,2),10),n=parseInt(t.substr(4,2),10);switch(r){case"1":case"2":case"5":case"6":e+=1900;break;case"3":case"4":case"7":case"8":e+=2e3;break;default:e+=1800}if(!f(e,s,n))return{meta:{},valid:!1};for(var i=[2,3,4,5,6,7,8,9,2,3,4,5],u=t.length,c=0,l=0;l<u-1;l++)c+=i[l]*parseInt(t.charAt(l),10);return{meta:{},valid:"".concat((11-c%11)%10)===t.charAt(u-1)}}(s.value);break;case"lv":V=function(a){if(!/^[0-9]{6}[-]{0,1}[0-9]{5}$/.test(a))return{meta:{},valid:!1};var t=a.replace(/\D/g,""),r=parseInt(t.substr(0,2),10),e=parseInt(t.substr(2,2),10),s=parseInt(t.substr(4,2),10);if(s=s+1800+100*parseInt(t.charAt(6),10),!p(s,e,r,!0))return{meta:{},valid:!1};for(var n=0,i=[10,5,8,4,2,1,6,3,7,9],u=0;u<10;u++)n+=parseInt(t.charAt(u),10)*i[u];return{meta:{},valid:"".concat(n=(n+1)%11%10)===t.charAt(10)}}(s.value);break;case"me":V=function(a){return{meta:{},valid:t(a,"ME")}}(s.value);break;case"mk":V=function(a){return{meta:{},valid:t(a,"MK")}}(s.value);break;case"mx":V=function(a){var t=a.toUpperCase();if(!/^[A-Z]{4}\d{6}[A-Z]{6}[0-9A-Z]\d$/.test(t))return{meta:{},valid:!1};var r=t.substr(0,4);if(["BACA","BAKA","BUEI","BUEY","CACA","CACO","CAGA","CAGO","CAKA","CAKO","COGE","COGI","COJA","COJE","COJI","COJO","COLA","CULO","FALO","FETO","GETA","GUEI","GUEY","JETA","JOTO","KACA","KACO","KAGA","KAGO","KAKA","KAKO","KOGE","KOGI","KOJA","KOJE","KOJI","KOJO","KOLA","KULO","LILO","LOCA","LOCO","LOKA","LOKO","MAME","MAMO","MEAR","MEAS","MEON","MIAR","MION","MOCO","MOKO","MULA","MULO","NACA","NACO","PEDA","PEDO","PENE","PIPI","PITO","POPO","PUTA","PUTO","QULO","RATA","ROBA","ROBE","ROBO","RUIN","SENO","TETA","VACA","VAGA","VAGO","VAKA","VUEI","VUEY","WUEI","WUEY"].indexOf(r)>=0)return{meta:{},valid:!1};var e=parseInt(t.substr(4,2),10),s=parseInt(t.substr(6,2),10),n=parseInt(t.substr(6,2),10);if(/^[0-9]$/.test(t.charAt(16))?e+=1900:e+=2e3,!b(e,s,n))return{meta:{},valid:!1};var i=t.charAt(10);if("H"!==i&&"M"!==i)return{meta:{},valid:!1};var u=t.substr(11,2);if(-1===["AS","BC","BS","CC","CH","CL","CM","CS","DF","DG","GR","GT","HG","JC","MC","MN","MS","NE","NL","NT","OC","PL","QR","QT","SL","SP","SR","TC","TL","TS","VZ","YN","ZS"].indexOf(u))return{meta:{},valid:!1};for(var c=0,l=t.length,v=0;v<l-1;v++)c+=(18-v)*"0123456789ABCDEFGHIJKLMN&OPQRSTUVWXYZ".indexOf(t.charAt(v));return{meta:{},valid:"".concat(c=(10-c%10)%10)===t.charAt(l-1)}}(s.value);break;case"my":V=function(a){if(!/^\d{12}$/.test(a))return{meta:{},valid:!1};var t=parseInt(a.substr(0,2),10),r=parseInt(a.substr(2,2),10),e=parseInt(a.substr(4,2),10);if(!m(t+1900,r,e)&&!m(t+2e3,r,e))return{meta:{},valid:!1};var s=a.substr(6,2);return{meta:{},valid:-1===["17","18","19","20","69","70","73","80","81","94","95","96","97"].indexOf(s)}}(s.value);break;case"nl":V=function(a){if(a.length<8)return{meta:{},valid:!1};var t=a;if(8===t.length&&(t="0".concat(t)),!/^[0-9]{4}[.]{0,1}[0-9]{2}[.]{0,1}[0-9]{3}$/.test(t))return{meta:{},valid:!1};if(t=t.replace(/\./g,""),0===parseInt(t,10))return{meta:{},valid:!1};for(var r=0,e=t.length,s=0;s<e-1;s++)r+=(9-s)*parseInt(t.charAt(s),10);return 10==(r%=11)&&(r=0),{meta:{},valid:"".concat(r)===t.charAt(e-1)}}(s.value);break;case"no":V=function(a){return/^\d{11}$/.test(a)?{meta:{},valid:"".concat(function(a){for(var t=[3,7,6,1,8,9,4,5,2],r=0,e=0;e<9;e++)r+=t[e]*parseInt(a.charAt(e),10);return 11-r%11}(a))===a.substr(-2,1)&&"".concat(function(a){for(var t=[5,4,3,2,7,6,5,4,3,2],r=0,e=0;e<10;e++)r+=t[e]*parseInt(a.charAt(e),10);return 11-r%11}(a))===a.substr(-1)}:{meta:{},valid:!1}}(s.value);break;case"pe":V=function(a){if(!/^\d{8}[0-9A-Z]*$/.test(a))return{meta:{},valid:!1};if(8===a.length)return{meta:{},valid:!0};for(var t=[3,2,7,6,5,4,3,2],r=0,e=0;e<8;e++)r+=t[e]*parseInt(a.charAt(e),10);var s=r%11,n=[6,5,4,3,2,1,1,0,9,8,7][s],i="KJIHGFEDCBA".charAt(s);return{meta:{},valid:a.charAt(8)==="".concat(n)||a.charAt(8)===i}}(s.value);break;case"pl":V=function(a){if(!/^[0-9]{11}$/.test(a))return{meta:{},valid:!1};for(var t=0,r=a.length,e=[1,3,7,9,1,3,7,9,1,3,7],s=0;s<r-1;s++)t+=e[s]*parseInt(a.charAt(s),10);return 0==(t%=10)&&(t=10),{meta:{},valid:"".concat(t=10-t)===a.charAt(r-1)}}(s.value);break;case"ro":V=function(a){if(!/^[0-9]{13}$/.test(a))return{meta:{},valid:!1};var t=parseInt(a.charAt(0),10);if(0===t||7===t||8===t)return{meta:{},valid:!1};var r=parseInt(a.substr(1,2),10),e=parseInt(a.substr(3,2),10),s=parseInt(a.substr(5,2),10);if(s>31&&e>12)return{meta:{},valid:!1};if(9!==t&&!I(r={1:1900,2:1900,3:1800,4:1800,5:2e3,6:2e3}[t+""]+r,e,s))return{meta:{},valid:!1};for(var n=0,i=[2,7,9,1,4,6,3,5,8,2,7,9],u=a.length,c=0;c<u-1;c++)n+=parseInt(a.charAt(c),10)*i[c];return 10==(n%=11)&&(n=1),{meta:{},valid:"".concat(n)===a.charAt(u-1)}}(s.value);break;case"rs":V=function(a){return{meta:{},valid:t(a,"RS")}}(s.value);break;case"se":V=function(a){if(!/^[0-9]{10}$/.test(a)&&!/^[0-9]{6}[-|+][0-9]{4}$/.test(a))return{meta:{},valid:!1};var t=a.replace(/[^0-9]/g,""),r=parseInt(t.substr(0,2),10)+1900,e=parseInt(t.substr(2,2),10),s=parseInt(t.substr(4,2),10);return O(r,e,s)?{meta:{},valid:h(t)}:{meta:{},valid:!1}}(s.value);break;case"si":V=function(a){return{meta:{},valid:t(a,"SI")}}(s.value);break;case"sm":V=function(a){return{meta:{},valid:/^\d{5}$/.test(a)}}(s.value);break;case"th":V=function(a){if(13!==a.length)return{meta:{},valid:!1};for(var t=0,r=0;r<12;r++)t+=parseInt(a.charAt(r),10)*(13-r);return{meta:{},valid:(11-t%11)%10===parseInt(a.charAt(12),10)}}(s.value);break;case"tr":V=function(a){if(11!==a.length)return{meta:{},valid:!1};for(var t=0,r=0;r<10;r++)t+=parseInt(a.charAt(r),10);return{meta:{},valid:t%10===parseInt(a.charAt(10),10)}}(s.value);break;case"tw":V=function(a){var t=a.toUpperCase();if(!/^[A-Z][12][0-9]{8}$/.test(t))return{meta:{},valid:!1};for(var r=t.length,e="ABCDEFGHJKLMNPQRSTUVXYWZIO".indexOf(t.charAt(0))+10,s=Math.floor(e/10)+e%10*(r-1),n=0,i=1;i<r-1;i++)n+=parseInt(t.charAt(i),10)*(r-1-i);return{meta:{},valid:(s+n+parseInt(t.charAt(r-1),10))%10==0}}(s.value);break;case"uy":V=function(a){if(!/^\d{8}$/.test(a))return{meta:{},valid:!1};for(var t=[2,9,8,7,6,3,4],r=0,e=0;e<7;e++)r+=parseInt(a.charAt(e),10)*t[e];return(r%=10)>0&&(r=10-r),{meta:{},valid:"".concat(r)===a.charAt(7)}}(s.value);break;case"za":V=function(a){if(!/^[0-9]{10}[0|1][8|9][0-9]$/.test(a))return{meta:{},valid:!1};var t=parseInt(a.substr(0,2),10),r=(new Date).getFullYear()%100,e=parseInt(a.substr(2,2),10),s=parseInt(a.substr(4,2),10);return k(t=t>=r?t+1900:t+2e3,e,s)?{meta:{},valid:C(a)}:{meta:{},valid:!1}}(s.value)}var D=g(s.l10n&&s.l10n.id?o.message||s.l10n.id.country:o.message,s.l10n&&s.l10n.id&&s.l10n.id.countries?s.l10n.id.countries[$.toUpperCase()]:$.toUpperCase());return Object.assign({},{message:D},V)}}}}));
