/**
 * The input data of the program. Everything that needs to get into the program needs to be declared here.
 *
 * <AUTHOR>
 * @version 2.0
 */
machineCouch.InputData = {
	pointSize: 10.0,

	clusters: {
		name: "wgl_clusters",
		flatColor: false,
		minColorPercentage: 0.5,
		gradientThresholdRadius: 0.4,
		clustersColorBlending: false,
		influenceRadius: 0.06,

		centroids: [
			{
				position: [0.0714, 0.0714, 0.0714],
				color: [1.0, 1.0, 1.0],
			},
			{
				position: [0.2142, 0.2142, 0.2142],
				color: [0.0, 1.0, 0.0],
			},
			{
				position: [0.3571, 0.3571, 0.3571],
				color: [0.0, 0.0, 1.0],
			},
			{
				position: [0.4999, 0.4999, 0.4999],
				color: [1.0, 0.0, 0.0],
			},
			{
				position: [0.6427, 0.6427, 0.6427],
				color: [1.0, 0.0, 1.0],
			},
			{
				position: [0.7855, 0.7855, 0.7855],
				color: [1.0, 1.0, 0.0],
			},
			{
				position: [0.9283, 0.9283, 0.9283],
				color: [0.0, 1.0, 1.0],
			},
		],

		data: []
	},

	getStudentData: function(id) {
		// Get the student data async. The callback function is openStudentPopup(data).

		// For testing purpose, gonna call the openStudentPopup(data).
		this.openStudentPopup({id: id, name: "Arlindo Almada", note: "Esta aluno está contudo em cima!"});
	},

	openStudentPopup: function(data) {
		// Converta o data para js object no formato {id: data.id, name: data.name, note: data.note}
		var jsData = {id: data.id, name: data.name, note: data.note};

		machineCouch.machineCouchWorld.showPopup(jsData);
	}
}