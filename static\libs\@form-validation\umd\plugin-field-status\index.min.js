/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-field-status
 * @version 2.4.0
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("@form-validation/core")):"function"==typeof define&&define.amd?define(["@form-validation/core"],t):((e="undefined"!=typeof globalThis?globalThis:e||self).FormValidation=e.FormValidation||{},e.FormValidation.plugins=e.FormValidation.plugins||{},e.FormValidation.plugins.FieldStatus=t(e.FormValidation))}(this,(function(e){"use strict";var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},t(e,n)};return function(e){function n(t){var n=e.call(this,t)||this;return n.statuses=new Map,n.opts=Object.assign({},{onStatusChanged:function(){}},t),n.elementValidatingHandler=n.onElementValidating.bind(n),n.elementValidatedHandler=n.onElementValidated.bind(n),n.elementNotValidatedHandler=n.onElementNotValidated.bind(n),n.elementIgnoredHandler=n.onElementIgnored.bind(n),n.fieldAddedHandler=n.onFieldAdded.bind(n),n.fieldRemovedHandler=n.onFieldRemoved.bind(n),n}return function(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}(n,e),n.prototype.install=function(){this.core.on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.ignored",this.elementIgnoredHandler).on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler)},n.prototype.uninstall=function(){this.statuses.clear(),this.core.off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.ignored",this.elementIgnoredHandler).off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler)},n.prototype.areFieldsValid=function(){return Array.from(this.statuses.values()).every((function(e){return"Valid"===e||"NotValidated"===e||"Ignored"===e}))},n.prototype.getStatuses=function(){return this.isEnabled?this.statuses:new Map},n.prototype.onFieldAdded=function(e){this.statuses.set(e.field,"NotValidated")},n.prototype.onFieldRemoved=function(e){this.statuses.has(e.field)&&this.statuses.delete(e.field),this.handleStatusChanged(this.areFieldsValid())},n.prototype.onElementValidating=function(e){this.statuses.set(e.field,"Validating"),this.handleStatusChanged(!1)},n.prototype.onElementValidated=function(e){this.statuses.set(e.field,e.valid?"Valid":"Invalid"),e.valid?this.handleStatusChanged(this.areFieldsValid()):this.handleStatusChanged(!1)},n.prototype.onElementNotValidated=function(e){this.statuses.set(e.field,"NotValidated"),this.handleStatusChanged(!1)},n.prototype.onElementIgnored=function(e){this.statuses.set(e.field,"Ignored"),this.handleStatusChanged(this.areFieldsValid())},n.prototype.handleStatusChanged=function(e){this.isEnabled&&this.opts.onStatusChanged(e)},n}(e.Plugin)}));
