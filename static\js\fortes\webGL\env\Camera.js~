/**
 * Class to manage the view object, or the camera position.
 * <AUTHOR>
 * @since 2018
 */
class Camera {
	/**
	 * Initialize the object with the rotation angle in X and Z axis and the Zoom translation on Z axis.
	 * Initialize also the view matrix. Because, it's a dynamic object.
	 *
	 * @param angleX The X axis angle.
	 * @param angleY The Y axis angle.
	 * @param angleZ The Z axis angle.
	 * @param zoomValue The Zoom value translation on Z axis.
	 */
	constructor(angleX, angleY, angleZ, zoomValue) {
		this.angleX = angleX;
		this.angleY = angleY;
		this.angleZ = angleZ;
		this.zoomValue = zoomValue;
		this.viewMatrix = new Matrix4x4();
		
		// Mouse auxiliary variables.
		this.drag = false;
		this.posX = 0; 
		this.posY = 0;
	}
	
	/**
	 * Initialize the projection matrix to be used by this camera.
	 *
	 * @param gl The webGL context object.
	 * @param u_projMatrix The projection matrix location in the GLSL program.
	 * @param fovy The angle between the upper and lower sides of the frustum.
	 * @param aspect The aspect ratio of the frustum. (width/height).
	 * @param near The distances to the nearer depth clipping plane. This value must be plus value.
	 * @param far The distances to the farther depth clipping plane. This value must be plus value.
	 */
	setProjection(gl, u_projMatrix, fovy, aspect, near, far) {
		try {
			var projMatrix = new Matrix4x4();
			
			// Calculate the projection matrix and pass it to the GPU.
			projMatrix.setPerspective(fovy, aspect, near, far);
			gl.uniformMatrix4fv(u_projMatrix, false, projMatrix.elements);
		} catch (exp) {
			console.log(exp);
		}
	}
	
	/**
	 * Rotate the view around the X and Z axels.
	 *
	 * @param The generated event object.
	 */
	rotate(ev) {
		if (this.drag) {
			if (ev.clientX - this.posX > 0)
				this.angleZ += 4.0;
			else if (ev.clientX - this.posX < 0)
				this.angleZ -= 4.0;
				
			if (ev.clientY - this.posY > 0)
				this.angleX += 4.0;
			else if (ev.clientY - this.posY < 0)
				this.angleX -= 4.0;
			
			this.posX = ev.clientX;		
			this.posY = ev.clientY; 
		}
	}

	/**
	 * Zoom in/out the view.
	 *
	 * @param The generated event object.
	 */
	zoom(ev) {
		if (ev.wheelDelta > 0)
			this.zoomValue += 4.0;
		else if (ev.wheelDelta < 0)
			this.zoomValue -= 4.0;
	}
	
	/**
	 * Move the camera. Set the view matrix and pass it to the GPU.
	 *
	 * @param gl The webGL context object.
	 * @param u_viewMatrix The View Matrix location in the GLSL program.
	 */
	move(gl, u_viewMatrix) {
		try {
			// Clump the values.
			if (this.angleX > 360)
				this.angleX -= 360;
			else if (this.angleX < -360)
				this.angleX += 360;
				
			if (this.angleZ > 360)
				this.angleZ -= 360;
			else if (this.angleZ < -360)
				this.angleZ += 360;
				
			if (this.zoomValue > -150)
				this.zoomValue = -150;
			else if (this.zoomValue < -400.0)
				this.zoomValue = -400.0;
		
			// Set the view matrix.
			this.viewMatrix.setIdentity();
			this.viewMatrix.translate(0.0, 0.0, this.zoomValue);
			this.viewMatrix.rotate(this.angleX, 1.0, 0.0, 0.0);
			this.viewMatrix.rotate(this.angleZ, 0.0, 0.0, 1.0);
			gl.uniformMatrix4fv(u_viewMatrix, false, this.viewMatrix.elements);
		} catch (exp) {
			console.log(exp);
		}
	}
}