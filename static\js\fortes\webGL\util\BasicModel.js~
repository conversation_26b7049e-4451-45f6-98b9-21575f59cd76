/**
 * Basic class to build up WebGL models.
 * <AUTHOR>
 * @version	1.0
 * @since	2016
 */
fortes.webGL.util.BasicModel = {
	parent: null,			// The parent model.
	visible: true,			// The flag to make it visible or not.
	vertices: null,			// The array of vertices.
	normals: null,			// The array of normals.
	texels: null,			// The array of texels.
	indices: null,			// The array of indices.
	verticesBuffer: null,	// The vertices buffer in the GPU.
	normalsBuffer: null,	// The normals buffer in the GPU.
	texelsBuffer: null,		// The texels buffer in the GPU.
	indicesBuffer: null,	// The indices buffer in the GPU.
	numberVertices: 0,		// The number of vertices in the array to send to the GPU.
	modelMatrix: null,		// The model matrix.
	
	/**
	 * Create a vertex buffer with the passed data.
	 *
	 * @param gl WebGL context object.
	 * @param data The buffer data.
	 */
	createVertexBuffer: function(gl, data) {
		var buffer = gl.createBuffer();
		
		if (data !== null && data.length > 0) {
		    gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
		    gl.bufferData(gl.ARRAY_BUFFER, data, gl.DYNAMIC_DRAW);
		    gl.bindBuffer(gl.ARRAY_BUFFER, null);
		}

		return buffer;
	},
	
	/**
	 * Create a index buffer with the passed data.
	 *
	 * @param gl WebGL context object.
	 * @param data The buffer data.
	 */
	createIndexBuffer: function(gl, data) {
		var buffer = gl.createBuffer();
		
		gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, buffer);
		gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, data, gl.STATIC_DRAW);
		gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, null);

		return buffer;
	},

	/**
	 * Method to be overridden. To create all model buffers.
	 *
	 * @param gl WebGL context object.
	 * @param data The buffer data.
	 */
	createBuffers: function(gl) {},

	/**
	 * Method to be overridden. To render the model.
	 *
	 * @param world The world object with shared data and the context.
	 */
	render: function(world) {},

	/**
	 * Method to be overridden. To transform and send properties to the GPU.
	 *
	 * @param world The world object with shared data and the context.
	 */
	move: function(world) {},
	
	/**
	 * Method to extend its properties and methods.
	 *
	 * @param	The child object to extend this object.
	 */
	extendTo: function(child) {
		var i;
		
		for (i in this) {
			child[i] = this[i];
		}
	}
}