// Pagination
// *******************************************************************************

// Pagination Mixins
@each $color, $value in $theme-colors {
  @if $color != primary and $color != light {
    @include template-pagination-variant('.pagination-#{$color}', $value);
    @include template-pagination-outline-variant('.pagination-outline-#{$color}', $value);
  }
}

// Pagination next, prev, first & last css padding
.page-item {
  &.first,
  &.last,
  &.next,
  &.prev,
  &.previous {
    .page-link {
      padding-top: $pagination-padding-y - 0.148rem;
      padding-bottom: $pagination-padding-y - 0.148rem;
    }
  }
  &.disabled {
    .page-link {
      border-color: $pagination-border-color;
    }
  }
}

// Pagination basic style
.page-link,
.page-link > a {
  @include border-radius($border-radius);

  line-height: $pagination-line-height;
  text-align: center;
  min-width: calc(
    #{'#{($font-size-base * $pagination-line-height) + ($pagination-padding-y * 2)} + calc(#{$pagination-border-width} * 2)'}
  );

  &:focus {
    color: $pagination-hover-color;
  }
}

// Removed border from default pagination
.pagination {
  &:not([class*='pagination-outline-']) {
    .page-link {
      border-color: transparent;
    }
  }
}

.page-link.btn-primary {
  box-shadow: none !important;
}

// Pagination shape rounded
.pagination {
  &.pagination-rounded .page-item a {
    @include border-radius($border-radius);
  }
}

// Sizing
// *******************************************************************************

// Pagination Large
.pagination-lg .page-link,
.pagination-lg > li > a:not(.page-link) {
  min-width: calc(
    #{'#{($font-size-lg * $pagination-line-height) + ($pagination-padding-y-lg * 2)} + calc(#{$pagination-border-width} * 2)'}
  );
}
.pagination-lg > .page-item {
  &.first,
  &.last,
  &.next,
  &.prev,
  &.previous {
    .page-link {
      padding-top: $pagination-padding-y-lg - 0.155rem;
      padding-bottom: $pagination-padding-y-lg - 0.155rem;
    }
  }
}

// Pagination Small
.pagination-sm .page-link,
.pagination-sm > li > a:not(.page-link) {
  min-width: calc(
    #{'#{($font-size-sm * $pagination-line-height) + ($pagination-padding-y-sm * 2)} + calc(#{$pagination-border-width} * 2)'}
  );
}
.pagination-sm > .page-item {
  &.first,
  &.last,
  &.next,
  &.prev,
  &.previous {
    .page-link {
      padding-top: $pagination-padding-y-sm - 0.074rem;
      padding-bottom: $pagination-padding-y-sm - 0.074rem;
    }
  }
}

// RTL pagination
// *******************************************************************************

@include rtl-only {
  .pagination {
    padding-right: 0;
  }

  // Add spacing between pagination items
  .page-item + .page-item .page-link,
  .pagination li + li > a:not(.page-link) {
    margin-left: 0;
    margin-right: $pagination-margin-start;
  }

  .page-item {
    &.first,
    &.last,
    &.next,
    &.prev,
    &.previous {
      .page-link {
        i {
          &::before {
            transform: rotate(180deg);
          }
        }
      }
    }
  }
}
