/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/validator-date
 * @version 2.4.0
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("@form-validation/core")):"function"==typeof define&&define.amd?define(["@form-validation/core"],t):((e="undefined"!=typeof globalThis?globalThis:e||self).FormValidation=e.FormValidation||{},e.FormValidation.validators=e.FormValidation.validators||{},e.FormValidation.validators.date=t(e.FormValidation))}(this,(function(e){"use strict";var t=e.utils.format,n=e.utils.isValidDate,a=e.utils.removeUndefined,r=function(e,t,n){var a=t.indexOf("YYYY"),r=t.indexOf("MM"),l=t.indexOf("DD");if(-1===a||-1===r||-1===l)return null;var i=e.split(" "),s=i[0].split(n);if(s.length<3)return null;var o=new Date(parseInt(s[a],10),parseInt(s[r],10)-1,parseInt(s[l],10)),c=i.length>2?i[2]:null;if(i.length>1){var d=i[1].split(":"),u=d.length>0?parseInt(d[0],10):0;o.setHours(c&&"PM"===c.toUpperCase()&&u<12?u+12:u),o.setMinutes(d.length>1?parseInt(d[1],10):0),o.setSeconds(d.length>2?parseInt(d[2],10):0)}return o},l=function(e,t){var n=t.replace(/Y/g,"y").replace(/M/g,"m").replace(/D/g,"d").replace(/:m/g,":M").replace(/:mm/g,":MM").replace(/:S/,":s").replace(/:SS/,":ss"),a=e.getDate(),r=a<10?"0".concat(a):a,l=e.getMonth()+1,i=l<10?"0".concat(l):l,s="".concat(e.getFullYear()).substr(2),o=e.getFullYear(),c=e.getHours()%12||12,d=c<10?"0".concat(c):c,u=e.getHours(),f=u<10?"0".concat(u):u,g=e.getMinutes(),m=g<10?"0".concat(g):g,p=e.getSeconds(),h=p<10?"0".concat(p):p,v={H:"".concat(u),HH:"".concat(f),M:"".concat(g),MM:"".concat(m),d:"".concat(a),dd:"".concat(r),h:"".concat(c),hh:"".concat(d),m:"".concat(l),mm:"".concat(i),s:"".concat(p),ss:"".concat(h),yy:"".concat(s),yyyy:"".concat(o)};return n.replace(/d{1,4}|m{1,4}|yy(?:yy)?|([HhMs])\1?|"[^"]*"|'[^']*'/g,(function(e){return v[e]?v[e]:e.slice(1,e.length-1)}))};return function(){return{validate:function(e){if(""===e.value)return{meta:{date:null},valid:!0};var i=Object.assign({},{format:e.element&&"date"===e.element.getAttribute("type")?"YYYY-MM-DD":"MM/DD/YYYY",message:""},a(e.options)),s=e.l10n?e.l10n.date.default:i.message,o={message:"".concat(s),meta:{date:null},valid:!1},c=i.format.split(" "),d=c.length>1?c[1]:null,u=c.length>2?c[2]:null,f=e.value.split(" "),g=f[0],m=f.length>1?f[1]:null,p=f.length>2?f[2]:null;if(c.length!==f.length)return o;var h=i.separator||(-1!==g.indexOf("/")?"/":-1!==g.indexOf("-")?"-":-1!==g.indexOf(".")?".":"/");if(null===h||-1===g.indexOf(h))return o;var v=g.split(h),M=c[0].split(h);if(v.length!==M.length)return o;var Y=v[M.indexOf("YYYY")],y=v[M.indexOf("MM")],D=v[M.indexOf("DD")];if(!/^\d+$/.test(Y)||!/^\d+$/.test(y)||!/^\d+$/.test(D)||Y.length>4||y.length>2||D.length>2)return o;var x=parseInt(Y,10),I=parseInt(y,10),O=parseInt(D,10);if(!n(x,I,O))return o;var T=new Date(x,I-1,O);if(d){var F=m.split(":");if(d.split(":").length!==F.length)return o;var H=F.length>0?F[0].length<=2&&/^\d+$/.test(F[0])?parseInt(F[0],10):-1:0,V=F.length>1?F[1].length<=2&&/^\d+$/.test(F[1])?parseInt(F[1],10):-1:0,b=F.length>2?F[2].length<=2&&/^\d+$/.test(F[2])?parseInt(F[2],10):-1:0;if(-1===H||-1===V||-1===b)return o;if(b<0||b>60)return o;if(H<0||H>=24||u&&H>12)return o;if(V<0||V>59)return o;T.setHours(p&&"PM"===p.toUpperCase()&&H<12?H+12:H),T.setMinutes(V),T.setSeconds(b)}var S="function"==typeof i.min?i.min():i.min,$=S instanceof Date?S:S?r(S,M,h):T,w="function"==typeof i.max?i.max():i.max,U=w instanceof Date?w:w?r(w,M,h):T,j=S instanceof Date?l($,i.format):S,C=w instanceof Date?l(U,i.format):w;switch(!0){case!!j&&!C:return{message:t(e.l10n?e.l10n.date.min:s,j),meta:{date:T},valid:T.getTime()>=$.getTime()};case!!C&&!j:return{message:t(e.l10n?e.l10n.date.max:s,C),meta:{date:T},valid:T.getTime()<=U.getTime()};case!!C&&!!j:return{message:t(e.l10n?e.l10n.date.range:s,[j,C]),meta:{date:T},valid:T.getTime()<=U.getTime()&&T.getTime()>=$.getTime()};default:return{message:"".concat(s),meta:{date:T},valid:!0}}}}}}));
