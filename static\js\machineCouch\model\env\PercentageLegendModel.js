/**
 * Submodel to assembly Machine Couch.
 * This class is generated by "WF3DConverter". My application to convert Wavefront objects.
 
 * <AUTHOR>
 * @version 2.0
 */
machineCouch.model.env.PercentageLegendeModel = function(parent) {
	// Inheritance.
	fortes.webGL.util.BasicModel.extendTo(this);
	
	// Properties.
	this.parent = parent;
	this.vertices = new Float32Array([0.12012,0.035081,0.0,0.120768,0.035037,0.0,0.121388,0.034909,0.0,0.121974,0.034703,0.0,0.122522,0.034425,0.0,0.123026,0.034082,0.0,0.12348,0.033678,0.0,0.123879,0.033221,0.0,0.124218,0.032716,0.0,0.124491,0.03217,0.0,0.124692,0.031588,0.0,0.124817,0.030976,0.0,0.12486,0.030341,0.0,0.124817,0.029693,0.0,0.124692,0.029073,0.0,0.124491,0.028486,0.0,0.124218,0.027938,0.0,0.123879,0.027435,0.0,0.12348,0.026981,0.0,0.123026,0.026582,0.0,0.122522,0.026243,0.0,0.121974,0.02597,0.0,0.121388,0.025768,0.0,0.120768,0.025644,0.0,0.12012,0.025601,0.0,0.119485,0.025644,0.0,0.118873,0.025768,0.0,0.118291,0.02597,0.0,0.117744,0.026243,0.0,0.11724,0.026582,0.0,0.116783,0.026981,0.0,0.116379,0.027435,0.0,0.116036,0.027938,0.0,0.115758,0.028486,0.0,0.115552,0.029073,0.0,0.115424,0.029693,0.0,0.11538,0.030341,0.0,0.115424,0.030976,0.0,0.115552,0.031588,0.0,0.115758,0.03217,0.0,0.116036,0.032716,0.0,0.116379,0.033221,0.0,0.116782,0.033678,0.0,0.11724,0.034082,0.0,0.117744,0.034425,0.0,0.118291,0.034703,0.0,0.118873,0.034909,0.0,0.119485,0.035037,0.0,0.12012,0.039521,0.0,0.118881,0.039436,0.0,0.11769,0.039191,0.0,0.116559,0.038795,0.0,0.1155,0.038261,0.0,0.114523,0.037599,0.0,0.11364,0.036821,0.0,0.112862,0.035938,0.0,0.1122,0.034961,0.0,0.111666,0.033901,0.0,0.11127,0.032771,0.0,0.111024,0.03158,0.0,0.11094,0.030341,0.0,0.111024,0.029089,0.0,0.11127,0.02789,0.0,0.111666,0.026755,0.0,0.1122,0.025694,0.0,0.112862,0.024718,0.0,0.11364,0.023838,0.0,0.114523,0.023064,0.0,0.1155,0.022407,0.0,0.116559,0.021878,0.0,0.11769,0.021487,0.0,0.118881,0.021244,0.0,0.12012,0.021161,0.0,0.121347,0.021245,0.0,0.122529,0.021491,0.0,0.123655,0.021886,0.0,0.124713,0.022421,0.0,0.125691,0.023083,0.0,0.126577,0.023861,0.0,0.12736,0.024744,0.0,0.128027,0.025721,0.0,0.128566,0.02678,0.0,0.128966,0.027911,0.0,0.129214,0.029101,0.0,0.1293,0.030341,0.0,0.129214,0.03158,0.0,0.128966,0.032771,0.0,0.128566,0.033901,0.0,0.128027,0.034961,0.0,0.12736,0.035938,0.0,0.126577,0.036821,0.0,0.125691,0.037599,0.0,0.124713,0.038261,0.0,0.123655,0.038795,0.0,0.122529,0.039191,0.0,0.121347,0.039436,0.0,0.12612,0.063221,0.0,0.12162,0.063221,0.0,0.09468,0.021161,0.0,0.09918,0.021161,0.0,0.10068,0.063161,0.0,0.099441,0.063076,0.0,0.09825,0.062831,0.0,0.097119,0.062435,0.0,0.09606,0.061901,0.0,0.095083,0.061239,0.0,0.0942,0.060461,0.0,0.093422,0.059578,0.0,0.09276,0.058601,0.0,0.092226,0.057541,0.0,0.09183,0.056411,0.0,0.091584,0.05522,0.0,0.0915,0.053981,0.0,0.091584,0.052729,0.0,0.09183,0.05153,0.0,0.092226,0.050395,0.0,0.09276,0.049334,0.0,0.093422,0.048358,0.0,0.0942,0.047478,0.0,0.095083,0.046704,0.0,0.09606,0.046047,0.0,0.097119,0.045518,0.0,0.09825,0.045127,0.0,0.099441,0.044884,0.0,0.10068,0.044801,0.0,0.101907,0.044885,0.0,0.103089,0.045131,0.0,0.104215,0.045526,0.0,0.105273,0.046061,0.0,0.106251,0.046723,0.0,0.107138,0.047501,0.0,0.10792,0.048384,0.0,0.108587,0.049361,0.0,0.109126,0.05042,0.0,0.109526,0.051551,0.0,0.109774,0.052741,0.0,0.10986,0.053981,0.0,0.109774,0.05522,0.0,0.109526,0.056411,0.0,0.109126,0.057541,0.0,0.108587,0.058601,0.0,0.10792,0.059578,0.0,0.107137,0.060461,0.0,0.106251,0.061239,0.0,0.105273,0.061901,0.0,0.104215,0.062435,0.0,0.103089,0.062831,0.0,0.101907,0.063076,0.0,0.10068,0.058721,0.0,0.101328,0.058677,0.0,0.101948,0.058549,0.0,0.102534,0.058343,0.0,0.103082,0.058065,0.0,0.103586,0.057722,0.0,0.10404,0.057318,0.0,0.104439,0.056861,0.0,0.104778,0.056356,0.0,0.105051,0.05581,0.0,0.105252,0.055228,0.0,0.105377,0.054616,0.0,0.10542,0.053981,0.0,0.105377,0.053333,0.0,0.105252,0.052713,0.0,0.105051,0.052126,0.0,0.104778,0.051578,0.0,0.104439,0.051075,0.0,0.10404,0.050621,0.0,0.103586,0.050222,0.0,0.103082,0.049883,0.0,0.102534,0.04961,0.0,0.101948,0.049408,0.0,0.101328,0.049284,0.0,0.10068,0.049241,0.0,0.100045,0.049284,0.0,0.099433,0.049408,0.0,0.098851,0.04961,0.0,0.098304,0.049883,0.0,0.0978,0.050222,0.0,0.097342,0.050621,0.0,0.096939,0.051075,0.0,0.096596,0.051578,0.0,0.096318,0.052126,0.0,0.096112,0.052713,0.0,0.095984,0.053333,0.0,0.09594,0.053981,0.0,0.095984,0.054616,0.0,0.096112,0.055228,0.0,0.096318,0.05581,0.0,0.096596,0.056356,0.0,0.096939,0.056861,0.0,0.097342,0.057318,0.0,0.0978,0.057722,0.0,0.098304,0.058065,0.0,0.098851,0.058343,0.0,0.099433,0.058549,0.0,0.100045,0.058677,0.0,0.018,0.062681,0.0,0.01212,0.062681,0.0,0.01212,0.021761,0.0,0.018,0.021761,0.0,0.0582,0.042221,0.0,0.058092,0.045281,0.0,0.057772,0.048151,0.0,0.057251,0.050818,0.0,0.056538,0.053265,0.0,0.055641,0.055479,0.0,0.05457,0.057446,0.0,0.053334,0.05915,0.0,0.051942,0.060576,0.0,0.050404,0.061711,0.0,0.048728,0.06254,0.0,0.046923,0.063048,0.0,0.045,0.063221,0.0,0.043077,0.063048,0.0,0.041272,0.06254,0.0,0.039596,0.061711,0.0,0.038058,0.060576,0.0,0.036666,0.059149,0.0,0.03543,0.057446,0.0,0.034359,0.055479,0.0,0.033462,0.053265,0.0,0.032749,0.050818,0.0,0.032228,0.048151,0.0,0.031908,0.045281,0.0,0.0318,0.042221,0.0,0.031908,0.039161,0.0,0.032228,0.03629,0.0,0.032749,0.033624,0.0,0.033462,0.031176,0.0,0.034359,0.028962,0.0,0.03543,0.026996,0.0,0.036666,0.025292,0.0,0.038058,0.023865,0.0,0.039596,0.02273,0.0,0.041272,0.021901,0.0,0.043077,0.021393,0.0,0.045,0.021221,0.0,0.046923,0.021393,0.0,0.048728,0.021901,0.0,0.050404,0.02273,0.0,0.051942,0.023865,0.0,0.053334,0.025292,0.0,0.05457,0.026996,0.0,0.055641,0.028962,0.0,0.056538,0.031176,0.0,0.057251,0.033624,0.0,0.057772,0.03629,0.0,0.058092,0.039161,0.0,0.05232,0.042221,0.0,0.052263,0.039901,0.0,0.052094,0.037745,0.0,0.051817,0.035761,0.0,0.051436,0.033956,0.0,0.050952,0.032337,0.0,0.05037,0.030911,0.0,0.049693,0.029684,0.0,0.048924,0.028665,0.0,0.048068,0.02786,0.0,0.047126,0.027276,0.0,0.046102,0.026921,0.0,0.045,0.026801,0.0,0.043898,0.026921,0.0,0.042874,0.027276,0.0,0.041933,0.02786,0.0,0.041076,0.028665,0.0,0.040307,0.029684,0.0,0.03963,0.030911,0.0,0.039048,0.032337,0.0,0.038564,0.033956,0.0,0.038183,0.035761,0.0,0.037906,0.037745,0.0,0.037737,0.039901,0.0,0.03768,0.042221,0.0,0.037737,0.044541,0.0,0.037906,0.046696,0.0,0.038183,0.04868,0.0,0.038564,0.050485,0.0,0.039048,0.052104,0.0,0.03963,0.053531,0.0,0.040307,0.054757,0.0,0.041076,0.055776,0.0,0.041932,0.056581,0.0,0.042874,0.057165,0.0,0.043898,0.057521,0.0,0.045,0.057641,0.0,0.046102,0.057521,0.0,0.047126,0.057165,0.0,0.048067,0.056581,0.0,0.048924,0.055776,0.0,0.049693,0.054757,0.0,0.05037,0.053531,0.0,0.050952,0.052104,0.0,0.051436,0.050485,0.0,0.051817,0.04868,0.0,0.052094,0.046696,0.0,0.052263,0.044541,0.0,0.0882,0.042221,0.0,0.088092,0.045281,0.0,0.087772,0.048151,0.0,0.087251,0.050818,0.0,0.086538,0.053265,0.0,0.085641,0.055479,0.0,0.08457,0.057446,0.0,0.083334,0.05915,0.0,0.081942,0.060576,0.0,0.080404,0.061711,0.0,0.078728,0.06254,0.0,0.076923,0.063048,0.0,0.075,0.063221,0.0,0.073077,0.063048,0.0,0.071272,0.06254,0.0,0.069596,0.061711,0.0,0.068058,0.060576,0.0,0.066666,0.059149,0.0,0.06543,0.057446,0.0,0.064359,0.055479,0.0,0.063462,0.053265,0.0,0.062749,0.050818,0.0,0.062228,0.048151,0.0,0.061908,0.045281,0.0,0.0618,0.042221,0.0,0.061908,0.039161,0.0,0.062228,0.03629,0.0,0.062749,0.033624,0.0,0.063462,0.031176,0.0,0.064359,0.028962,0.0,0.06543,0.026996,0.0,0.066666,0.025292,0.0,0.068058,0.023865,0.0,0.069596,0.02273,0.0,0.071272,0.021901,0.0,0.073077,0.021393,0.0,0.075,0.021221,0.0,0.076923,0.021393,0.0,0.078728,0.021901,0.0,0.080404,0.02273,0.0,0.081942,0.023865,0.0,0.083334,0.025292,0.0,0.08457,0.026996,0.0,0.085641,0.028962,0.0,0.086538,0.031176,0.0,0.087251,0.033624,0.0,0.087772,0.03629,0.0,0.088092,0.039161,0.0,0.08232,0.042221,0.0,0.082263,0.039901,0.0,0.082094,0.037745,0.0,0.081817,0.035761,0.0,0.081436,0.033956,0.0,0.080952,0.032337,0.0,0.08037,0.030911,0.0,0.079693,0.029684,0.0,0.078924,0.028665,0.0,0.078067,0.02786,0.0,0.077126,0.027276,0.0,0.076102,0.026921,0.0,0.075,0.026801,0.0,0.073898,0.026921,0.0,0.072874,0.027276,0.0,0.071932,0.02786,0.0,0.071076,0.028665,0.0,0.070307,0.029684,0.0,0.06963,0.030911,0.0,0.069048,0.032337,0.0,0.068564,0.033956,0.0,0.068182,0.035761,0.0,0.067906,0.037745,0.0,0.067737,0.039901,0.0,0.06768,0.042221,0.0,0.067737,0.044541,0.0,0.067906,0.046696,0.0,0.068183,0.04868,0.0,0.068564,0.050485,0.0,0.069048,0.052104,0.0,0.06963,0.053531,0.0,0.070307,0.054757,0.0,0.071076,0.055776,0.0,0.071933,0.056581,0.0,0.072874,0.057165,0.0,0.073898,0.057521,0.0,0.075,0.057641,0.0,0.076102,0.057521,0.0,0.077126,0.057165,0.0,0.078068,0.056581,0.0,0.078924,0.055776,0.0,0.079693,0.054757,0.0,0.08037,0.053531,0.0,0.080952,0.052104,0.0,0.081436,0.050485,0.0,0.081818,0.04868,0.0,0.082094,0.046696,0.0,0.082263,0.044541,0.0]);
	this.texels = null;
	this.normals = null;
	this.indices = new Uint16Array([198,196,197,198,199,196,213,211,212,213,210,211,214,210,213,214,209,210,215,209,214,215,208,209,216,208,215,216,207,208,217,207,216,217,206,207,218,284,217,284,206,217,218,283,284,285,206,284,218,282,283,286,206,285,286,205,206,219,282,218,219,281,282,287,205,286,219,280,281,288,205,287,219,279,280,289,205,288,289,204,205,220,279,219,220,278,279,290,204,289,220,277,278,291,204,290,291,203,204,221,277,220,221,276,277,292,203,291,292,202,203,222,276,221,222,275,276,293,202,292,222,274,275,294,202,293,294,201,202,223,274,222,223,273,274,295,201,294,295,200,201,224,273,223,224,272,273,248,200,295,225,272,224,225,271,272,249,200,248,249,247,200,225,270,271,250,247,249,250,246,247,226,270,225,226,269,270,251,246,250,251,245,246,227,269,226,227,268,269,252,245,251,227,267,268,253,245,252,253,244,245,228,267,227,228,266,267,254,244,253,254,243,244,229,266,228,229,265,266,255,243,254,229,264,265,256,243,255,256,242,243,230,264,229,230,263,264,257,242,256,230,262,263,258,242,257,230,261,262,259,242,258,259,241,242,231,261,230,231,260,261,260,241,259,231,241,260,231,240,241,232,240,231,233,240,232,233,239,240,234,239,233,234,238,239,235,238,234,235,237,238,236,237,235,309,307,308,309,306,307,310,306,309,310,305,306,311,305,310,311,304,305,312,304,311,312,303,304,313,303,312,313,302,303,314,380,313,380,302,313,314,379,380,381,302,380,314,378,379,382,302,381,382,301,302,315,378,314,315,377,378,383,301,382,315,376,377,384,301,383,315,375,376,385,301,384,385,300,301,316,375,315,316,374,375,386,300,385,316,373,374,387,300,386,387,299,300,317,373,316,317,372,373,388,299,387,388,298,299,318,372,317,318,371,372,389,298,388,318,370,371,390,298,389,390,297,298,319,370,318,319,369,370,391,297,390,391,296,297,320,369,319,320,368,369,344,296,391,321,368,320,321,367,368,345,296,344,345,343,296,321,366,367,346,343,345,346,342,343,322,366,321,322,365,366,347,342,346,347,341,342,323,365,322,323,364,365,348,341,347,323,363,364,349,341,348,349,340,341,324,363,323,324,362,363,350,340,349,350,339,340,325,362,324,325,361,362,351,339,350,325,360,361,352,339,351,352,338,339,326,360,325,326,359,360,353,338,352,326,358,359,354,338,353,326,357,358,355,338,354,355,337,338,327,357,326,327,356,357,356,337,355,327,337,356,327,336,337,328,336,327,329,336,328,329,335,336,330,335,329,330,334,335,331,334,330,331,333,334,332,333,331,98,96,97,98,99,96,101,147,100,102,147,101,102,146,147,103,146,102,103,145,146,104,145,103,104,144,145,105,144,104,105,143,144,106,143,105,106,142,143,107,142,106,107,141,142,108,141,107,108,148,141,148,140,141,108,195,148,149,140,148,150,140,149,108,194,195,109,194,108,150,139,140,151,139,150,109,193,194,152,139,151,109,192,193,153,139,152,109,191,192,154,139,153,109,190,191,110,190,109,154,138,139,155,138,154,110,189,190,156,138,155,110,188,189,111,188,110,156,137,138,157,137,156,111,187,188,158,137,157,111,186,187,159,137,158,111,185,186,112,185,111,159,136,137,160,136,159,112,184,185,113,184,112,113,183,184,161,136,160,161,135,136,113,182,183,162,135,161,162,134,135,114,182,113,114,181,182,163,134,162,114,180,181,164,134,163,114,179,180,165,134,164,165,133,134,115,179,114,115,178,179,166,133,165,115,177,178,167,133,166,167,132,133,116,177,115,116,176,177,168,132,167,116,175,176,169,132,168,116,174,175,170,132,169,116,173,174,171,132,170,171,131,132,117,173,116,117,172,173,172,131,171,117,131,172,117,130,131,118,130,117,118,129,130,119,129,118,119,128,129,120,128,119,120,127,128,121,127,120,121,126,127,122,126,121,122,125,126,123,125,122,123,124,125,49,95,48,50,95,49,50,94,95,51,94,50,51,93,94,52,93,51,52,92,93,53,92,52,53,91,92,54,91,53,54,90,91,55,90,54,55,89,90,56,89,55,56,0,89,0,88,89,56,47,0,1,88,0,2,88,1,56,46,47,57,46,56,2,87,88,3,87,2,57,45,46,4,87,3,57,44,45,5,87,4,57,43,44,6,87,5,57,42,43,58,42,57,6,86,87,7,86,6,58,41,42,8,86,7,58,40,41,59,40,58,8,85,86,9,85,8,59,39,40,10,85,9,59,38,39,11,85,10,59,37,38,60,37,59,11,84,85,12,84,11,60,36,37,61,36,60,61,35,36,13,84,12,13,83,84,61,34,35,14,83,13,14,82,83,62,34,61,62,33,34,15,82,14,62,32,33,16,82,15,62,31,32,17,82,16,17,81,82,63,31,62,63,30,31,18,81,17,63,29,30,19,81,18,19,80,81,64,29,63,64,28,29,20,80,19,64,27,28,21,80,20,64,26,27,22,80,21,64,25,26,23,80,22,23,79,80,65,25,64,65,24,25,24,79,23,65,79,24,65,78,79,66,78,65,66,77,78,67,77,66,67,76,77,68,76,67,68,75,76,69,75,68,69,74,75,70,74,69,70,73,74,71,73,70,71,72,73]);	
	this.color = new Float32Array([0.7, 0.7, 0.7, 1.0]);				// Model color.
	this.modelMatrix = new fortes.webGL.util.Matrix4x4();				// The model matrix.

	this.createBuffers = function(gl) {
		try {
			this.verticesBuffer = this.createVertexBuffer(gl, this.vertices);
			this.indicesBuffer = this.createIndexBuffer(gl, this.indices);
			this.numberVertices = this.indices.length;
			this.clean();
		} catch (exp) {
			console.log(exp);
		}
	};

	/**
	 * Clean the arrays. This must be done after creating the buffers to release unnecessary data.
	 */
	this.clean = function() {
		this.vertices = null;
		this.indices = null;
	};

	this.render = function(world) {
		world.gl.uniform4f(world.u_color, this.color[0], this.color[1], this.color[2], this.color[3]);

		world.gl.bindBuffer(world.gl.ARRAY_BUFFER, this.verticesBuffer);
		world.gl.vertexAttribPointer(world.a_position, 3, world.gl.FLOAT, false, 0, 0); 
		world.gl.enableVertexAttribArray(world.a_position);

		world.gl.bindBuffer(world.gl.ELEMENT_ARRAY_BUFFER, this.indicesBuffer);

		// The same object will show up in 3 positions.
		if (world.cameraManager.selectedView == "3D") {
			this.renderToAxisX(world);
			this.renderToAxisY(world);
			this.renderToAxisZ(world);
		} else {
			this.modelMatrix.setIdentity();
			
			if (world.cameraManager.selectedView == "XZ") {
				this.modelMatrix.translate(1.0, 0.0, 0.95);
				this.modelMatrix.rotate(-90.0, 0.0, 1.0, 0.0);
				this.modelMatrix.rotate(-90.0, 1.0, 0.0, 0.0);
			} else if (world.cameraManager.selectedView == "YZ") {
				this.modelMatrix.translate(0.0, 1.0, 1.1);
				this.modelMatrix.rotate(90, 0.0, 1.0, 0.0);
			} else {
				this.modelMatrix.translate(0.95, 1.0, 0.0);
			}
			
			// Send the data to GLSL.
			world.gl.uniformMatrix4fv(world.u_modelMatrix, false, this.modelMatrix.elements);

			// Draw.
			world.gl.drawElements(world.gl.TRIANGLES, this.numberVertices, world.gl.UNSIGNED_SHORT, 0);
		}
	};
	
	/**
	 * Translate to be in the X axis.
	 */
	this.renderToAxisX = function(world) {
		this.modelMatrix.setIdentity();
		this.modelMatrix.translate(1.0, 0.0, 0);

		// Send the data to GLSL.
		world.gl.uniformMatrix4fv(world.u_modelMatrix, false, this.modelMatrix.elements);

		// Draw.
		world.gl.drawElements(world.gl.TRIANGLES, this.numberVertices, world.gl.UNSIGNED_SHORT, 0);
	};
	
	/**
	 * Translate and rotate to be in the Y axis.
	 */
	this.renderToAxisY = function(world) {
		this.modelMatrix.setIdentity();
		this.modelMatrix.rotate(45, 0.0, 1.0, 0);
		this.modelMatrix.translate(-0.15, 1.0, 0);

		// Send the data to GLSL.
		world.gl.uniformMatrix4fv(world.u_modelMatrix, false, this.modelMatrix.elements);

		// Draw.
		world.gl.drawElements(world.gl.TRIANGLES, this.numberVertices, world.gl.UNSIGNED_SHORT, 0);
	};
	
	/**
	 * Translate and rotate to be in the Z axis.
	 */
	this.renderToAxisZ = function(world) {
		this.modelMatrix.setIdentity();
		this.modelMatrix.translate(0.0, 0.0, 1.14);
		this.modelMatrix.rotate(90, 0.0, 1.0, 0);

		// Send the data to GLSL.
		world.gl.uniformMatrix4fv(world.u_modelMatrix, false, this.modelMatrix.elements);

		// Draw.
		world.gl.drawElements(world.gl.TRIANGLES, this.numberVertices, world.gl.UNSIGNED_SHORT, 0);
	};
};