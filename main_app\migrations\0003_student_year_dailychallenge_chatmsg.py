# Generated by Django 4.2.5 on 2023-12-14 11:54

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("main_app", "0002_course_lecturer_course_student_course"),
    ]

    operations = [
        migrations.AddField(
            model_name="student",
            name="year",
            field=models.IntegerField(null=True),
        ),
        migrations.CreateModel(
            name="DailyChallenge",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("challenges", models.TextField()),
                ("day", models.DateField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ChatMsg",
            fields=[
                (
                    "id",
                    models.<PERSON><PERSON>utoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("bot_reply", models.BooleanField(default=False)),
                ("message", models.TextField()),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user_dest",
                    models.OneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        related_name="user_dest",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
