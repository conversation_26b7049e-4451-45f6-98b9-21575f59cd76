/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/validator-zip-code
 * @version 2.4.0
 */

!function(e,a){"object"==typeof exports&&"undefined"!=typeof module?module.exports=a(require("@form-validation/core")):"function"==typeof define&&define.amd?define(["@form-validation/core"],a):((e="undefined"!=typeof globalThis?globalThis:e||self).FormValidation=e.FormValidation||{},e.FormValidation.validators=e.FormValidation.validators||{},e.FormValidation.validators.zipCode=a(e.FormValidation))}(this,(function(e){"use strict";var a=e.utils.format,t=e.utils.removeUndefined;return function(){var e=["AT","BG","BR","CA","CH","CZ","DE","DK","ES","FR","GB","IE","IN","IT","MA","NL","PL","PT","RO","RU","SE","SG","SK","US"];return{validate:function(s){var i=Object.assign({},{message:""},t(s.options));if(""===s.value||!i.country)return{valid:!0};var r=s.value.substr(0,2);if(!(r="function"==typeof i.country?i.country.call(this):i.country)||-1===e.indexOf(r.toUpperCase()))return{valid:!0};var o=!1;switch(r=r.toUpperCase()){case"AT":case"CH":o=/^([1-9]{1})(\d{3})$/.test(s.value);break;case"BG":o=/^([1-9]{1}[0-9]{3})$/.test(s.value);break;case"BR":o=/^(\d{2})([.]?)(\d{3})([-]?)(\d{3})$/.test(s.value);break;case"CA":o=/^(?:A|B|C|E|G|H|J|K|L|M|N|P|R|S|T|V|X|Y){1}[0-9]{1}(?:A|B|C|E|G|H|J|K|L|M|N|P|R|S|T|V|W|X|Y|Z){1}\s?[0-9]{1}(?:A|B|C|E|G|H|J|K|L|M|N|P|R|S|T|V|W|X|Y|Z){1}[0-9]{1}$/i.test(s.value);break;case"CZ":case"SK":o=/^(\d{3})([ ]?)(\d{2})$/.test(s.value);break;case"DE":o=/^(?!01000|99999)(0[1-9]\d{3}|[1-9]\d{4})$/.test(s.value);break;case"DK":o=/^(DK(-|\s)?)?\d{4}$/i.test(s.value);break;case"ES":o=/^(?:0[1-9]|[1-4][0-9]|5[0-2])\d{3}$/.test(s.value);break;case"FR":o=/^[0-9]{5}$/i.test(s.value);break;case"GB":o=function(e){for(var a="[ABCDEFGHIJKLMNOPRSTUWYZ]",t="[ABCDEFGHKLMNOPQRSTUVWXY]",s="[ABDEFGHJLNPQRSTUWXYZ]",i=0,r=[new RegExp("^(".concat(a,"{1}").concat(t,"?[0-9]{1,2})(\\s*)([0-9]{1}").concat(s,"{2})$"),"i"),new RegExp("^(".concat(a,"{1}[0-9]{1}").concat("[ABCDEFGHJKPMNRSTUVWXY]","{1})(\\s*)([0-9]{1}").concat(s,"{2})$"),"i"),new RegExp("^(".concat(a,"{1}").concat(t,"{1}?[0-9]{1}").concat("[ABEHMNPRVWXY]","{1})(\\s*)([0-9]{1}").concat(s,"{2})$"),"i"),new RegExp("^(BF1)(\\s*)([0-6]{1}[ABDEFGHJLNPQRST]{1}[ABDEFGHJLNPQRSTUWZYZ]{1})$","i"),/^(GIR)(\s*)(0AA)$/i,/^(BFPO)(\s*)([0-9]{1,4})$/i,/^(BFPO)(\s*)(c\/o\s*[0-9]{1,3})$/i,/^([A-Z]{4})(\s*)(1ZZ)$/i,/^(AI-2640)$/i];i<r.length;i++)if(r[i].test(e))return!0;return!1}(s.value);break;case"IN":o=/^\d{3}\s?\d{3}$/.test(s.value);break;case"IE":o=/^(D6W|[ACDEFHKNPRTVWXY]\d{2})\s[0-9ACDEFHKNPRTVWXY]{4}$/.test(s.value);break;case"IT":o=/^(I-|IT-)?\d{5}$/i.test(s.value);break;case"MA":o=/^[1-9][0-9]{4}$/i.test(s.value);break;case"NL":o=/^[1-9][0-9]{3} ?(?!sa|sd|ss)[a-z]{2}$/i.test(s.value);break;case"PL":o=/^[0-9]{2}-[0-9]{3}$/.test(s.value);break;case"PT":o=/^[1-9]\d{3}-\d{3}$/.test(s.value);break;case"RO":o=/^(0[1-8]{1}|[1-9]{1}[0-5]{1})?[0-9]{4}$/i.test(s.value);break;case"RU":o=/^[0-9]{6}$/i.test(s.value);break;case"SE":o=/^(S-)?\d{3}\s?\d{2}$/i.test(s.value);break;case"SG":o=/^([0][1-9]|[1-6][0-9]|[7]([0-3]|[5-9])|[8][0-2])(\d{4})$/i.test(s.value);break;default:o=/^\d{4,5}([-]?\d{4})?$/.test(s.value)}return{message:a(s.l10n&&s.l10n.zipCode?i.message||s.l10n.zipCode.country:i.message,s.l10n&&s.l10n.zipCode&&s.l10n.zipCode.countries?s.l10n.zipCode.countries[r]:r),valid:o}}}}}));
