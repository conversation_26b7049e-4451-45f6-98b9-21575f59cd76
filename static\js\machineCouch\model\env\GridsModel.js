/**
 * Submodel to assembly Machine Couch.
 * This class is generated by "WF3DConverter". My application to convert Wavefront objects.
 
 * <AUTHOR>
 * @version 2.0
 */
machineCouch.model.env.GridsModel = function(parent) {
	// Inheritance.
	fortes.webGL.util.BasicModel.extendTo(this);
	
	// Properties.
	this.parent = parent;   // The parent model.
	this.vertices = new Float32Array([0.0, 0.0, 1.0, 0.0, 1.0, 1.0, //ZY grid
										0.0, 1.0, 1.0, 0.0, 1.0, 0.0,
										0.0, 0.0, 0.75, 0.0, 1.0, 0.75,
										0.0, 0.0, 0.5, 0.0, 1.0, 0.5,
										0.0, 0.0, 0.25, 0.0, 1.0, 0.25,
										0.0, 0.75, 0.0, 0.0, 0.75, 1.0,
										0.0, 0.5, 0.0, 0.0, 0.5, 1.0,
										0.0, 0.25, 0.0, 0.0, 0.25, 1.0,
										0.0, 0.0, 1.0, 1.0, 0.0, 1.0, // ZX grid
										1.0, 0.0, 1.0, 1.0, 0.0, 0.0,
										0.0, 0.0, 0.75, 1.0, 0.0, 0.75,
										0.0, 0.0, 0.5, 1.0, 0.0, 0.5,
										0.0, 0.0, 0.25, 1.0, 0.0, 0.25,
										0.75, 0.0, 0.0, 0.75, 0.0, 1.0,
										0.5, 0.0, 0.0, 0.5, 0.0, 1.0,
										0.25, 0.0, 0.0, 0.25, 0.0, 1.0,
										0.0, 1.0, 0.0, 1.0, 1.0, 0.0, // XY grid
										1.0, 1.0, 0.0, 1.0, 0.0, 0.0,
										0.0, 0.75, 0.0, 1.0, 0.75, 0.0,
										0.0, 0.5, 0.0, 1.0, 0.5, 0.0,
										0.0, 0.25, 0.0, 1.0, 0.25, 0.0,
										0.75, 0.0, 0.0, 0.75, 1.0, 0.0,
										0.5, 0.0, 0.0, 0.5, 1.0, 0.0,
										0.25, 0.0, 0.0, 0.25, 1.0, 0.0]);  // 

	this.numberVertices = this.vertices.length / 3;				// Number of vertices.
	this.color = new Float32Array([0.25, 0.25, 0.25, 1.0]);		// Model color.
	this.modelMatrix = new fortes.webGL.util.Matrix4x4();		// The model matrix.

	this.createBuffers = function(gl) {
		try {
			this.verticesBuffer = this.createVertexBuffer(gl, this.vertices);
			this.clean();
		} catch (exp) {
			console.log(exp);
		}
	};

	/**
	 * Clean the arrays. This must be done after creating the buffers to release unnecessary data.
	 */
	this.clean = function() {
		this.vertices = null;
	};

	this.render = function(world) {
		world.gl.uniform4f(world.u_color, this.color[0], this.color[1], this.color[2], this.color[3]);

		world.gl.bindBuffer(world.gl.ARRAY_BUFFER, this.verticesBuffer);
		world.gl.vertexAttribPointer(world.a_position, 3, world.gl.FLOAT, false, 0, 0); 
		world.gl.enableVertexAttribArray(world.a_position);

		world.gl.drawArrays(world.gl.LINES, 0, this.numberVertices);
	};
	
	/**
	 * Overridden method to transform and send the model data to the GPU.
	 *
	 * @param world The world object with shared data and the context.
	 */
	this.move = function(world) {
		world.gl.uniformMatrix4fv(world.u_modelMatrix, false, this.modelMatrix.elements);
	}
}