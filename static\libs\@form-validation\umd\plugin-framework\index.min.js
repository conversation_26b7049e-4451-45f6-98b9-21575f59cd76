/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-framework
 * @version 2.4.0
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("@form-validation/core"),require("@form-validation/plugin-message")):"function"==typeof define&&define.amd?define(["@form-validation/core","@form-validation/plugin-message"],t):((e="undefined"!=typeof globalThis?globalThis:e||self).FormValidation=e.FormValidation||{},e.FormValidation.plugins=e.FormValidation.plugins||{},e.FormValidation.plugins.Framework=t(e.FormValidation,e.FormValidation.plugins))}(this,(function(e,t){"use strict";var o=function(e,t){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])},o(e,t)};var n=e.utils.classSet,i=e.utils.closest;return function(e){function s(t){var o=e.call(this,t)||this;return o.results=new Map,o.containers=new Map,o.opts=Object.assign({},{defaultMessageContainer:!0,eleInvalidClass:"",eleValidClass:"",rowClasses:"",rowValidatingClass:""},t),o.elementIgnoredHandler=o.onElementIgnored.bind(o),o.elementValidatingHandler=o.onElementValidating.bind(o),o.elementValidatedHandler=o.onElementValidated.bind(o),o.elementNotValidatedHandler=o.onElementNotValidated.bind(o),o.iconPlacedHandler=o.onIconPlaced.bind(o),o.fieldAddedHandler=o.onFieldAdded.bind(o),o.fieldRemovedHandler=o.onFieldRemoved.bind(o),o.messagePlacedHandler=o.onMessagePlaced.bind(o),o}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}(s,e),s.prototype.install=function(){var e,o=this;n(this.core.getFormElement(),((e={})[this.opts.formClass]=!0,e["fv-plugins-framework"]=!0,e)),this.core.on("core.element.ignored",this.elementIgnoredHandler).on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("plugins.icon.placed",this.iconPlacedHandler).on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler),this.opts.defaultMessageContainer&&(this.core.registerPlugin(s.MESSAGE_PLUGIN,new t.Message({clazz:this.opts.messageClass,container:function(e,n){var s="string"==typeof o.opts.rowSelector?o.opts.rowSelector:o.opts.rowSelector(e,n),a=i(n,s);return t.Message.getClosestContainer(n,a,o.opts.rowPattern)}})),this.core.on("plugins.message.placed",this.messagePlacedHandler))},s.prototype.uninstall=function(){var e;this.results.clear(),this.containers.clear(),n(this.core.getFormElement(),((e={})[this.opts.formClass]=!1,e["fv-plugins-framework"]=!1,e)),this.core.off("core.element.ignored",this.elementIgnoredHandler).off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("plugins.icon.placed",this.iconPlacedHandler).off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler),this.opts.defaultMessageContainer&&(this.core.deregisterPlugin(s.MESSAGE_PLUGIN),this.core.off("plugins.message.placed",this.messagePlacedHandler))},s.prototype.onEnabled=function(){var e;n(this.core.getFormElement(),((e={})[this.opts.formClass]=!0,e)),this.opts.defaultMessageContainer&&this.core.enablePlugin(s.MESSAGE_PLUGIN)},s.prototype.onDisabled=function(){var e;n(this.core.getFormElement(),((e={})[this.opts.formClass]=!1,e)),this.opts.defaultMessageContainer&&this.core.disablePlugin(s.MESSAGE_PLUGIN)},s.prototype.onIconPlaced=function(e){},s.prototype.onMessagePlaced=function(e){},s.prototype.onFieldAdded=function(e){var t=this,o=e.elements;o&&(o.forEach((function(e){var o,i=t.containers.get(e);i&&(n(i,((o={})[t.opts.rowInvalidClass]=!1,o[t.opts.rowValidatingClass]=!1,o[t.opts.rowValidClass]=!1,o["fv-plugins-icon-container"]=!1,o)),t.containers.delete(e))})),this.prepareFieldContainer(e.field,o))},s.prototype.onFieldRemoved=function(e){var t=this;e.elements.forEach((function(e){var o,i=t.containers.get(e);i&&n(i,((o={})[t.opts.rowInvalidClass]=!1,o[t.opts.rowValidatingClass]=!1,o[t.opts.rowValidClass]=!1,o))}))},s.prototype.prepareFieldContainer=function(e,t){var o=this;if(t.length){var n=t[0].getAttribute("type");"radio"===n||"checkbox"===n?this.prepareElementContainer(e,t[0]):t.forEach((function(t){return o.prepareElementContainer(e,t)}))}},s.prototype.prepareElementContainer=function(e,t){var o,s="string"==typeof this.opts.rowSelector?this.opts.rowSelector:this.opts.rowSelector(e,t),a=i(t,s);a!==t&&(n(a,((o={})[this.opts.rowClasses]=!0,o["fv-plugins-icon-container"]=!0,o)),this.containers.set(t,a))},s.prototype.onElementValidating=function(e){this.removeClasses(e.element,e.elements)},s.prototype.onElementNotValidated=function(e){this.removeClasses(e.element,e.elements)},s.prototype.onElementIgnored=function(e){this.removeClasses(e.element,e.elements)},s.prototype.removeClasses=function(e,t){var o,i=this,s=e.getAttribute("type"),a="radio"===s||"checkbox"===s?t[0]:e;t.forEach((function(e){var t;n(e,((t={})[i.opts.eleValidClass]=!1,t[i.opts.eleInvalidClass]=!1,t))}));var l=this.containers.get(a);l&&n(l,((o={})[this.opts.rowInvalidClass]=!1,o[this.opts.rowValidatingClass]=!1,o[this.opts.rowValidClass]=!1,o))},s.prototype.onElementValidated=function(e){var t,o,i=this,s=e.elements,a=e.element.getAttribute("type"),l="radio"===a||"checkbox"===a?s[0]:e.element;s.forEach((function(t){var o;n(t,((o={})[i.opts.eleValidClass]=e.valid,o[i.opts.eleInvalidClass]=!e.valid,o))}));var r=this.containers.get(l);if(r)if(e.valid){this.results.delete(l);var d=!0;this.containers.forEach((function(e,t){e===r&&!1===i.results.get(t)&&(d=!1)})),d&&n(r,((o={})[this.opts.rowInvalidClass]=!1,o[this.opts.rowValidatingClass]=!1,o[this.opts.rowValidClass]=!0,o))}else this.results.set(l,!1),n(r,((t={})[this.opts.rowInvalidClass]=!0,t[this.opts.rowValidatingClass]=!1,t[this.opts.rowValidClass]=!1,t))},s.MESSAGE_PLUGIN="___frameworkMessage",s}(e.Plugin)}));
