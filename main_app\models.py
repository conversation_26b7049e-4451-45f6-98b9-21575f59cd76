from django.db import models
from django.contrib.auth.models import User
import json

GENDER = ((0,"Masculino"),(1,"Femenino"))

EXTRO_INTRO = ((1,"Extroverted"),(2,"Introverted"))
SENSE_INTUITION = ((1,"Sensing"),(2,"Intuition"))
THINK_FEEL = ((1,"Thinking"),(2,"Feeling"))
JUDGE_PERCEIVE = ((1,"Judging"),(2,"Perceiving"))

def get_label(val):
    values = {"stress level": "nível de estress",
        "anxiety fear level": "nível de ansiedade/medo",
        "low standards level": "nível de baixos padrão",
        "low selfSteem level": "nível de baixa auto-estima",
        "feel depressed level": "nível de depressão",
        "loneliness level": "nível de solidão",
        "immediacy level": "nível de imediatismo",
        "not aim excellence level": "nível de não busca por excelência",
        "bad time management level": "nível de má gestão de tempo",
        "set priorities level": "nível de definição de prioridades",
        "lack self control level": "nível de falta de auto-controle",
        "achieve personal goals level": "nível de falta de ambissão",
        "discrimination level": "nível de discriminação",
        "long distance level": "nível de dificuldade por distância",
        "family income level": "nível de recurso familiar",
        "studying working level": "nível de trabalho",
        "dropout level": "nível de dropout",
        "addicted sensual images video level": "nível de vício em conteúdos adultos",
        "expressing yourself level": "nível de dificuldade de expressão",
        "fluency language level": "nível de linguagem",
        "grammar vocabulary level": "nível de vocabulário",
        "understand lecturer classroom level": "nível de entendimento",
        "understanding reading level": "nível de percepção",
        "learning problem impact communication level": "nível de aprendizado",
        "reread level": "nível de revisão",
        "practice tests level": "nível de dificuldades em testes",
        "self explanation level": "nível de dificuldade de auto",
        "prepare summaries level": "nível de resumo",
        "highlighting underlining level": "nível de highlight",
        "preparation questionnaire level": "nível de questionários",
        "sleep problems level": "nível de problemas de sono",
        "feel always tired level": "nível de cansaço",
        "eating unhealthily level": "nível de alimentação não saudável",
        "feel mentally unhealthy level": "nível de saúde mental",
        "lack energy study time level": "nível de falta de energia",
        "no regular physical activity exercise level": "nível de exercicio físico",
        "extroverted introverted level": "nível Extrovertido/Introvertido",
        "sensing intuition level": "nível Sensorial/",
        "thinking feeling level": "nível Pensativo/Sensorial",
        "judging perceiving level": "nível Julgativo/Perceptivo"}

    return values[val]



def get_color(color):
    if color==1:return "danger"
    if color==2:return "warning"
    if color==3:return "secondary"
    if color==4:return "primary"
    if color==5:return "success"

# Create your models here.
class Course(models.Model):
    name = models.CharField("Course", max_length=50)

    def __str__(self):
        return self.name

class Student(models.Model):

    # Create relationship (don't inherit from User!)
    user = models.OneToOneField(User, on_delete=models.DO_NOTHING,)

    # Add any additional attributes you want

    lastYearResult = models.IntegerField()
    targetResult = models.IntegerField()
    gender = models.IntegerField(choices = GENDER)
    studentNumber = models.CharField(max_length=25, null = True)
    countryOrig = models.CharField(max_length=25, null = True)
    year = models.IntegerField(null = True)
    classRoom = models.IntegerField(null = True)

    course = models.ForeignKey(Course, null = True, on_delete=models.CASCADE)

    #      portfolio_site = models.URLField(blank=True)
    # pip install pillow to use this!
    # Optional: pip install pillow --global-option=”build_ext” --global-option=”--disable-jpeg”
    #      profile_pic = models.ImageField(upload_to='profile_pics',blank=True)

    def __str__(self):
        # Built-in attribute of django.contrib.auth.models.User !
        return self.user.username

    def get_finished_tasks(self):
        return 0

    def get_cluster(self):
        return StudentClusterModel.objects.get(user = self.user).cluster

class Lecturer(models.Model):

    user = models.OneToOneField(User, on_delete=models.DO_NOTHING,)

    subject = models.CharField(max_length=25)
    extrovertedIntroverted = models.IntegerField(choices = EXTRO_INTRO)
    sensingIntuition = models.IntegerField(choices = SENSE_INTUITION)
    thinkingFeeling = models.IntegerField(choices = THINK_FEEL)
    judgingPerceiving = models.IntegerField(choices = JUDGE_PERCEIVE)

    course = models.ForeignKey(Course, null = True, on_delete=models.CASCADE)
    def __str__(self):
        return self.user.username

class Assistant(models.Model):

    user = models.OneToOneField(User, on_delete=models.DO_NOTHING,)

    subject = models.CharField(max_length=25)
    ps2clhModel = models.CharField(max_length=50,null = True)
    countryOrig = models.CharField(max_length=25)
    extrovertedIntroverted = models.IntegerField(choices = EXTRO_INTRO)
    sensingIntuition = models.IntegerField(choices = SENSE_INTUITION)
    thinkingFeeling = models.IntegerField(choices = THINK_FEEL)
    judgingPerceiving = models.IntegerField(choices = JUDGE_PERCEIVE)

    def __str__(self):
        return self.user.username

class WeightPS2CLH(models.Model):

    user = models.OneToOneField(User, on_delete=models.DO_NOTHING,)

    country = models.CharField(max_length=25)
    university = models.CharField(max_length=50)
    dateModel = models.DateTimeField()

    stress = models.CharField(max_length=25)
    stressW = models.FloatField()
    anxietyFear = models.CharField(max_length=25)
    anxietyFearW = models.FloatField()
    lowStandards = models.CharField(max_length=25)
    lowStandardsW = models.FloatField()
    lowSelfSteem = models.CharField(max_length=25)
    lowSelfSteemW = models.FloatField()
    feelDepressed = models.CharField(max_length=25)
    feelDepressedW = models.FloatField()
    loneliness = models.CharField(max_length=25)
    lonelinessW = models.FloatField()

    immediacy = models.CharField(max_length=25)
    immediacyW = models.FloatField()
    notAimExcellence = models.CharField(max_length=25)
    notAimExcellenceW = models.FloatField()
    badTimeManagement = models.CharField(max_length=25)
    badTimeManagementW = models.FloatField()
    setPriorities = models.CharField(max_length=25)
    setPrioritiesW = models.FloatField()
    lackSelfControl = models.CharField(max_length=25)
    lackSelfControlW = models.FloatField()
    achievePersonalGoals = models.CharField(max_length=25)
    achievePersonalGoalsW = models.FloatField()

    discrimination = models.CharField(max_length=25)
    discriminationW = models.FloatField()
    longDistance = models.CharField(max_length=25)
    longDistanceW = models.FloatField()
    familyIncome = models.CharField(max_length=25)
    familyIncomeW = models.FloatField()
    studyingWorking = models.CharField(max_length=25)
    studyingWorkingW = models.FloatField()
    dropout = models.CharField(max_length=25)
    dropoutW = models.FloatField()
    addictedSensualImagesVideo = models.CharField(max_length=25)
    addictedSensualImagesVideoW = models.FloatField()

    expressingYourself = models.CharField(max_length=25)
    expressingYourselfW = models.FloatField()
    fluencyLanguage = models.CharField(max_length=25)
    fluencyLanguageW = models.FloatField()
    grammarVocabulary = models.CharField(max_length=25)
    grammarVocabularyW = models.FloatField()
    understandLecturerClassroom = models.CharField(max_length=25)
    understandLecturerClassroomW = models.FloatField()
    understandingReading = models.CharField(max_length=25)
    understandingReadingW = models.FloatField()
    learningProblemImpactCommunication = models.CharField(max_length=25)
    learningProblemImpactCommunicationW = models.FloatField()

    reread = models.CharField(max_length=25)
    rereadW = models.FloatField()
    practiceTests = models.CharField(max_length=25)
    practiceTestsW = models.FloatField()
    selfExplanation = models.CharField(max_length=25)
    selfExplanationW = models.FloatField()
    prepareSummaries = models.CharField(max_length=25)
    prepareSummariesW = models.FloatField()
    highlightingUnderlining = models.CharField(max_length=25)
    highlightingUnderliningW = models.FloatField()
    preparationQuestionnaire = models.CharField(max_length=25)
    preparationQuestionnaireW = models.FloatField()

    sleepProblems = models.CharField(max_length=25)
    sleepProblemsW = models.FloatField()
    feelAlwaysTired = models.CharField(max_length=25)
    feelAlwaysTiredW = models.FloatField()
    eatingUnhealthily = models.CharField(max_length=25)
    eatingUnhealthilyW = models.FloatField()
    feelMentallyUnhealthy = models.CharField(max_length=25)
    feelMentallyUnhealthyW = models.FloatField()
    lackEnergyStudyTime = models.CharField(max_length=25)
    lackEnergyStudyTimeW = models.FloatField()
    noRegularPhysicalActivityExercise = models.CharField(max_length=25)
    noRegularPhysicalActivityExerciseW = models.FloatField()

    def __str__(self):
        return self.user.username

class StudentInfo(models.Model):

    user = models.OneToOneField(User, on_delete=models.DO_NOTHING,)

    stress = models.IntegerField("stress")
    anxietyFear = models.IntegerField("anxiety/fear")
    lowStandards = models.IntegerField("has low standard")
    lowSelfSteem = models.IntegerField("low self esteem")
    feelDepressed = models.IntegerField()
    loneliness = models.IntegerField()

    immediacy = models.IntegerField()
    notAimExcellence = models.IntegerField()
    badTimeManagement = models.IntegerField()
    setPriorities = models.IntegerField()
    lackSelfControl = models.IntegerField()
    achievePersonalGoals = models.IntegerField()

    discrimination = models.IntegerField()
    longDistance = models.IntegerField()
    familyIncome = models.IntegerField()
    studyingWorking = models.IntegerField()
    dropout = models.IntegerField()
    addictedSensualImagesVideo = models.IntegerField()

    expressingYourself = models.IntegerField()
    fluencyLanguage = models.IntegerField()
    grammarVocabulary = models.IntegerField()
    understandLecturerClassroom = models.IntegerField()
    understandingReading = models.IntegerField()
    learningProblemImpactCommunication = models.IntegerField()

    reread = models.IntegerField()
    practiceTests = models.IntegerField()
    selfExplanation = models.IntegerField()
    prepareSummaries = models.IntegerField()
    highlightingUnderlining = models.IntegerField()
    preparationQuestionnaire = models.IntegerField()

    sleepProblems = models.IntegerField()
    feelAlwaysTired = models.IntegerField()
    eatingUnhealthily = models.IntegerField()
    feelMentallyUnhealthy = models.IntegerField()
    lackEnergyStudyTime = models.IntegerField()
    noRegularPhysicalActivityExercise = models.IntegerField()

    extrovertedIntroverted = models.IntegerField()
    sensingIntuition = models.IntegerField()
    thinkingFeeling = models.IntegerField()
    judgingPerceiving = models.IntegerField()

    def __str__(self):
        # Built-in attribute of django.contrib.auth.models.User !
        return self.user.username

    def get_list_values_(self):
        values = {"stress level":self.stress,
            "anxiety fear level":self.anxietyFear,
            "low standards level":self.lowStandards,
            "low selfSteem level":self.lowSelfSteem,
            "feel depressed level":self.feelDepressed,
            "loneliness level":self.loneliness,
            "immediacy level":self.immediacy,
            "not aim excellence level":self.notAimExcellence,
            "bad time management level":self.badTimeManagement,
            "set priorities level":self.setPriorities,
            "lack self control level":self.lackSelfControl,
            "achieve personal goals level":self.achievePersonalGoals,
            "discrimination level":self.discrimination,
            "long distance level":self.longDistance,
            "family income level":self.familyIncome,
            "studying working level":self.studyingWorking,
            "dropout level":self.dropout,
            "addicted sensual images video level":self.addictedSensualImagesVideo,
            "expressing yourself level":self.expressingYourself,
            "fluency language level":self.fluencyLanguage,
            "grammar vocabulary level":self.grammarVocabulary,
            "understand lecturer classroom level":self.understandLecturerClassroom,
            "understanding reading level":self.understandingReading,
            "learning problem impact communication level":self.learningProblemImpactCommunication,
            "reread level":self.reread,
            "practice tests level":self.practiceTests,
            "self explanation level":self.selfExplanation,
            "prepare summaries level":self.prepareSummaries,
            "highlighting underlining level":self.highlightingUnderlining,
            "preparation questionnaire level":self.preparationQuestionnaire,
            "sleep problems level":self.sleepProblems,
            "feel always tired level":self.feelAlwaysTired,
            "eating unhealthily level":self.eatingUnhealthily,
            "feel mentally unhealthy level":self.feelMentallyUnhealthy,
            "lack energy study time level":self.lackEnergyStudyTime,
            "no regular physical activity exercise level":self.noRegularPhysicalActivityExercise,
            "extroverted introverted level":self.extrovertedIntroverted,
            "sensing intuition level":self.sensingIntuition,
            "thinking feeling level":self.thinkingFeeling,
            "judging perceiving level":self.judgingPerceiving}

        return values.items()

    def get_list_values(self):
        values = [
            {"id_lbl":"stresslevel","label":get_label("stress level"),"value":self.stress,"color":get_color(self.stress)},
            {"id_lbl":"stresslevel","label":get_label("anxiety fear level"),"value":self.anxietyFear,"color":get_color(self.anxietyFear)},
            {"id_lbl":"stresslevel","label":get_label("low standards level"),"value":self.lowStandards,"color":get_color(self.lowStandards)},
            {"id_lbl":"stresslevel","label":get_label("low selfSteem level"),"value":self.lowSelfSteem,"color":get_color(self.lowSelfSteem)},
            {"id_lbl":"stresslevel","label":get_label("feel depressed level"),"value":self.feelDepressed,"color":get_color(self.feelDepressed)},
            {"id_lbl":"stresslevel","label":get_label("loneliness level"),"value":self.loneliness,"color":get_color(self.loneliness)},
            {"id_lbl":"stresslevel","label":get_label("immediacy level"),"value":self.immediacy,"color":get_color(self.immediacy)},
            {"id_lbl":"stresslevel","label":get_label("not aim excellence level"),"value":self.notAimExcellence,"color":get_color(self.notAimExcellence)},
            {"id_lbl":"stresslevel","label":get_label("bad time management level"),"value":self.badTimeManagement,"color":get_color(self.badTimeManagement)},
            {"id_lbl":"stresslevel","label":get_label("set priorities level"),"value":self.setPriorities,"color":get_color(self.setPriorities)},
            {"id_lbl":"stresslevel","label":get_label("lack self control level"),"value":self.lackSelfControl,"color":get_color(self.lackSelfControl)},
            {"id_lbl":"stresslevel","label":get_label("achieve personal goals level"),"value":self.achievePersonalGoals,"color":get_color(self.achievePersonalGoals)},
            {"id_lbl":"stresslevel","label":get_label("discrimination level"),"value":self.discrimination,"color":get_color(self.discrimination)},
            {"id_lbl":"stresslevel","label":get_label("long distance level"),"value":self.longDistance,"color":get_color(self.longDistance)},
            {"id_lbl":"stresslevel","label":get_label("family income level"),"value":self.familyIncome,"color":get_color(self.familyIncome)},
            {"id_lbl":"stresslevel","label":get_label("studying working level"),"value":self.studyingWorking,"color":get_color(self.studyingWorking)},
            {"id_lbl":"stresslevel","label":get_label("dropout level"),"value":self.dropout,"color":get_color(self.dropout)},
            {"id_lbl":"stresslevel","label":get_label("addicted sensual images video level"),"value":self.addictedSensualImagesVideo,"color":get_color(self.addictedSensualImagesVideo)},
            {"id_lbl":"stresslevel","label":get_label("expressing yourself level"),"value":self.expressingYourself,"color":get_color(self.expressingYourself)},
            {"id_lbl":"stresslevel","label":get_label("fluency language level"),"value":self.fluencyLanguage,"color":get_color(self.fluencyLanguage)},
            {"id_lbl":"stresslevel","label":get_label("grammar vocabulary level"),"value":self.grammarVocabulary,"color":get_color(self.grammarVocabulary)},
            {"id_lbl":"stresslevel","label":get_label("understand lecturer classroom level"),"value":self.understandLecturerClassroom,"color":get_color(self.understandLecturerClassroom)},
            {"id_lbl":"stresslevel","label":get_label("understanding reading level"),"value":self.understandingReading,"color":get_color(self.understandingReading)},
            {"id_lbl":"stresslevel","label":get_label("learning problem impact communication level"),"value":self.learningProblemImpactCommunication,"color":get_color(self.learningProblemImpactCommunication)},
            {"id_lbl":"stresslevel","label":get_label("reread level"),"value":self.reread,"color":get_color(self.reread)},
            {"id_lbl":"stresslevel","label":get_label("practice tests level"),"value":self.practiceTests,"color":get_color(self.practiceTests)},
            {"id_lbl":"stresslevel","label":get_label("self explanation level"),"value":self.selfExplanation,"color":get_color(self.selfExplanation)},
            {"id_lbl":"stresslevel","label":get_label("prepare summaries level"),"value":self.prepareSummaries,"color":get_color(self.prepareSummaries)},
            {"id_lbl":"stresslevel","label":get_label("highlighting underlining level"),"value":self.highlightingUnderlining,"color":get_color(self.highlightingUnderlining)},
            {"id_lbl":"stresslevel","label":get_label("preparation questionnaire level"),"value":self.preparationQuestionnaire,"color":get_color(self.preparationQuestionnaire)},
            {"id_lbl":"stresslevel","label":get_label("sleep problems level"),"value":self.sleepProblems,"color":get_color(self.sleepProblems)},
            {"id_lbl":"stresslevel","label":get_label("feel always tired level"),"value":self.feelAlwaysTired,"color":get_color(self.feelAlwaysTired)},
            {"id_lbl":"stresslevel","label":get_label("eating unhealthily level"),"value":self.eatingUnhealthily,"color":get_color(self.eatingUnhealthily)},
            {"id_lbl":"stresslevel","label":get_label("feel mentally unhealthy level"),"value":self.feelMentallyUnhealthy,"color":get_color(self.feelMentallyUnhealthy)},
            {"id_lbl":"stresslevel","label":get_label("lack energy study time level"),"value":self.lackEnergyStudyTime,"color":get_color(self.lackEnergyStudyTime)},
            {"id_lbl":"stresslevel","label":get_label("no regular physical activity exercise level"),"value":self.noRegularPhysicalActivityExercise,"color":get_color(self.noRegularPhysicalActivityExercise)},
            {"id_lbl":"stresslevel","label":get_label("extroverted introverted level"),"value":self.extrovertedIntroverted,"color":get_color(self.extrovertedIntroverted)},
            {"id_lbl":"stresslevel","label":get_label("sensing intuition level"),"value":self.sensingIntuition,"color":get_color(self.sensingIntuition)},
            {"id_lbl":"stresslevel","label":get_label("thinking feeling level"),"value":self.thinkingFeeling,"color":get_color(self.thinkingFeeling)},
            {"id_lbl":"stresslevel","label":get_label("judging perceiving level"),"value":self.judgingPerceiving,"color":get_color(self.judgingPerceiving)}
        ]

        values = sorted(values, key = lambda d: d["value"])
        return values

    def get_list_values(self):
        values = [
            {"id_lbl":"stress level","label":get_label("stress level"),"value":self.stress,"color":get_color(self.stress)},
            {"id_lbl":"anxiety fear level","label":get_label("anxiety fear level"),"value":self.anxietyFear,"color":get_color(self.anxietyFear)},
            {"id_lbl":"low standards level","label":get_label("low standards level"),"value":self.lowStandards,"color":get_color(self.lowStandards)},
            {"id_lbl":"low selfSteem level","label":get_label("low selfSteem level"),"value":self.lowSelfSteem,"color":get_color(self.lowSelfSteem)},
            {"id_lbl":"feel depressed level","label":get_label("feel depressed level"),"value":self.feelDepressed,"color":get_color(self.feelDepressed)},
            {"id_lbl":"loneliness level","label":get_label("loneliness level"),"value":self.loneliness,"color":get_color(self.loneliness)},
            {"id_lbl":"immediacy level","label":get_label("immediacy level"),"value":self.immediacy,"color":get_color(self.immediacy)},
            {"id_lbl":"not aim excellence level","label":get_label("not aim excellence level"),"value":self.notAimExcellence,"color":get_color(self.notAimExcellence)},
            {"id_lbl":"bad time management level","label":get_label("bad time management level"),"value":self.badTimeManagement,"color":get_color(self.badTimeManagement)},
            {"id_lbl":"set priorities level","label":get_label("set priorities level"),"value":self.setPriorities,"color":get_color(self.setPriorities)},
            {"id_lbl":"lack self control level","label":get_label("lack self control level"),"value":self.lackSelfControl,"color":get_color(self.lackSelfControl)},
            {"id_lbl":"achieve personal goals level","label":get_label("achieve personal goals level"),"value":self.achievePersonalGoals,"color":get_color(self.achievePersonalGoals)},
            {"id_lbl":"discrimination level","label":get_label("discrimination level"),"value":self.discrimination,"color":get_color(self.discrimination)},
            {"id_lbl":"long distance level","label":get_label("long distance level"),"value":self.longDistance,"color":get_color(self.longDistance)},
            {"id_lbl":"family income level","label":get_label("family income level"),"value":self.familyIncome,"color":get_color(self.familyIncome)},
            {"id_lbl":"studying working level","label":get_label("studying working level"),"value":self.studyingWorking,"color":get_color(self.studyingWorking)},
            {"id_lbl":"dropout level","label":get_label("dropout level"),"value":self.dropout,"color":get_color(self.dropout)},
            {"id_lbl":"addicted sensual images video level","label":get_label("addicted sensual images video level"),"value":self.addictedSensualImagesVideo,"color":get_color(self.addictedSensualImagesVideo)},
            {"id_lbl":"expressing yourself level","label":get_label("expressing yourself level"),"value":self.expressingYourself,"color":get_color(self.expressingYourself)},
            {"id_lbl":"fluency language level","label":get_label("fluency language level"),"value":self.fluencyLanguage,"color":get_color(self.fluencyLanguage)},
            {"id_lbl":"grammar vocabulary level","label":get_label("grammar vocabulary level"),"value":self.grammarVocabulary,"color":get_color(self.grammarVocabulary)},
            {"id_lbl":"understand lecturer classroom level","label":get_label("understand lecturer classroom level"),"value":self.understandLecturerClassroom,"color":get_color(self.understandLecturerClassroom)},
            {"id_lbl":"understanding reading level","label":get_label("understanding reading level"),"value":self.understandingReading,"color":get_color(self.understandingReading)},
            {"id_lbl":"learning problem impact communication level","label":get_label("learning problem impact communication level"),"value":self.learningProblemImpactCommunication,"color":get_color(self.learningProblemImpactCommunication)},
            {"id_lbl":"reread level","label":get_label("reread level"),"value":self.reread,"color":get_color(self.reread)},
            {"id_lbl":"practice tests level","label":get_label("practice tests level"),"value":self.practiceTests,"color":get_color(self.practiceTests)},
            {"id_lbl":"self explanation level","label":get_label("self explanation level"),"value":self.selfExplanation,"color":get_color(self.selfExplanation)},
            {"id_lbl":"self explanation level","label":get_label("self explanation level"),"value":self.prepareSummaries,"color":get_color(self.prepareSummaries)},
            {"id_lbl":"highlighting underlining level","label":get_label("highlighting underlining level"),"value":self.highlightingUnderlining,"color":get_color(self.highlightingUnderlining)},
            {"id_lbl":"preparation questionnaire level","label":get_label("preparation questionnaire level"),"value":self.preparationQuestionnaire,"color":get_color(self.preparationQuestionnaire)},
            {"id_lbl":"sleep problems level","label":get_label("sleep problems level"),"value":self.sleepProblems,"color":get_color(self.sleepProblems)},
            {"id_lbl":"feel always tired level","label":get_label("feel always tired level"),"value":self.feelAlwaysTired,"color":get_color(self.feelAlwaysTired)},
            {"id_lbl":"eating unhealthily level","label":get_label("eating unhealthily level"),"value":self.eatingUnhealthily,"color":get_color(self.eatingUnhealthily)},
            {"id_lbl":"feel mentally unhealthy level","label":get_label("feel mentally unhealthy level"),"value":self.feelMentallyUnhealthy,"color":get_color(self.feelMentallyUnhealthy)},
            {"id_lbl":"lack energy study time level","label":get_label("lack energy study time level"),"value":self.lackEnergyStudyTime,"color":get_color(self.lackEnergyStudyTime)},
            {"id_lbl":"no regular physical activity exercise level","label":get_label("no regular physical activity exercise level"),"value":self.noRegularPhysicalActivityExercise,"color":get_color(self.noRegularPhysicalActivityExercise)},
            {"id_lbl":"extroverted introverted level","label":get_label("extroverted introverted level"),"value":self.extrovertedIntroverted,"color":get_color(self.extrovertedIntroverted)},
            {"id_lbl":"sensing intuition level","label":get_label("sensing intuition level"),"value":self.sensingIntuition,"color":get_color(self.sensingIntuition)},
            {"id_lbl":"thinking feeling level","label":get_label("thinking feeling level"),"value":self.thinkingFeeling,"color":get_color(self.thinkingFeeling)},
            {"id_lbl":"judging perceiving level","label":get_label("judging perceiving level"),"value":self.judgingPerceiving,"color":get_color(self.judgingPerceiving)}
        ]

        values = sorted(values, key = lambda d: d["value"])
        return values

    def get_preview_list(self):

        return list(self.get_list_values())[:5]

    def get_rest_list(self):

        return list(self.get_list_values())[5:]

class ChatMsg(models.Model):
    user = models.ForeignKey(User, on_delete=models.DO_NOTHING, null = True,related_name = "user")
    user_dest = models.ForeignKey(User, on_delete=models.DO_NOTHING, null = True, related_name = "user_dest")
    bot_reply = models.BooleanField(default = False)
    message = models.TextField()

class StudentChallenge(models.Model):
    user = models.OneToOneField(User, on_delete=models.DO_NOTHING)
    challenges = models.TextField()
    #ex: challenges = "{'list':[]}"
    def get_challenges(self):
        return json.loads(self.challenges)['list']

class DailyChallenge(models.Model):
    user = models.OneToOneField(User, on_delete=models.DO_NOTHING)
    challenges = models.TextField()
    day = models.DateField(auto_now=True, auto_now_add=False)

    def get_weekly_challenges(self):
        pass

class StudentClusterModel(models.Model):

    user = models.OneToOneField(User, on_delete=models.DO_NOTHING,)

    psychology = models.FloatField(default=None)
    selfResponsability = models.FloatField(default=None)
    psychologySelfResponsability = models.FloatField(default=None)
    sociology = models.FloatField(default=None)
    communication = models.FloatField(default=None)
    sociologyCommunication = models.FloatField(default=None)
    learning = models.FloatField(default=None)
    healthWellbeing = models.FloatField(default=None)
    learningHealthWellbeing = models.FloatField(default=None)

    centroid = models.CharField(max_length=60, default=None)
    cluster = models.IntegerField()

    def __str__(self):
        # Built-in attribute of django.contrib.auth.models.User !
        return self.user.username

    def get_x(self):
        return str(float(self.centroid.split(",")[0])/100)

    def get_y(self):
        return str(float(self.centroid.split(",")[1])/100)

    def get_z(self):
        return str(float(self.centroid.split(",")[2])/100)

class AddQAModel(models.Model):

    username = models.CharField(max_length=50, default=None)
    context = models.CharField(max_length=50)
    question = models.CharField(max_length=100)
    answer = models.CharField(max_length=300)
    display_picture = models.FileField()

    def __str__(self):
        return self.username

class VisualRepModel(models.Model):

    user = models.CharField(max_length=50, default=None)
    targetVariable = models.IntegerField()
    psychology = models.CharField(max_length=50)
    psychologyVal = models.IntegerField()
    self_responsibility = models.CharField(max_length=50)
    self_responsibilityVal = models.IntegerField()
    sociology = models.CharField(max_length=50)
    sociologyVal = models.IntegerField()
    communication = models.CharField(max_length=50)
    communicationVal = models.IntegerField()
    learning = models.CharField(max_length=50)
    learningVal = models.IntegerField()
    health_wellbeing = models.CharField(max_length=50)
    health_wellbeing_Val = models.IntegerField()

    def __str__(self):
        return self.user

class StudentData(models.Model):

    studentID = models.FloatField(default=None)
    email = models.CharField(max_length=50)
    lastYearResult = models.IntegerField()
    targetVariable = models.IntegerField()

    stress = models.IntegerField()
    feelDepressed = models.IntegerField()
    anxietyFear = models.IntegerField()
    disturbanceModeBeing = models.IntegerField()
    loneness = models.IntegerField()
    lowSelfsteam = models.IntegerField()
    unspecifiedPsychProb = models.IntegerField()
    totalPsychology = models.IntegerField()

    setPriorities = models.IntegerField()
    establishAchievePersonalGoals = models.IntegerField()
    timeManagement = models.IntegerField()
    aimExcellence = models.IntegerField()
    procrastination = models.IntegerField()
    immediacy = models.IntegerField()
    acceptChange = models.IntegerField()
    hoursStudyPerDay = models.IntegerField()
    totalSelfResponsibility = models.IntegerField()

    studyingWorking = models.IntegerField()
    bullying = models.IntegerField()
    familyIncome = models.IntegerField()
    AdictSensualImages = models.IntegerField()
    discouragementNegativity = models.IntegerField()
    longDistance = models.IntegerField()
    certainNegativeBeliefsHabits = models.IntegerField()
    badConditionsHabitability = models.IntegerField()
    lackElectricity = models.IntegerField()
    lackPublicTransport = models.IntegerField()
    universityCondictions = models.IntegerField()
    hoursPlayDistractionPerDay = models.IntegerField()
    totalSociology = models.IntegerField()

    fluencyInLanguage = models.IntegerField()
    understandLecturerClassroom = models.IntegerField()
    understandingInterpretationOfReading = models.IntegerField()
    expressingYourself = models.IntegerField()
    grammarVocabulary = models.IntegerField()
    stutteringTypologyDisfluencies = models.IntegerField()
    learningProbImpactCommunication = models.IntegerField()
    totalCommunication = models.IntegerField()

    preparationQuestionnaire = models.IntegerField()
    highlightingUnderlining = models.IntegerField()
    practiceTests = models.IntegerField()
    reread = models.IntegerField()
    distributedStudy = models.IntegerField()
    selfExplanation = models.IntegerField()
    prepareSummaries = models.IntegerField()
    totalLearningTec = models.IntegerField()

    sleepProblems = models.IntegerField()
    feelMentallyHealthy = models.IntegerField()
    regularPhysicalActivityExercise = models.IntegerField()
    plentyEnergyDuringStudy = models.IntegerField()
    eatingHealthily = models.IntegerField()
    restBodyMind = models.IntegerField()
    totalHealthWellbeing = models.IntegerField()

    def __str__(self):
        return self.email
