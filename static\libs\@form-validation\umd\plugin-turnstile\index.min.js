/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-turnstile
 * @version 2.4.0
 */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("@form-validation/core")):"function"==typeof define&&define.amd?define(["@form-validation/core"],e):((t="undefined"!=typeof globalThis?globalThis:t||self).FormValidation=t.FormValidation||{},t.FormValidation.plugins=t.FormValidation.plugins||{},t.FormValidation.plugins.Turnstile=e(t.FormValidation))}(this,(function(t){"use strict";var e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},e(t,i)};var i=t.utils.fetch,a=t.utils.removeUndefined;return function(t){function n(e){var i=t.call(this,e)||this;return i.widgetIds=new Map,i.captchaStatus="NotValidated",i.captchaContainer="",i.opts=Object.assign({},n.DEFAULT_OPTIONS,a(e)),i.fieldResetHandler=i.onResetField.bind(i),i.preValidateFilter=i.preValidate.bind(i),i.iconPlacedHandler=i.onIconPlaced.bind(i),i.captchaContainer=i.opts.element.startsWith("#")?i.opts.element:"#".concat(i.opts.element),i}return function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function a(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(a.prototype=i.prototype,new a)}(n,t),n.prototype.install=function(){var t=this;this.core.on("core.field.reset",this.fieldResetHandler).on("plugins.icon.placed",this.iconPlacedHandler).registerFilter("validate-pre",this.preValidateFilter);var e=void 0===window[n.LOADED_CALLBACK]?function(){}:window[n.LOADED_CALLBACK];window[n.LOADED_CALLBACK]=function(){e();var a=t.getTurnstileInstance().render(t.captchaContainer,t.buildTurnstileRenderOptions());t.widgetIds.set(t.captchaContainer,a),t.core.addField(n.CAPTCHA_FIELD,{validators:{promise:{message:t.opts.message,promise:function(e){var a,r=t.widgetIds.has(t.captchaContainer)?t.getTurnstileInstance().getResponse(t.widgetIds.get(t.captchaContainer)):e.value;return""===r?(t.captchaStatus="Invalid",Promise.resolve({valid:!1})):""===t.opts.backendVerificationUrl?(t.captchaStatus="Valid",Promise.resolve({valid:!0})):"Valid"===t.captchaStatus?Promise.resolve({valid:!0}):i(t.opts.backendVerificationUrl,{method:"POST",params:(a={},a[n.CAPTCHA_FIELD]=r,a)}).then((function(e){var i="true"==="".concat(e.success);return t.captchaStatus=i?"Valid":"Invalid",Promise.resolve({meta:e,valid:i})})).catch((function(e){return t.captchaStatus="NotValidated",Promise.reject({valid:!1})}))}}}})};var a=this.getScript();if(!document.body.querySelector('script[src="'.concat(a,'"]'))){var r=document.createElement("script");r.type="text/javascript",r.async=!0,r.defer=!0,r.src=a,document.body.appendChild(r)}},n.prototype.uninstall=function(){var t=this;delete window[n.LOADED_CALLBACK],this.core.off("core.field.reset",this.fieldResetHandler).off("plugins.icon.placed",this.iconPlacedHandler).deregisterFilter("validate-pre",this.preValidateFilter),this.widgetIds.forEach((function(e,i,a){t.getTurnstileInstance().remove(i)})),this.widgetIds.clear();var e=this.getScript();[].slice.call(document.body.querySelectorAll('script[src="'.concat(e,'"]'))).forEach((function(t){return t.parentNode.removeChild(t)})),this.core.removeField(n.CAPTCHA_FIELD)},n.prototype.onEnabled=function(){this.core.enableValidator(n.CAPTCHA_FIELD,"promise")},n.prototype.onDisabled=function(){this.core.disableValidator(n.CAPTCHA_FIELD,"promise")},n.prototype.buildTurnstileRenderOptions=function(){var t=this;return{callback:function(){""===t.opts.backendVerificationUrl&&(t.captchaStatus="Valid",t.core.updateFieldStatus(n.CAPTCHA_FIELD,"Valid"))},"error-callback":function(){t.captchaStatus="Invalid",t.core.updateFieldStatus(n.CAPTCHA_FIELD,"Invalid")},"expired-callback":function(){t.captchaStatus="NotValidated",t.core.updateFieldStatus(n.CAPTCHA_FIELD,"NotValidated")},sitekey:this.opts.siteKey,action:this.opts.action,appearance:this.opts.appearance,cData:this.opts.cData,language:this.opts.language,size:this.opts.size,"refresh-expired":this.opts.refreshExpired,retry:this.opts.retry,"retry-interval":this.opts.retryInterval,tabindex:this.opts.tabIndex,theme:this.opts.theme}},n.prototype.getTurnstileInstance=function(){return window.turnstile},n.prototype.getScript=function(){return"https://challenges.cloudflare.com/turnstile/v0/api.js?onload=".concat(n.LOADED_CALLBACK,"&render=explicit")},n.prototype.preValidate=function(){return this.isEnabled&&"execute"===this.opts.appearance&&this.widgetIds.has(this.captchaContainer)&&"Valid"!==this.captchaStatus&&this.getTurnstileInstance().execute(this.captchaContainer,this.buildTurnstileRenderOptions()),Promise.resolve()},n.prototype.onResetField=function(t){if(t.field===n.CAPTCHA_FIELD&&this.widgetIds.has(this.captchaContainer)){var e=this.widgetIds.get(this.captchaContainer);this.getTurnstileInstance().reset(e)}},n.prototype.onIconPlaced=function(t){if(t.field===n.CAPTCHA_FIELD)if("execute"===this.opts.appearance)t.iconElement.style.display="none";else{var e=document.getElementById(this.captchaContainer);e&&e.parentNode.insertBefore(t.iconElement,e.nextSibling)}},n.CAPTCHA_FIELD="cf-turnstile-response",n.DEFAULT_OPTIONS={backendVerificationUrl:"",appearance:"always",language:"auto",refreshExpired:"auto",retry:"auto",size:"normal",tabIndex:0,theme:"auto"},n.LOADED_CALLBACK="___turnstileLoaded___",n}(t.Plugin)}));
