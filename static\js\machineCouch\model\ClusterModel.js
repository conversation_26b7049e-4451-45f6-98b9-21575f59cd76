/**
 * The cluster model object. The cluster is composed by a set of dots in one color.
 * This class is generated by "WF3DConverter". My application to convert Wavefront objects.
 
 * <AUTHOR> <PERSON>
 * @version 2.0
 */
machineCouch.model.ClusterModel = function(index) {
	// Inheritance.
	fortes.webGL.util.BasicModel.extendTo(this);
	
	// Properties.
	this.index = index;										// The cluster index value.
	this.centroid = null;                  					// The centroid object.
	this.pointsData = [];             						// The array to store the points data: IDs, coordinates and colors.
	this.isLoaded = false;									// Flag to tell whether the cluster was loaded or not.
	this.floatSize = 0;										// The size in bytes of the float type.
	this.modelMatrix = new fortes.webGL.util.Matrix4x4();	// The model matrix.
	this.type = "cluster";

	/**
	* Create the object buffers.
	*
	* @param gl WebGL context object.
	*/
	this.createBuffers = function(gl) {
		try {
			this.floatSize = this.vertices.BYTES_PER_ELEMENT;
			this.verticesBuffer = this.createVertexBuffer(gl, this.vertices);
			this.clean();
		} catch (exp) {
			console.log(exp);
		}
	};
	
	/**
	 * Clean the arrays. This must be done after creating the buffers to release unnecessary data.
	 */
	this.clean = function() {
		this.vertices = null;
	};

	/**
	* Render the object and its submodels.
	*
	* @param world The world object with shared data and the context.
	*/
	this.render = function(world) {
		world.gl.uniform1f(world.u_clusterIndex, this.index);
		world.gl.uniform1i(world.u_isClusterVS, 1);
		world.gl.uniform1i(world.u_isClusterFS, 1);

		world.gl.bindBuffer(world.gl.ARRAY_BUFFER, this.verticesBuffer);
		world.gl.vertexAttribPointer(world.a_pointIndex, 1, world.gl.FLOAT, false, this.floatSize * 7, 0);
		world.gl.vertexAttribPointer(world.a_position, 3, world.gl.FLOAT, false, this.floatSize * 7, this.floatSize);
		world.gl.vertexAttribPointer(world.a_pointColor, 3, world.gl.FLOAT, false, this.floatSize * 7, this.floatSize * 4);
		world.gl.enableVertexAttribArray(world.a_pointIndex);
		world.gl.enableVertexAttribArray(world.a_position);
		world.gl.enableVertexAttribArray(world.a_pointColor);

		// Draw the cluster points.
		world.gl.drawArrays(world.gl.POINTS, 0, this.numberVertices);

		// Set the cluster flags to false.
		world.gl.uniform1i(world.u_isClusterVS, 0);
		world.gl.uniform1i(world.u_isClusterFS, 0);

		// Disable the a_pointIndex and a_pointColor attributes, because it is supposed to be used only in by the clusters.
		world.gl.disableVertexAttribArray(world.a_pointIndex);
		world.gl.disableVertexAttribArray(world.a_pointColor);
	};

	/**
	* Overridden method to transform and send the model data to the GPU.
	*
	* @param world The world object with shared data and the context.
	*/
	this.move = function(world) {
		world.gl.uniformMatrix4fv(world.u_modelMatrix, false, this.modelMatrix.elements);
	};
};