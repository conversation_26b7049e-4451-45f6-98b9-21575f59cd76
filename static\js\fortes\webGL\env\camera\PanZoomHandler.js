/**
 * Class to manage the camera view panning and zooming events.
 
 * <AUTHOR>
 * @version 1.0
 * @date 	22/03/2019
 */
fortes.webGL.env.camera.PanZoomHandler = function() {
	// Properties.
	this.panX = 0.0;										// The absolute moving value in X axis.
	this.panY = 0.0;										// The absolute moving value in Y axis.
	this.zoomValue = 0.0;									// The absolute zoom value.
	this.minZoomValue = -100.0;								// The minimum zoom value.
	this.maxZoomValue = 100.0;								// The maximum zoom value.
	this.panZoomMatrix = new fortes.webGL.util.Matrix4x4();	// The panZoom matrix.
	
	/**
	 * Panning the current canvas in the XY plane.
	 *
	 * @canvas	The canvas to pan.
	 * @deltaX	The step length in the X axis.
	 * @deltaY	The step length in the Y axis.
	 */
	this.pan = function(canvas, deltaX, deltaY) {
		this.panX += (2 * deltaX) / canvas.width;
		this.panY += (2 * deltaY) / canvas.height;
	};
	
	/**
	 * Zoom in/out the view.
	 *
	 * @param value The zoom value to add.
	 */
	this.zoom = function(value) {
		var sumValue = this.zoomValue + value;
		
		// Translate the pan matrix in the Z axis whether in the limits.
		if (sumValue >= this.minZoomValue && sumValue <= this.maxZoomValue) {
			this.zoomValue = sumValue;
		}
	};
	
	/**
	 * Translate relatively the camera position.
	 *
	 * @param x	The X axis value.
	 * @param y	The Y axis value.
	 * @param z	The Z axis value.
	 */
	this.translate = function(x, y, z) {
		this.moveX += x;
		this.moveY += y;
		this.moveZ += z;
	};
	
	/**
	 * Transform the pan zoom matrix.
	 */
	this.move = function() {
		try {
			var pkg = fortes.webGL.env;
			
			this.panZoomMatrix.setIdentity();
			this.panZoomMatrix.translate(this.panX, this.panY, this.zoomValue);
		} catch (exp) {
			console.log(exp);
		}
	};
};