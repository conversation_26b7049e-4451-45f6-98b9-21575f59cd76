/**
 * The input data of the program. Everything that needs to get into the program needs to be declared here.
 *
 * <AUTHOR>
 * @version 2.0
 */
machineCouch.InputData = {
	pointSize: 10.0,

	clusters: {
		name: "wgl_clusters",
		flatColor: false,
		minColorPercentage: 0.5,
		gradientThresholdRadius: 0.4,
		clustersColorBlending: false,
		influenceRadius: 0.06,

		centroids: [
			{
				position: [0.0714, 0.0714, 0.0714],
				color: [1.0, 1.0, 1.0],
			},
			{
				position: [0.2142, 0.2142, 0.2142],
				color: [0.0, 1.0, 0.0],
			},
			{
				position: [0.3571, 0.3571, 0.3571],
				color: [0.0, 0.0, 1.0],
			},
			{
				position: [0.4999, 0.4999, 0.4999],
				color: [1.0, 0.0, 0.0],
			},
			{
				position: [0.6427, 0.6427, 0.6427],
				color: [1.0, 0.0, 1.0],
			},
			{
				position: [0.7855, 0.7855, 0.7855],
				color: [1.0, 1.0, 0.0],
			},
			{
				position: [0.9283, 0.9283, 0.9283],
				color: [0.0, 1.0, 1.0],
			},
		],

		data: [
			1000001, 0.13, 0.092, 0.0074,
			1000002, 0.02, 0.12, 0.14,
			1000003, 0.23, 0.22, 0.074,
			1000004, 0.33, 0.26, 0.18,
			10000014445, 0.428571429, 0.92, 0.74,
			10000018367, 0.333333333, 0.656, 0.45,
			1000001856, 0.733333333, 0.712, 0.78,
			1000003751, 0.847619048, 0.76, 0.88,
			1000004974, 0.819047619, 0.8, 0.84,
			1000005590, 0.6, 0.704, 0.42,
			1000006475, 0.819047619, 0.776, 0.7,
			1000007051, 0.552380952, 0.696, 0.7,
			1000007840, 0.866666667, 0.944, 0.92,
			1000009065, 0.59047619, 0.824, 0.53,
			1000010547, 0.571428571, 0.752, 0.83,
			1000010934, 0.419047619, 0.6, 0.51,
			1000011308, 0.628571429, 0.896, 0.83,
			1000011554, 0.495238095, 0.672, 0.72,
			1000011721, 0.771428571, 0.808, 0.74,
			1000011725, 0.542857143, 0.888, 0.81,
			1000011803, 0.647619048, 0.8, 0.76,
			1000012059, 0.742857143, 0.824, 0.77,
			1000012228, 0.714285714, 0.984, 0.78,
			1000013102, 0.647619048, 0.856, 0.77,
			1000013114, 0.79047619, 0.912, 0.91,
			1000013218, 0.723809524, 0.672, 0.82,
			1000013475, 0.295238095, 0.48, 0.46,
			1000013482, 0.638095238, 0.824, 0.76,
			1000013622, 0.59047619, 0.672, 0.61,
			1000013641, 0.819047619, 0.864, 0.9,
			1000013646, 0.60952381, 0.84, 0.79,
			1000013710, 0.695238095, 0.816, 0.86,
			1000013805, 0.676190476, 0.8, 0.76,
			1000013840, 0.533333333, 0.688, 0.81,
			1000013852, 0.638095238, 0.576, 0.5,
			1000013854, 0.657142857, 0.944, 0.8,
			1000013879, 0.828571429, 0.936, 0.96,
			1000013984, 0.647619048, 0.544, 0.92,
			1000014016, 0.40952381, 0.68, 0.58,
			1000014070, 0.733333333, 0.792, 0.68,
			1000014090, 0.8, 0.856, 0.99,
			1000014287, 0.733333333, 0.912, 0.74,
			1000014298, 0.361904762, 0.656, 0.68,
			1000014556, 0.685714286, 0.752, 0.75,
			1000014569, 0.904761905, 0.76, 0.86,
			1000015034 , 0.361904762, 0.576, 0.65,
			1000015045, 0.742857143, 0.92, 0.92,
			1000015059, 0.495238095, 0.752, 0.58,
			1000015062, 0.59047619, 0.616, 0.66,
			1000015068, 0.676190476, 0.808, 0.84,
			1000015073, 0.571428571, 0.984, 0.86,
			1000015100, 0.876190476, 0.688, 0.94,
			1000015123, 0.828571429, 0.928, 0.94,
			1000015248, 0.838095238, 0.904, 1,
			1000015263, 0.638095238, 0.824, 0.65,
			1000015269, 0.438095238, 0.712, 0.58,
			1000015273, 0.571428571, 0.808, 0.83,
			1000015290, 0.542857143, 0.728, 0.4,
			1000015297, 0.628571429, 0.808, 0.75,
			1000015311, 0.561904762, 0.64, 0.8,
			1000015333, 0.647619048, 0.744, 0.66,
			1000015349, 0.40952381, 0.432, 0.32,
			1000015439, 0.685714286, 0.84, 0.76,
			1000015509, 0.438095238, 0.704, 0.71,
			1000015536, 0.6, 0.576, 0.35,
			1000015553, 0.780952381, 0.912, 1,
			1000015557, 0.761904762, 0.896, 0.81,
			1000015611, 0.447619048, 0.648, 0.55,
			1000015661, 0.638095238, 0.72, 0.76,
			1000015697, 0.380952381, 0.896, 0.78,
			1000015726, 0.619047619, 0.776, 0.75,
			1000015738, 0.628571429, 0.864, 0.75,
			1000015760, 0.485714286, 0.912, 1,
			1000015767, 0.685714286, 0.816, 0.7,
			1000015912, 0.619047619, 0.8, 0.78,
			1000015966, 0.714285714, 0.928, 0.69,
			1000016042, 0.561904762, 0.632, 0.67,
			1000016101, 0.571428571, 0.704, 0.6,
			1000016125, 0.59047619, 0.864, 0.68,
			1000016137, 0.695238095, 0.856, 0.83,
			1000016143, 0.342857143, 0.488, 0.54,
			1000016159, 0.980952381, 0.832, 0.99,
			1000016231, 0.59047619, 0.76, 0.67,
			1000016241, 0.619047619, 0.824, 0.84,
			1000016273, 0.561904762, 0.736, 0.54,
			1000016299, 0.771428571, 0.952, 0.91,
			1000016321, 0.780952381, 0.904, 1,
			1000016351, 0.476190476, 0.568, 0.33,
			1000016357, 0.542857143, 0.848, 0.84,
			1000016428, 0.6, 0.672, 0.67,
			1000016460, 0.714285714, 0.912, 1,
			1000016474, 0.580952381, 0.784, 0.83,
			1000016555, 0.60952381, 0.768, 0.61,
			1000016594, 0.6, 0.656, 0.6,
			1000016641, 0.561904762, 0.68, 0.66,
			1000016672, 0.619047619, 0.776, 0.65,
			1000016694, 0.514285714, 0.792, 0.69,
			1000016727, 0.39047619, 0.536, 0.68,
			1000016824, 0.542857143, 0.56, 0.61,
			1000016870, 0.657142857, 0.904, 0.78,
			1000016935, 0.60952381, 0.736, 0.73,
			1000016953, 0.428571429, 0.72, 0.51,
			1000017078, 0.60952381, 0.72, 0.49,
			1000017110, 0.657142857, 0.864, 0.92,
			1000017152, 0.447619048, 0.416, 0.46,
			1000017163, 0.495238095, 0.848, 0.78,
			1000017182, 0.561904762, 0.696, 0.77,
			1000017283, 0.580952381, 0.832, 0.8,
			1000017324, 0.657142857, 0.784, 0.62,
			1000017442, 0.504761905, 0.608, 0.45,
			1000017451, 0.657142857, 0.872, 0.69,
			1000017454, 0.4, 0.528, 0.54,
			1000017474, 0.571428571, 0.792, 0.75,
			1000017546, 0.60952381, 0.712, 0.59,
			1000017556, 0.704761905, 0.84, 0.95,
			1000017579, 0.552380952, 0.832, 0.58,
			1000017744, 0.923809524, 0.864, 0.99,
			1000017767, 0.876190476, 0.896, 0.81,
			1000017991, 0.504761905, 0.544, 0.5,
			1000018055, 0.60952381, 0.848, 0.82,
			1000018165, 0.704761905, 0.92, 0.89,
			1000018173, 0.628571429, 0.896, 0.93,
			1000018186, 0.771428571, 0.832, 0.81,
			1000018194, 0.59047619, 0.84, 0.75,
			1000018240, 0.523809524, 0.84, 0.78,
			1000018246, 0.580952381, 0.672, 0.75,
			1000018326, 0.542857143, 0.688, 0.66,
			1000018345, 0.542857143, 0.48, 0.49,
			1000018380, 0.580952381, 0.904, 0.75,
			1000018391, 0.60952381, 0.704, 0.74,
			1000018417, 0.447619048, 0.824, 0.82,
			1000018421, 0.466666667, 0.744, 0.47,
			1000018433, 0.59047619, 0.696, 0.81,
			1000018461, 0.59047619, 0.816, 0.73,
			1000018463, 0.4, 0.832, 0.83,
			1000018528, 0.571428571, 0.816, 0.65,
			1000018545, 0.6, 0.872, 0.65,
			1000018551, 0.552380952, 0.68, 0.43,
			1000018567, 0.714285714, 0.928, 0.75,
			1000018622, 0.723809524, 0.904, 0.93,
			1000018629, 0.59047619, 0.824, 0.96,
			1000018630, 0.419047619, 0.576, 0.5,
			1000018631, 0.457142857, 0.632, 0.59,
			1000018676, 0.923809524, 0.96, 0.97,
			1000018684, 0.638095238, 0.856, 0.65,
			1000018702, 0.561904762, 0.84, 0.49,
			1000018730, 0.39047619, 0.792, 0.79,
			1000018753, 0.504761905, 0.688, 0.83,
			1000018758, 0.742857143, 0.608, 0.6,
			1000018918, 0.685714286, 0.824, 0.65,
			1000018943, 0.942857143, 0.824, 1,
			1000018974, 0.571428571, 0.624, 0.5,
			1000018998, 0.476190476, 0.688, 0.47,
			1000019011, 0.619047619, 0.912, 0.88,
			1000019047, 0.561904762, 0.744, 0.92,
			1000019076, 0.39047619, 0.832, 0.57,
			1000019184, 0.40952381, 0.456, 0.4,
			1000019204, 0.676190476, 0.904, 0.66,
			1000019230, 0.476190476, 0.8, 0.58,
			1000019269, 0.628571429, 0.88, 0.81,
			1000019312, 0.8, 0.968, 0.88,
			1000019350, 0.676190476, 0.88, 0.58,
			1000019368, 0.561904762, 0.656, 0.36,
			1000019444, 0.895238095, 0.784, 0.96,
			1000019472, 0.733333333, 0.568, 0.83,
			1000019551, 0.428571429, 0.448, 0.41,
			1000019594, 0.657142857, 0.832, 0.59,
			1000019606, 0.733333333, 0.92, 0.72,
			1000019649, 0.561904762, 0.84, 0.65,
			1000019654, 0.628571429, 0.8, 0.87,
			1000019770, 0.628571429, 0.824, 0.87,
			1000019856, 0.476190476, 0.552, 0.62,
			1000019994, 0.580952381, 0.528, 0.6,
			1000020212, 0.857142857, 0.968, 0.95,
			1000020329, 0.333333333, 0.648, 0.56,
			1000020454, 0.685714286, 0.68, 0.93,
			1000020479, 0.647619048, 0.888, 0.77,
			1000020482, 0.714285714, 0.784, 0.64,
			1000020511, 0.914285714, 0.768, 0.85,
			1000020567, 0.619047619, 0.776, 0.62,
			1000020575, 0.857142857, 0.896, 0.83,
			1000020577, 0.428571429, 0.744, 0.67,
			1000020877, 0.647619048, 0.784, 0.67,
			1000020915, 0.742857143, 0.816, 0.61,
			1000021064, 0.657142857, 0.896, 0.53,
			1000021139, 0.428571429, 0.8, 0.71,
			1000021315, 0.80952381, 0.664, 0.78,
			1000021381, 0.657142857, 0.8, 0.75,
			1000021462, 0.428571429, 0.8, 0.73,
			1000021542, 0.561904762, 0.704, 0.69,
			1000021784, 0.6, 0.696, 0.69,
			100023, 0.552380952, 0.872, 0.89,
			10417, 0.657142857, 0.824, 0.86,
			10487, 0.666666667, 0.792, 0.76,
			10573, 0.857142857, 0.912, 1,
			10579, 0.714285714, 0.744, 0.58,
			10840, 0.723809524, 0.808, 0.92,
			10992, 0.495238095, 0.832, 0.61,
			11004, 0.533333333, 0.72, 0.48,
			11206, 0.571428571, 0.616, 0.54,
			11454, 0.476190476, 0.528, 0.58,
			11508, 0.685714286, 0.816, 0.91,
			11525, 0.752380952, 0.896, 0.94,
			11596, 0.752380952, 0.864, 0.81,
			11726, 0.657142857, 0.656, 1,
			117691, 0.742857143, 0.816, 0.93,
			11841, 0.933333333, 0.952, 1,
			11871, 0.885714286, 0.952, 0.97,
			12028, 0.752380952, 0.832, 0.89,
			120715, 0.571428571, 0.568, 0.49,
			120815, 0.761904762, 0.792, 0.86,
			121115, 0.742857143, 0.592, 0.8,
			12137, 0.704761905, 0.92, 0.76,
			12145, 0.580952381, 0.696, 0.94,
			121815, 0.838095238, 0.856, 0.84,
			123815, 0.819047619, 0.864, 0.98,
			12394, 0.504761905, 0.728, 0.64,
			12489, 0.657142857, 0.928, 0.87,
			12508, 0.466666667, 0.68, 0.49,
			12619, 0.704761905, 0.864, 0.67,
			12642, 0.380952381, 0.736, 0.5,
			12652, 0.266666667, 0.552, 0.54,
			12683, 0.723809524, 0.912, 0.8,
			126915, 0.476190476, 0.528, 0.54,
			12739, 0.447619048, 0.744, 0.5,
			12782015, 0.561904762, 0.504, 0.48,
			12820, 0.866666667, 0.848, 0.91,
			12852, 0.561904762, 0.792, 0.72,
			12885, 0.771428571, 0.784, 0.67,
			12932, 0.447619048, 0.816, 0.67,
			12980, 0.619047619, 0.728, 0.86,
			13009, 0.628571429, 0.744, 0.76,
			13096, 0.638095238, 0.672, 0.69,
			13110, 0.580952381, 0.84, 0.73,
			13141, 0.619047619, 0.744, 0.8,
			13412, 0.457142857, 0.824, 0.6,
			13456, 0.485714286, 0.808, 0.83,
			13482, 0.571428571, 0.632, 0.62,
			1350, 0.647619048, 0.904, 0.84,
			13522, 0.59047619, 0.736, 0.72,
			136016, 0.438095238, 0.512, 0.56,
			13614, 0.885714286, 0.832, 0.95,
			13648, 0.695238095, 0.896, 0.76,
			13675, 0.361904762, 0.688, 0.36,
			13676, 0.485714286, 0.648, 0.64,
			1368, 0.59047619, 0.776, 0.76,
			13685, 0.742857143, 0.88, 0.96,
			136916, 0.6, 0.6, 0.59,
			137216, 0.80952381, 0.776, 0.76,
			137716, 0.638095238, 0.752, 0.54,
			13839, 0.571428571, 0.912, 0.88,
			13853, 0.504761905, 0.688, 0.71,
			13857, 0.761904762, 0.856, 0.9,
			138716, 0.619047619, 0.664, 0.75,
			138816, 0.457142857, 0.688, 0.57,
			13962, 0.685714286, 0.848, 0.81,
			139716, 0.619047619, 0.728, 0.66,
			13973, 0.39047619, 0.808, 0.54,
			1399/16, 0.638095238, 0.664, 0.68,
			140216, 0.895238095, 0.92, 0.96,
			140316, 0.8, 0.824, 0.87,
			14039, 0.695238095, 0.72, 0.79,
			14041, 0.59047619, 0.84, 0.8,
			14050, 0.342857143, 0.344, 0.37,
			14084, 0.666666667, 0.68, 0.76,
			14088, 0.666666667, 0.888, 0.84,
			14109, 0.695238095, 0.792, 0.82,
			141116, 0.419047619, 0.312, 0.22,
			14130, 0.685714286, 0.88, 0.74,
			141516, 0.4, 0.488, 0.36,
			141716, 0.752380952, 0.864, 0.94,
			14173, 0.780952381, 0.728, 0.94,
			14175, 0.676190476, 0.752, 0.93,
			142016, 0.771428571, 0.832, 0.97,
			142116, 0.619047619, 0.656, 0.56,
			14250, 0.514285714, 0.608, 0.75,
			142516, 0.666666667, 0.608, 0.7,
			142616, 0.495238095, 0.696, 0.76,
			14270, 0.552380952, 0.768, 0.71,
			14304, 0.542857143, 0.64, 0.7,
			14323, 0.447619048, 0.728, 0.72,
			143316, 0.704761905, 0.84, 0.96,
			14359, 0.752380952, 0.704, 0.76,
			14363, 0.79047619, 0.784, 0.85,
			14364, 0.419047619, 0.392, 0.41,
			14371, 0.580952381, 0.68, 0.56,
			14391, 0.380952381, 0.56, 0.61,
			14498, 0.542857143, 0.824, 0.77,
			14574, 0.895238095, 0.976, 0.91,
			14588, 0.504761905, 0.6, 0.47,
			14618, 0.80952381, 0.816, 0.9,
			14639, 0.828571429, 0.896, 0.94,
			14688, 0.657142857, 0.744, 0.74,
			15006, 0.428571429, 0.616, 0.6,
			15016, 0.657142857, 0.688, 0.56,
			15022, 0.533333333, 0.792, 0.82,
			15064, 0.447619048, 0.752, 0.49,
			15067, 0.466666667, 0.688, 0.46,
			15117, 0.647619048, 0.76, 0.83,
			15161, 0.419047619, 0.576, 0.6,
			15177, 0.504761905, 0.648, 0.85,
			15182, 0.771428571, 0.736, 0.77,
			15196, 0.819047619, 0.92, 0.81,
			15241, 0.6, 0.888, 0.97,
			15251, 0.914285714, 0.92, 0.91,
			15267, 0.628571429, 0.672, 0.82,
			15290, 0.371428571, 0.72, 0.76,
			15329, 0.857142857, 0.848, 0.94,
			15332, 0.80952381, 0.912, 0.92,
			15362, 0.647619048, 0.832, 0.9,
			15365, 0.838095238, 0.792, 0.75,
			15425, 0.552380952, 0.832, 0.98,
			15472, 0.676190476, 0.856, 0.79,
			15633, 0.79047619, 0.864, 0.65,
			15697, 0.657142857, 0.872, 0.94,
			15736, 0.733333333, 0.704, 0.88,
			15760, 0.695238095, 0.968, 0.94,
			15806, 0.495238095, 0.536, 0.64,
			15833, 0.6, 0.792, 0.83,
			15849, 0.771428571, 0.856, 0.62,
			15865, 0.59047619, 0.72, 0.6,
			15916, 0.828571429, 0.872, 0.92,
			15926, 0.60952381, 0.744, 0.72,
			15966, 0.59047619, 0.808, 0.64,
			15988, 0.676190476, 0.712, 0.68,
			16020, 0.723809524, 0.704, 0.74,
			16052, 0.552380952, 0.72, 0.7,
			16103, 0.504761905, 0.68, 0.27,
			16104, 0.771428571, 0.728, 0.7,
			16146, 0.476190476, 0.712, 0.65,
			16189, 0.657142857, 0.64, 0.58,
			16191, 0.619047619, 0.872, 0.82,
			16210, 0.40952381, 0.592, 0.65,
			16240, 0.704761905, 0.888, 0.96,
			16267, 0.428571429, 0.688, 0.68,
			16278, 0.666666667, 0.8, 0.58,
			16291, 0.485714286, 0.904, 0.78,
			16322, 0.704761905, 0.792, 1,
			16354, 0.695238095, 0.824, 0.72,
			16360, 0.352380952, 0.344, 0.28,
			16379, 0.828571429, 0.888, 0.92,
			16390, 0.838095238, 0.784, 0.84,
			16423, 0.657142857, 0.76, 0.65,
			16442, 0.628571429, 0.864, 0.87,
			16559, 0.457142857, 0.736, 0.48,
			16602, 0.828571429, 0.816, 0.84,
			16618, 0.647619048, 0.736, 0.55,
			16630, 0.676190476, 0.552, 0.58,
			16638, 0.695238095, 0.68, 0.73,
			16694, 0.619047619, 0.944, 0.95,
			16714, 0.657142857, 0.512, 0.9,
			16716, 0.6, 0.76, 0.86,
			16767, 0.514285714, 0.728, 0.77,
			16782, 0.352380952, 0.88, 0.73,
			16816, 0.714285714, 0.8, 1,
			16824, 0.542857143, 0.672, 0.76,
			16844, 0.571428571, 0.736, 0.76,
			16862, 0.6, 0.752, 0.86,
			16895, 0.657142857, 0.672, 0.75,
			16913, 0.6, 0.6, 0.73,
			16973, 0.4, 0.72, 0.59,
			16974, 0.571428571, 0.896, 0.77,
			17017, 0.771428571, 0.816, 0.79,
			17021, 0.580952381, 0.688, 0.6,
			17043, 0.771428571, 0.904, 0.89,
			17046, 0.438095238, 0.648, 0.62,
			17079, 0.638095238, 0.848, 0.9,
			17110, 0.552380952, 0.856, 0.85,
			17121, 0.361904762, 0.48, 0.52,
			17197, 0.571428571, 0.912, 0.98,
			17217, 0.847619048, 0.928, 0.96,
			17227, 0.79047619, 0.896, 0.99,
			17249, 0.904761905, 0.84, 0.68,
			17327, 0.571428571, 0.752, 0.63,
			17336, 0.466666667, 0.512, 0.46,
			17423, 0.40952381, 0.624, 0.37,
			17425, 0.504761905, 0.816, 0.76,
			17429, 0.733333333, 0.824, 0.85,
			17432, 0.514285714, 0.752, 0.48,
			17437, 0.628571429, 0.752, 0.87,
			17460, 0.8, 0.752, 0.85,
			17501, 0.714285714, 0.728, 0.73,
			17547, 0.685714286, 0.72, 0.74,
			17562, 0.39047619, 0.696, 0.48,
			17704, 0.580952381, 0.664, 0.65,
			17712, 0.828571429, 0.912, 0.99,
			17759, 0.676190476, 0.856, 0.5,
			17786, 0.761904762, 0.888, 0.96,
			17991, 0.59047619, 0.52, 0.72,
			18011, 0.447619048, 0.776, 0.8,
			18012, 0.466666667, 0.664, 0.46,
			18029, 0.752380952, 0.848, 0.94,
			18040, 0.666666667, 0.864, 0.77,
			18058, 0.542857143, 0.544, 0.49,
			18089, 0.438095238, 0.488, 0.45,
			18111, 0.466666667, 0.888, 0.81,
			18121, 0.666666667, 0.808, 0.74,
			18173, 0.59047619, 0.8, 0.75,
			18181, 0.60952381, 0.888, 0.84,
			18206, 0.438095238, 0.76, 0.59,
			18221, 0.419047619, 0.624, 0.55,
			18228, 0.847619048, 0.976, 0.93,
			18244, 0.580952381, 0.704, 0.75,
			18250, 0.523809524, 0.784, 0.81,
			18258, 0.476190476, 0.592, 0.55,
			18276, 0.80952381, 0.848, 0.96,
			18304, 0.533333333, 0.688, 0.74,
			18307, 0.542857143, 0.728, 0.94,
			18313, 0.761904762, 0.872, 0.92,
			18363, 0.79047619, 0.952, 0.84,
			18369, 0.8, 0.816, 1,
			18371, 0.342857143, 0.592, 0.37,
			18374, 0.39047619, 0.592, 0.66,
			18381, 0.676190476, 0.832, 0.85,
			18391, 0.419047619, 0.672, 0.78,
			18408, 0.580952381, 0.592, 0.62,
			18417, 0.40952381, 0.632, 0.61,
			18421, 0.495238095, 0.792, 0.63,
			18433, 0.580952381, 0.648, 0.79,
			18434, 0.657142857, 0.688, 0.64,
			18435, 0.857142857, 0.84, 0.94,
			18461, 0.628571429, 0.84, 0.64,
			18478, 0.571428571, 0.952, 0.88,
			18489, 0.828571429, 0.824, 0.86,
			18493, 0.628571429, 0.632, 0.64,
			18500, 0.342857143, 0.72, 0.55,
			18504, 0.828571429, 0.864, 0.91,
			18507, 0.866666667, 0.88, 0.91,
			18552, 0.619047619, 0.752, 0.56,
			18556, 0.504761905, 0.616, 0.42,
			18560 , 0.647619048, 0.776, 0.9,
			18588, 0.676190476, 0.952, 0.95,
			18591, 0.580952381, 0.824, 0.53,
			18607, 0.533333333, 0.744, 0.86,
			18610, 0.571428571, 0.688, 0.56,
			18621, 0.857142857, 0.888, 0.95,
			18649, 0.780952381, 0.912, 0.97,
			18684, 0.60952381, 0.88, 0.73,
			18697, 0.571428571, 0.776, 0.76,
			18705, 0.761904762, 0.888, 0.91,
			18767, 0.457142857, 0.616, 0.75,
			18797, 0.580952381, 0.848, 0.84,
			18806, 0.542857143, 0.72, 0.83,
			18814, 0.857142857, 0.8, 0.93,
			18825, 0.838095238, 0.8, 0.94,
			18856, 0.59047619, 0.776, 0.7,
			18894, 0.552380952, 0.864, 0.79,
			18909, 0.657142857, 0.72, 0.78,
			18912, 0.80952381, 0.816, 0.84,
			18918, 0.571428571, 0.728, 0.75,
			18940, 0.819047619, 0.776, 0.91,
			18943, 0.904761905, 0.92, 0.83,
			18955, 0.533333333, 0.744, 0.69,
			18974, 0.466666667, 0.68, 0.54,
			19003, 0.304761905, 0.408, 0.36,
			19047, 0.504761905, 0.744, 0.88,
			19056, 0.742857143, 0.896, 0.71,
			19073, 0.628571429, 0.704, 0.76,
			19090, 0.457142857, 0.664, 0.4,
			19116, 0.552380952, 0.512, 0.8,
			19125, 0.466666667, 0.752, 0.85,
			19175, 0.752380952, 0.792, 0.95,
			19178, 0.60952381, 0.592, 0.48,
			19180, 0.904761905, 0.936, 1,
			19199, 0.923809524, 0.936, 1,
			19204444444, 0.847619048, 0.92, 0.91,
			19230, 0.60952381, 0.72, 0.64,
			19234, 0.428571429, 0.336, 0.27,
			19282, 0.971428571, 0.848, 0.94,
			19291, 0.428571429, 0.472, 0.71,
			19298, 0.552380952, 0.552, 0.6,
			19299, 0.685714286, 0.744, 0.88,
			19313, 0.647619048, 0.848, 0.84,
			19322, 0.60952381, 0.728, 0.55,
			19333, 0.504761905, 0.68, 0.59,
			19343, 0.771428571, 0.736, 0.77,
			19350, 0.666666667, 0.768, 0.71,
			19351, 0.457142857, 0.824, 0.76,
			19430, 0.723809524, 0.792, 0.79,
			19487, 0.523809524, 0.744, 0.7,
			19490, 0.561904762, 0.688, 0.69,
			19519, 0.857142857, 0.88, 0.8,
			19554, 0.514285714, 0.616, 0.9,
			19565, 0.819047619, 0.768, 0.96,
			19606, 0.752380952, 0.872, 0.79,
			19647, 0.676190476, 0.928, 0.89,
			19707, 0.8, 0.872, 0.92,
			19774, 0.828571429, 0.912, 0.91,
			19788, 0.723809524, 0.872, 0.86,
			19802, 0.857142857, 0.864, 0.95,
			19828, 0.761904762, 0.72, 0.78,
			19839, 0.685714286, 0.768, 0.64,
			19856, 0.419047619, 0.72, 0.69,
			19937, 0.723809524, 0.896, 0.91,
			19961, 0.59047619, 0.792, 0.71,
			20250, 0.771428571, 0.912, 0.99,
			20333, 0.752380952, 0.832, 0.86,
			20395, 0.59047619, 0.72, 0.79,
			20474, 0.561904762, 0.808, 0.76,
			20727, 0.657142857, 0.888, 0.77,
			20739, 0.752380952, 0.896, 0.9,
			20876, 0.676190476, 0.92, 0.97,
			21099, 0.552380952, 0.752, 0.72,
			21173, 0.847619048, 0.976, 0.99,
			21793, 0.485714286, 0.456, 0.6,
			22113, 0.571428571, 0.72, 0.53,
			3514, 0.895238095, 0.912, 0.96,
			4, 0.942857143, 0.96, 0.97,
			5802, 0.495238095, 0.728, 0.72,
			6, 0.380952381, 0.896, 0.44,
			6530, 0.838095238, 0.88, 0.9,
			7094, 0.80952381, 0.776, 0.86,
			7301, 0.580952381, 0.792, 0.72,
			7459, 0.647619048, 0.872, 0.94,
			8070, 0.495238095, 0.664, 0.59,
			8099, 0.419047619, 0.72, 0.61,
			9261, 0.447619048, 0.576, 0.6,
			9280, 0.733333333, 0.904, 0.9,
			9632, 0.714285714, 0.864, 0.86,
			971, 0.714285714, 0.768, 0.7,
			9882, 0.80952381, 0.856, 0.93,
			9996, 0.8, 0.872, 0.89,
			1000013060, 0.838095238, 0.736, 0.8,
			1000014719, 0.571428571, 0.616, 0.37,
			1000015138, 0.657142857, 0.872, 0.71,
			1000015605, 0.523809524, 0.632, 0.77,
			1000015692, 0.752380952, 0.872, 0.73,
			1000015834, 0.580952381, 0.768, 0.81,
			1000016164, 0.39047619, 0.624, 0.41,
			1000016453, 0.8, 0.904, 0.94,
			1000016932, 0.647619048, 0.84, 0.56,
			1000016974, 0.428571429, 0.688, 0.68,
			1000017327, 0.476190476, 0.648, 0.66,
			1000018172, 0.561904762, 0.928, 0.85,
			1000018643, 0.942857143, 0.912, 0.98,
			1000019249, 0.638095238, 0.632, 0.75,
			1000019533, 0.733333333, 0.856, 0.89,
			1000019881, 0.723809524, 0.864, 0.68,
			1000020440, 0.638095238, 0.896, 0.81,
			1000020594, 0.361904762, 0.68, 0.53,
			1000021008, 0.933333333, 0.952, 1,
			1000021117, 0.8, 0.768, 0.84,
			1000021819, 0.6, 0.8, 0.75,
			1000022002, 0.59047619, 0.752, 0.54
		]
	},

	getStudentData: function(id) {
		// Get the student data async. The callback function is openStudentPopup(data).

		// For testing purpose, gonna call the openStudentPopup(data).
		this.openStudentPopup({id: id, name: "Arlindo Almada", note: "Esta aluno está contudo em cima!"});
	},

	openStudentPopup: function(data) {
		// Converta o data para js object no formato {id: data.id, name: data.name, note: data.note}
		var jsData = {id: data.id, name: data.name, note: data.note};

		machineCouch.machineCouchWorld.showPopup(jsData);
	}
}