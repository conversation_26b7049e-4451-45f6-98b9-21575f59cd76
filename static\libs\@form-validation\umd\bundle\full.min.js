/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 * 
 * @license https://formvalidation.io/license
 * @package @form-validation/bundle
 * @version 2.4.0
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).FormValidation={})}(this,(function(e){"use strict";var t,r={exports:{}},a={};r.exports=function(){if(t)return a;t=1;var e={luhn:function(e){for(var t=e.length,r=[[0,1,2,3,4,5,6,7,8,9],[0,2,4,6,8,1,3,5,7,9]],a=0,n=0;t--;)n+=r[a][parseInt(e.charAt(t),10)],a=1-a;return n%10==0&&n>0},mod11And10:function(e){for(var t=e.length,r=5,a=0;a<t;a++)r=(2*(r||10)%11+parseInt(e.charAt(a),10))%10;return 1===r},mod37And36:function(e,t){void 0===t&&(t="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ");for(var r=e.length,a=t.length,n=Math.floor(a/2),i=0;i<r;i++)n=(2*(n||a)%(a+1)+t.indexOf(e.charAt(i)))%a;return 1===n},mod97And10:function(e){for(var t=function(e){return e.split("").map((function(e){var t=e.charCodeAt(0);return t>=65&&t<=90?t-55:e})).join("").split("").map((function(e){return parseInt(e,10)}))}(e),r=0,a=t.length,n=0;n<a-1;++n)r=10*(r+t[n])%97;return(r+=t[a-1])%97==1},verhoeff:function(e){for(var t=[[0,1,2,3,4,5,6,7,8,9],[1,2,3,4,0,6,7,8,9,5],[2,3,4,0,1,7,8,9,5,6],[3,4,0,1,2,8,9,5,6,7],[4,0,1,2,3,9,5,6,7,8],[5,9,8,7,6,0,4,3,2,1],[6,5,9,8,7,1,0,4,3,2],[7,6,5,9,8,2,1,0,4,3],[8,7,6,5,9,3,2,1,0,4],[9,8,7,6,5,4,3,2,1,0]],r=[[0,1,2,3,4,5,6,7,8,9],[1,5,7,6,2,8,3,0,9,4],[5,8,0,3,7,9,6,1,4,2],[8,9,1,6,0,4,3,5,2,7],[9,4,5,3,1,2,6,8,7,0],[4,2,8,6,5,7,3,9,0,1],[2,7,9,3,8,0,6,4,1,5],[7,0,4,6,9,1,3,2,5,8]],a=e.reverse(),n=0,i=0;i<a.length;i++)n=t[n][r[i%8][a[i]]];return 0===n}},r=function(){function e(e,t){this.fields={},this.elements={},this.ee={fns:{},clear:function(){this.fns={}},emit:function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];(this.fns[e]||[]).map((function(e){return e.apply(e,t)}))},off:function(e,t){if(this.fns[e]){var r=this.fns[e].indexOf(t);r>=0&&this.fns[e].splice(r,1)}},on:function(e,t){(this.fns[e]=this.fns[e]||[]).push(t)}},this.filter={filters:{},add:function(e,t){(this.filters[e]=this.filters[e]||[]).push(t)},clear:function(){this.filters={}},execute:function(e,t,r){if(!this.filters[e]||!this.filters[e].length)return t;for(var a=t,n=this.filters[e],i=n.length,s=0;s<i;s++)a=n[s].apply(a,r);return a},remove:function(e,t){this.filters[e]&&(this.filters[e]=this.filters[e].filter((function(e){return e!==t})))}},this.plugins={},this.results=new Map,this.validators={},this.form=e,this.fields=t}return e.prototype.on=function(e,t){return this.ee.on(e,t),this},e.prototype.off=function(e,t){return this.ee.off(e,t),this},e.prototype.emit=function(e){for(var t,r=[],a=1;a<arguments.length;a++)r[a-1]=arguments[a];return(t=this.ee).emit.apply(t,function(e,t,r){if(r||2===arguments.length)for(var a,n=0,i=t.length;n<i;n++)!a&&n in t||(a||(a=Array.prototype.slice.call(t,0,n)),a[n]=t[n]);return e.concat(a||Array.prototype.slice.call(t))}([e],r,!1)),this},e.prototype.registerPlugin=function(e,t){if(this.plugins[e])throw new Error("The plguin ".concat(e," is registered"));return t.setCore(this),t.install(),this.plugins[e]=t,this},e.prototype.deregisterPlugin=function(e){var t=this.plugins[e];return t&&t.uninstall(),delete this.plugins[e],this},e.prototype.enablePlugin=function(e){var t=this.plugins[e];return t&&t.enable(),this},e.prototype.disablePlugin=function(e){var t=this.plugins[e];return t&&t.disable(),this},e.prototype.isPluginEnabled=function(e){var t=this.plugins[e];return!!t&&t.isPluginEnabled()},e.prototype.registerValidator=function(e,t){if(this.validators[e])throw new Error("The validator ".concat(e," is registered"));return this.validators[e]=t,this},e.prototype.registerFilter=function(e,t){return this.filter.add(e,t),this},e.prototype.deregisterFilter=function(e,t){return this.filter.remove(e,t),this},e.prototype.executeFilter=function(e,t,r){return this.filter.execute(e,t,r)},e.prototype.addField=function(e,t){var r=Object.assign({},{selector:"",validators:{}},t);return this.fields[e]=this.fields[e]?{selector:r.selector||this.fields[e].selector,validators:Object.assign({},this.fields[e].validators,r.validators)}:r,this.elements[e]=this.queryElements(e),this.emit("core.field.added",{elements:this.elements[e],field:e,options:this.fields[e]}),this},e.prototype.removeField=function(e){if(!this.fields[e])throw new Error("The field ".concat(e," validators are not defined. Please ensure the field is added first"));var t=this.elements[e],r=this.fields[e];return delete this.elements[e],delete this.fields[e],this.emit("core.field.removed",{elements:t,field:e,options:r}),this},e.prototype.validate=function(){var e=this;return this.emit("core.form.validating",{formValidation:this}),this.filter.execute("validate-pre",Promise.resolve(),[]).then((function(){return Promise.all(Object.keys(e.fields).map((function(t){return e.validateField(t)}))).then((function(t){switch(!0){case-1!==t.indexOf("Invalid"):return e.emit("core.form.invalid",{formValidation:e}),Promise.resolve("Invalid");case-1!==t.indexOf("NotValidated"):return e.emit("core.form.notvalidated",{formValidation:e}),Promise.resolve("NotValidated");default:return e.emit("core.form.valid",{formValidation:e}),Promise.resolve("Valid")}}))}))},e.prototype.validateField=function(e){var t=this,r=this.results.get(e);if("Valid"===r||"Invalid"===r)return Promise.resolve(r);this.emit("core.field.validating",e);var a=this.elements[e];if(0===a.length)return this.emit("core.field.valid",e),Promise.resolve("Valid");var n=a[0].getAttribute("type");return"radio"===n||"checkbox"===n||1===a.length?this.validateElement(e,a[0]):Promise.all(a.map((function(r){return t.validateElement(e,r)}))).then((function(r){switch(!0){case-1!==r.indexOf("Invalid"):return t.emit("core.field.invalid",e),t.results.set(e,"Invalid"),Promise.resolve("Invalid");case-1!==r.indexOf("NotValidated"):return t.emit("core.field.notvalidated",e),t.results.delete(e),Promise.resolve("NotValidated");default:return t.emit("core.field.valid",e),t.results.set(e,"Valid"),Promise.resolve("Valid")}}))},e.prototype.validateElement=function(e,t){var r=this;this.results.delete(e);var a=this.elements[e];if(this.filter.execute("element-ignored",!1,[e,t,a]))return this.emit("core.element.ignored",{element:t,elements:a,field:e}),Promise.resolve("Ignored");var n=this.fields[e].validators;this.emit("core.element.validating",{element:t,elements:a,field:e});var i=Object.keys(n).map((function(a){return function(){return r.executeValidator(e,t,a,n[a])}}));return this.waterfall(i).then((function(n){var i=-1===n.indexOf("Invalid");r.emit("core.element.validated",{element:t,elements:a,field:e,valid:i});var s=t.getAttribute("type");return"radio"!==s&&"checkbox"!==s&&1!==a.length||r.emit(i?"core.field.valid":"core.field.invalid",e),Promise.resolve(i?"Valid":"Invalid")})).catch((function(n){return r.emit("core.element.notvalidated",{element:t,elements:a,field:e}),Promise.resolve(n)}))},e.prototype.executeValidator=function(e,t,r,a){var n=this,i=this.elements[e],s=this.filter.execute("validator-name",r,[r,e]);if(a.message=this.filter.execute("validator-message",a.message,[this.locale,e,s]),!this.validators[s]||!1===a.enabled)return this.emit("core.validator.validated",{element:t,elements:i,field:e,result:this.normalizeResult(e,s,{valid:!0}),validator:s}),Promise.resolve("Valid");var o=this.validators[s],l=this.getElementValue(e,t,s);if(!this.filter.execute("field-should-validate",!0,[e,t,l,r]))return this.emit("core.validator.notvalidated",{element:t,elements:i,field:e,validator:r}),Promise.resolve("NotValidated");this.emit("core.validator.validating",{element:t,elements:i,field:e,validator:r});var d=o().validate({element:t,elements:i,field:e,l10n:this.localization,options:a,value:l});if("function"==typeof d.then)return d.then((function(a){var s=n.normalizeResult(e,r,a);return n.emit("core.validator.validated",{element:t,elements:i,field:e,result:s,validator:r}),s.valid?"Valid":"Invalid"}));var u=this.normalizeResult(e,r,d);return this.emit("core.validator.validated",{element:t,elements:i,field:e,result:u,validator:r}),Promise.resolve(u.valid?"Valid":"Invalid")},e.prototype.getElementValue=function(e,t,r){var a=function(e,t,r,a){var n=(r.getAttribute("type")||"").toLowerCase(),i=r.tagName.toLowerCase();if("textarea"===i)return r.value;if("select"===i){var s=r,o=s.selectedIndex;return o>=0?s.options.item(o).value:""}if("input"===i){if("radio"===n||"checkbox"===n){var l=a.filter((function(e){return e.checked})).length;return 0===l?"":l+""}return r.value}return""}(this.form,0,t,this.elements[e]);return this.filter.execute("field-value",a,[a,e,t,r])},e.prototype.getElements=function(e){return this.elements[e]},e.prototype.getFields=function(){return this.fields},e.prototype.getFormElement=function(){return this.form},e.prototype.getLocale=function(){return this.locale},e.prototype.getPlugin=function(e){return this.plugins[e]},e.prototype.updateFieldStatus=function(e,t,r){var a=this,n=this.elements[e],i=n[0].getAttribute("type");if(("radio"===i||"checkbox"===i?[n[0]]:n).forEach((function(n){return a.updateElementStatus(e,n,t,r)})),r)"Invalid"===t&&(this.emit("core.field.invalid",e),this.results.set(e,"Invalid"));else switch(t){case"NotValidated":this.emit("core.field.notvalidated",e),this.results.delete(e);break;case"Validating":this.emit("core.field.validating",e),this.results.delete(e);break;case"Valid":this.emit("core.field.valid",e),this.results.set(e,"Valid");break;case"Invalid":this.emit("core.field.invalid",e),this.results.set(e,"Invalid")}return this},e.prototype.updateElementStatus=function(e,t,r,a){var n=this,i=this.elements[e],s=this.fields[e].validators,o=a?[a]:Object.keys(s);switch(r){case"NotValidated":o.forEach((function(r){return n.emit("core.validator.notvalidated",{element:t,elements:i,field:e,validator:r})})),this.emit("core.element.notvalidated",{element:t,elements:i,field:e});break;case"Validating":o.forEach((function(r){return n.emit("core.validator.validating",{element:t,elements:i,field:e,validator:r})})),this.emit("core.element.validating",{element:t,elements:i,field:e});break;case"Valid":o.forEach((function(r){return n.emit("core.validator.validated",{element:t,elements:i,field:e,result:{message:s[r].message,valid:!0},validator:r})})),this.emit("core.element.validated",{element:t,elements:i,field:e,valid:!0});break;case"Invalid":o.forEach((function(r){return n.emit("core.validator.validated",{element:t,elements:i,field:e,result:{message:s[r].message,valid:!1},validator:r})})),this.emit("core.element.validated",{element:t,elements:i,field:e,valid:!1})}return this},e.prototype.resetForm=function(e){var t=this;return Object.keys(this.fields).forEach((function(r){return t.resetField(r,e)})),this.emit("core.form.reset",{formValidation:this,reset:e}),this},e.prototype.resetField=function(e,t){if(t){var r=this.elements[e],a=r[0].getAttribute("type");r.forEach((function(e){"radio"===a||"checkbox"===a?(e.removeAttribute("selected"),e.removeAttribute("checked"),e.checked=!1):(e.setAttribute("value",""),(e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement)&&(e.value=""))}))}return this.updateFieldStatus(e,"NotValidated"),this.emit("core.field.reset",{field:e,reset:t}),this},e.prototype.revalidateField=function(e){return this.fields[e]?(this.updateFieldStatus(e,"NotValidated"),this.validateField(e)):Promise.resolve("Ignored")},e.prototype.disableValidator=function(e,t){if(!this.fields[e])return this;var r=this.elements[e];return this.toggleValidator(!1,e,t),this.emit("core.validator.disabled",{elements:r,field:e,formValidation:this,validator:t}),this},e.prototype.enableValidator=function(e,t){if(!this.fields[e])return this;var r=this.elements[e];return this.toggleValidator(!0,e,t),this.emit("core.validator.enabled",{elements:r,field:e,formValidation:this,validator:t}),this},e.prototype.updateValidatorOption=function(e,t,r,a){return this.fields[e]&&this.fields[e].validators&&this.fields[e].validators[t]&&(this.fields[e].validators[t][r]=a),this},e.prototype.setFieldOptions=function(e,t){return this.fields[e]=t,this},e.prototype.destroy=function(){var e=this;return Object.keys(this.plugins).forEach((function(t){return e.plugins[t].uninstall()})),this.ee.clear(),this.filter.clear(),this.results.clear(),this.plugins={},this},e.prototype.setLocale=function(e,t){return this.locale=e,this.localization=t,this},e.prototype.waterfall=function(e){return e.reduce((function(e,t){return e.then((function(e){return t().then((function(t){return e.push(t),e}))}))}),Promise.resolve([]))},e.prototype.queryElements=function(e){var t=this.fields[e].selector?"#"===this.fields[e].selector.charAt(0)?'[id="'.concat(this.fields[e].selector.substring(1),'"]'):this.fields[e].selector:'[name="'.concat(e.replace(/"/g,'\\"'),'"]');return[].slice.call(this.form.querySelectorAll(t))},e.prototype.normalizeResult=function(e,t,r){var a=this.fields[e].validators[t];return Object.assign({},r,{message:r.message||(a?a.message:"")||(this.localization&&this.localization[t]&&this.localization[t].default?this.localization[t].default:"")||"The field ".concat(e," is not valid")})},e.prototype.toggleValidator=function(e,t,r){var a=this,n=this.fields[t].validators;return r&&n&&n[r]?this.fields[t].validators[r].enabled=e:r||Object.keys(n).forEach((function(r){return a.fields[t].validators[r].enabled=e})),this.updateFieldStatus(t,"NotValidated",r)},e}(),n=function(){function e(e){this.opts=e,this.isEnabled=!0}return e.prototype.setCore=function(e){return this.core=e,this},e.prototype.enable=function(){return this.isEnabled=!0,this.onEnabled(),this},e.prototype.disable=function(){return this.isEnabled=!1,this.onDisabled(),this},e.prototype.isPluginEnabled=function(){return this.isEnabled},e.prototype.onEnabled=function(){},e.prototype.onDisabled=function(){},e.prototype.install=function(){},e.prototype.uninstall=function(){},e}(),i=function(e,t){var r=e.matches||e.webkitMatchesSelector||e.mozMatchesSelector||e.msMatchesSelector;return r?r.call(e,t):[].slice.call(e.parentElement.querySelectorAll(t)).indexOf(e)>=0},s={call:function(e,t){if("function"==typeof e)return e.apply(this,t);if("string"==typeof e){var r=e;"()"===r.substring(r.length-2)&&(r=r.substring(0,r.length-2));for(var a=r.split("."),n=a.pop(),i=window,s=0,o=a;s<o.length;s++)i=i[o[s]];return void 0===i[n]?null:i[n].apply(this,t)}},classSet:function(e,t){var r=[],a=[];Object.keys(t).forEach((function(e){e&&(t[e]?r.push(e):a.push(e))})),a.forEach((function(t){return function(e,t){t.split(" ").forEach((function(t){e.classList?e.classList.remove(t):e.className=e.className.replace(t,"")}))}(e,t)})),r.forEach((function(t){return function(e,t){t.split(" ").forEach((function(t){e.classList?e.classList.add(t):" ".concat(e.className," ").indexOf(" ".concat(t," "))&&(e.className+=" ".concat(t))}))}(e,t)}))},closest:function(e,t){for(var r=e;r&&!i(r,t);)r=r.parentElement;return r},fetch:function(e,t){return new Promise((function(r,a){var n,i=Object.assign({},{crossDomain:!1,headers:{},method:"GET",params:{}},t),s=Object.keys(i.params).map((function(e){return"".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(i.params[e]))})).join("&"),o=e.indexOf("?")>-1,l="GET"===i.method?"".concat(e).concat(o?"&":"?").concat(s):e;if(i.crossDomain){var d=document.createElement("script"),u="___FormValidationFetch_".concat(Array(12).fill("").map((function(e){return Math.random().toString(36).charAt(2)})).join(""),"___");window[u]=function(e){delete window[u],r(e)},d.src="".concat(l).concat(o?"&":"?","callback=").concat(u),d.async=!0,d.addEventListener("load",(function(){d.parentNode.removeChild(d)})),d.addEventListener("error",(function(){return a})),document.head.appendChild(d)}else{var c=new XMLHttpRequest;c.open(i.method,l),c.setRequestHeader("X-Requested-With","XMLHttpRequest"),"POST"===i.method&&c.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),Object.keys(i.headers).forEach((function(e){return c.setRequestHeader(e,i.headers[e])})),c.addEventListener("load",(function(){r(JSON.parse(this.responseText))})),c.addEventListener("error",(function(){return a})),c.send((n=i.params,Object.keys(n).map((function(e){return"".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(n[e]))})).join("&")))}}))},format:function(e,t){var r=Array.isArray(t)?t:[t],a=e;return r.forEach((function(e){a=a.replace("%s",e)})),a},hasClass:function(e,t){return e.classList?e.classList.contains(t):new RegExp("(^| )".concat(t,"( |$)"),"gi").test(e.className)},isValidDate:function(e,t,r,a){if(isNaN(e)||isNaN(t)||isNaN(r))return!1;if(e<1e3||e>9999||t<=0||t>12)return!1;if(r<=0||r>[31,e%400==0||e%100!=0&&e%4==0?29:28,31,30,31,30,31,31,30,31,30,31][t-1])return!1;if(!0===a){var n=new Date,i=n.getFullYear(),s=n.getMonth(),o=n.getDate();return e<i||e===i&&t-1<s||e===i&&t-1===s&&r<o}return!0},removeUndefined:function(e){return e?Object.entries(e).reduce((function(e,t){var r=t[0],a=t[1];return void 0===a||(e[r]=a),e}),{}):{}}};return a.Plugin=n,a.algorithms=e,a.formValidation=function(e,t){var a=Object.assign({},{fields:{},locale:"en_US",plugins:{},init:function(e){}},t),n=new r(e,a.fields);return n.setLocale(a.locale,a.localization),Object.keys(a.plugins).forEach((function(e){return n.registerPlugin(e,a.plugins[e])})),a.init(n),Object.keys(a.fields).forEach((function(e){return n.addField(e,a.fields[e])})),n},a.utils=s,a}();var n,i=r.exports,s={exports:{}},o={};s.exports=function(){if(n)return o;n=1;var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)},t=function(t){function r(e){var r=t.call(this,e)||this;return r.opts=e||{},r.validatorNameFilter=r.getValidatorName.bind(r),r}return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function a(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}(r,t),r.prototype.install=function(){this.core.registerFilter("validator-name",this.validatorNameFilter)},r.prototype.uninstall=function(){this.core.deregisterFilter("validator-name",this.validatorNameFilter)},r.prototype.getValidatorName=function(e,t){return this.isEnabled&&this.opts[e]||e},r}(i.Plugin);return o.Alias=t,o}();var l,d=s.exports,u={exports:{}},c={};u.exports=function(){if(l)return c;l=1;var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)},t=function(t){function r(){var e=t.call(this,{})||this;return e.elementValidatedHandler=e.onElementValidated.bind(e),e.fieldValidHandler=e.onFieldValid.bind(e),e.fieldInvalidHandler=e.onFieldInvalid.bind(e),e.messageDisplayedHandler=e.onMessageDisplayed.bind(e),e}return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function a(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}(r,t),r.prototype.install=function(){this.core.on("core.field.valid",this.fieldValidHandler).on("core.field.invalid",this.fieldInvalidHandler).on("core.element.validated",this.elementValidatedHandler).on("plugins.message.displayed",this.messageDisplayedHandler)},r.prototype.uninstall=function(){this.core.off("core.field.valid",this.fieldValidHandler).off("core.field.invalid",this.fieldInvalidHandler).off("core.element.validated",this.elementValidatedHandler).off("plugins.message.displayed",this.messageDisplayedHandler)},r.prototype.onElementValidated=function(e){e.valid&&(e.element.setAttribute("aria-invalid","false"),e.element.removeAttribute("aria-describedby"))},r.prototype.onFieldValid=function(e){var t=this.core.getElements(e);t&&t.forEach((function(e){e.setAttribute("aria-invalid","false"),e.removeAttribute("aria-describedby")}))},r.prototype.onFieldInvalid=function(e){var t=this.core.getElements(e);t&&t.forEach((function(e){return e.setAttribute("aria-invalid","true")}))},r.prototype.onMessageDisplayed=function(e){e.messageElement.setAttribute("role","alert"),e.messageElement.setAttribute("aria-hidden","false");var t=this.core.getElements(e.field),r=t.indexOf(e.element),a="js-fv-".concat(e.field,"-").concat(r,"-").concat(Date.now(),"-message");e.messageElement.setAttribute("id",a),e.element.setAttribute("aria-describedby",a);var n=e.element.getAttribute("type");"radio"!==n&&"checkbox"!==n||t.forEach((function(e){return e.setAttribute("aria-describedby",a)}))},r}(i.Plugin);return c.Aria=t,c}();var f,p=u.exports,v={exports:{}},h={};v.exports=function(){if(f)return h;f=1;var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)},t=function(t){function r(e){var r=t.call(this,e)||this;return r.addedFields=new Map,r.opts=Object.assign({},{html5Input:!1,pluginPrefix:"data-fvp-",prefix:"data-fv-"},e),r.fieldAddedHandler=r.onFieldAdded.bind(r),r.fieldRemovedHandler=r.onFieldRemoved.bind(r),r}return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function a(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}(r,t),r.prototype.install=function(){var e=this;this.parsePlugins();var t=this.parseOptions();Object.keys(t).forEach((function(r){e.addedFields.has(r)||e.addedFields.set(r,!0),e.core.addField(r,t[r])})),this.core.on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler)},r.prototype.uninstall=function(){this.addedFields.clear(),this.core.off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler)},r.prototype.onFieldAdded=function(e){var t=this,r=e.elements;r&&0!==r.length&&!this.addedFields.has(e.field)&&(this.addedFields.set(e.field,!0),r.forEach((function(r){var a=t.parseElement(r);if(!t.isEmptyOption(a)){var n={selector:e.options.selector,validators:Object.assign({},e.options.validators||{},a.validators)};t.core.setFieldOptions(e.field,n)}})))},r.prototype.onFieldRemoved=function(e){e.field&&this.addedFields.has(e.field)&&this.addedFields.delete(e.field)},r.prototype.parseOptions=function(){var e=this,t=this.opts.prefix,r={},a=this.core.getFields(),n=this.core.getFormElement();return[].slice.call(n.querySelectorAll("[name], [".concat(t,"field]"))).forEach((function(a){var n=e.parseElement(a);if(!e.isEmptyOption(n)){var i=a.getAttribute("name")||a.getAttribute("".concat(t,"field"));r[i]=Object.assign({},r[i],n)}})),Object.keys(r).forEach((function(e){Object.keys(r[e].validators).forEach((function(t){r[e].validators[t].enabled=r[e].validators[t].enabled||!1,a[e]&&a[e].validators&&a[e].validators[t]&&Object.assign(r[e].validators[t],a[e].validators[t])}))})),Object.assign({},a,r)},r.prototype.createPluginInstance=function(e,t){for(var r=e.split("."),a=window||this,n=0,i=r.length;n<i;n++)a=a[r[n]];if("function"!=typeof a)throw new Error("the plugin ".concat(e," doesn't exist"));return new a(t)},r.prototype.parsePlugins=function(){for(var e,t=this,r=this.core.getFormElement(),a=new RegExp("^".concat(this.opts.pluginPrefix,"([a-z0-9-]+)(___)*([a-z0-9-]+)*$")),n=r.attributes.length,i={},s=0;s<n;s++){var o=r.attributes[s].name,l=r.attributes[s].value,d=a.exec(o);if(d&&4===d.length){var u=this.toCamelCase(d[1]);i[u]=Object.assign({},d[3]?((e={})[this.toCamelCase(d[3])]=l,e):{enabled:""===l||"true"===l},i[u])}}Object.keys(i).forEach((function(e){var r=i[e],a=r.enabled,n=r.class;if(a&&n){delete r.enabled,delete r.clazz;var s=t.createPluginInstance(n,r);t.core.registerPlugin(e,s)}}))},r.prototype.isEmptyOption=function(e){var t=e.validators;return 0===Object.keys(t).length&&t.constructor===Object},r.prototype.parseElement=function(e){for(var t=new RegExp("^".concat(this.opts.prefix,"([a-z0-9-]+)(___)*([a-z0-9-]+)*$")),r=e.attributes.length,a={},n=e.getAttribute("type"),i=0;i<r;i++){var s=e.attributes[i].name,o=e.attributes[i].value;if(this.opts.html5Input)switch(!0){case"minlength"===s:a.stringLength=Object.assign({},{enabled:!0,min:parseInt(o,10)},a.stringLength);break;case"maxlength"===s:a.stringLength=Object.assign({},{enabled:!0,max:parseInt(o,10)},a.stringLength);break;case"pattern"===s:a.regexp=Object.assign({},{enabled:!0,regexp:o},a.regexp);break;case"required"===s:a.notEmpty=Object.assign({},{enabled:!0},a.notEmpty);break;case"type"===s&&"color"===o:a.color=Object.assign({},{enabled:!0,type:"hex"},a.color);break;case"type"===s&&"email"===o:a.emailAddress=Object.assign({},{enabled:!0},a.emailAddress);break;case"type"===s&&"url"===o:a.uri=Object.assign({},{enabled:!0},a.uri);break;case"type"===s&&"range"===o:a.between=Object.assign({},{enabled:!0,max:parseFloat(e.getAttribute("max")),min:parseFloat(e.getAttribute("min"))},a.between);break;case"min"===s&&"date"!==n&&"range"!==n:a.greaterThan=Object.assign({},{enabled:!0,min:parseFloat(o)},a.greaterThan);break;case"max"===s&&"date"!==n&&"range"!==n:a.lessThan=Object.assign({},{enabled:!0,max:parseFloat(o)},a.lessThan)}var l=t.exec(s);if(l&&4===l.length){var d=this.toCamelCase(l[1]);a[d]||(a[d]={}),l[3]?a[d][this.toCamelCase(l[3])]=this.normalizeValue(o):!0===a[d].enabled&&!1===a[d].enabled||(a[d].enabled=""===o||"true"===o)}}return{validators:a}},r.prototype.normalizeValue=function(e){return"true"===e||""===e||"false"!==e&&e},r.prototype.toUpperCase=function(e){return e.charAt(1).toUpperCase()},r.prototype.toCamelCase=function(e){return e.replace(/-./g,this.toUpperCase)},r}(i.Plugin);return h.Declarative=t,h}();var m,g=v.exports,b={exports:{}},A={};b.exports=function(){if(m)return A;m=1;var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)},t=function(t){function r(){var e=t.call(this,{})||this;return e.onValidHandler=e.onFormValid.bind(e),e}return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function a(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}(r,t),r.prototype.install=function(){if(this.core.getFormElement().querySelectorAll('[type="submit"][name="submit"]').length)throw new Error("Do not use `submit` for the name attribute of submit button");this.core.on("core.form.valid",this.onValidHandler)},r.prototype.uninstall=function(){this.core.off("core.form.valid",this.onValidHandler)},r.prototype.onFormValid=function(){var e=this.core.getFormElement();this.isEnabled&&e instanceof HTMLFormElement&&e.submit()},r}(i.Plugin);return A.DefaultSubmit=t,A}();var y,E=b.exports,I={exports:{}},x={};I.exports=function(){if(y)return x;y=1;var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)},t=function(t){function r(e){var r=t.call(this,e)||this;return r.opts=e||{},r.triggerExecutedHandler=r.onTriggerExecuted.bind(r),r}return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function a(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}(r,t),r.prototype.install=function(){this.core.on("plugins.trigger.executed",this.triggerExecutedHandler)},r.prototype.uninstall=function(){this.core.off("plugins.trigger.executed",this.triggerExecutedHandler)},r.prototype.onTriggerExecuted=function(e){if(this.isEnabled&&this.opts[e.field])for(var t=0,r=this.opts[e.field].split(" ");t<r.length;t++){var a=r[t].trim();this.opts[a]&&this.core.revalidateField(a)}},r}(i.Plugin);return x.Dependency=t,x}();var C,O=I.exports,F={exports:{}},V={};F.exports=function(){if(C)return V;C=1;var e=i,t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},t(e,r)},r=e.utils.removeUndefined,a=function(e){function a(t){var n=e.call(this,t)||this;return n.opts=Object.assign({},{excluded:a.defaultIgnore},r(t)),n.ignoreValidationFilter=n.ignoreValidation.bind(n),n}return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}(a,e),a.defaultIgnore=function(e,t,r){var a=!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length),n=t.getAttribute("disabled");return""===n||"disabled"===n||"hidden"===t.getAttribute("type")||!a},a.prototype.install=function(){this.core.registerFilter("element-ignored",this.ignoreValidationFilter)},a.prototype.uninstall=function(){this.core.deregisterFilter("element-ignored",this.ignoreValidationFilter)},a.prototype.ignoreValidation=function(e,t,r){return!!this.isEnabled&&this.opts.excluded.apply(this,[e,t,r])},a}(e.Plugin);return V.Excluded=a,V}();var S,k=F.exports,w={exports:{}},H={};w.exports=function(){if(S)return H;S=1;var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)},t=function(t){function r(e){var r=t.call(this,e)||this;return r.statuses=new Map,r.opts=Object.assign({},{onStatusChanged:function(){}},e),r.elementValidatingHandler=r.onElementValidating.bind(r),r.elementValidatedHandler=r.onElementValidated.bind(r),r.elementNotValidatedHandler=r.onElementNotValidated.bind(r),r.elementIgnoredHandler=r.onElementIgnored.bind(r),r.fieldAddedHandler=r.onFieldAdded.bind(r),r.fieldRemovedHandler=r.onFieldRemoved.bind(r),r}return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function a(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}(r,t),r.prototype.install=function(){this.core.on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.ignored",this.elementIgnoredHandler).on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler)},r.prototype.uninstall=function(){this.statuses.clear(),this.core.off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.ignored",this.elementIgnoredHandler).off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler)},r.prototype.areFieldsValid=function(){return Array.from(this.statuses.values()).every((function(e){return"Valid"===e||"NotValidated"===e||"Ignored"===e}))},r.prototype.getStatuses=function(){return this.isEnabled?this.statuses:new Map},r.prototype.onFieldAdded=function(e){this.statuses.set(e.field,"NotValidated")},r.prototype.onFieldRemoved=function(e){this.statuses.has(e.field)&&this.statuses.delete(e.field),this.handleStatusChanged(this.areFieldsValid())},r.prototype.onElementValidating=function(e){this.statuses.set(e.field,"Validating"),this.handleStatusChanged(!1)},r.prototype.onElementValidated=function(e){this.statuses.set(e.field,e.valid?"Valid":"Invalid"),e.valid?this.handleStatusChanged(this.areFieldsValid()):this.handleStatusChanged(!1)},r.prototype.onElementNotValidated=function(e){this.statuses.set(e.field,"NotValidated"),this.handleStatusChanged(!1)},r.prototype.onElementIgnored=function(e){this.statuses.set(e.field,"Ignored"),this.handleStatusChanged(this.areFieldsValid())},r.prototype.handleStatusChanged=function(e){this.isEnabled&&this.opts.onStatusChanged(e)},r}(i.Plugin);return H.FieldStatus=t,H}();var $,N=w.exports,M={exports:{}},T={},L={exports:{}},P={};L.exports=function(){if($)return P;$=1;var e=i,t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},t(e,r)},r=e.utils.classSet,a=function(e){function a(t){var r=e.call(this,t)||this;return r.useDefaultContainer=!1,r.messages=new Map,r.defaultContainer=document.createElement("div"),r.useDefaultContainer=!t||!t.container,r.opts=Object.assign({},{container:function(e,t){return r.defaultContainer}},t),r.elementIgnoredHandler=r.onElementIgnored.bind(r),r.fieldAddedHandler=r.onFieldAdded.bind(r),r.fieldRemovedHandler=r.onFieldRemoved.bind(r),r.validatorValidatedHandler=r.onValidatorValidated.bind(r),r.validatorNotValidatedHandler=r.onValidatorNotValidated.bind(r),r}return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}(a,e),a.getClosestContainer=function(e,t,r){for(var a=e;a&&a!==t&&(a=a.parentElement,!r.test(a.className)););return a},a.prototype.install=function(){this.useDefaultContainer&&this.core.getFormElement().appendChild(this.defaultContainer),this.core.on("core.element.ignored",this.elementIgnoredHandler).on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler).on("core.validator.validated",this.validatorValidatedHandler).on("core.validator.notvalidated",this.validatorNotValidatedHandler)},a.prototype.uninstall=function(){this.useDefaultContainer&&this.core.getFormElement().removeChild(this.defaultContainer),this.messages.forEach((function(e){return e.parentNode.removeChild(e)})),this.messages.clear(),this.core.off("core.element.ignored",this.elementIgnoredHandler).off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler).off("core.validator.validated",this.validatorValidatedHandler).off("core.validator.notvalidated",this.validatorNotValidatedHandler)},a.prototype.onEnabled=function(){this.messages.forEach((function(e,t,a){r(t,{"fv-plugins-message-container--enabled":!0,"fv-plugins-message-container--disabled":!1})}))},a.prototype.onDisabled=function(){this.messages.forEach((function(e,t,a){r(t,{"fv-plugins-message-container--enabled":!1,"fv-plugins-message-container--disabled":!0})}))},a.prototype.onFieldAdded=function(e){var t=this,r=e.elements;r&&(r.forEach((function(e){var r=t.messages.get(e);r&&(r.parentNode.removeChild(r),t.messages.delete(e))})),this.prepareFieldContainer(e.field,r))},a.prototype.onFieldRemoved=function(e){var t=this;if(e.elements.length&&e.field){var r=e.elements[0].getAttribute("type");("radio"===r||"checkbox"===r?[e.elements[0]]:e.elements).forEach((function(e){if(t.messages.has(e)){var r=t.messages.get(e);r.parentNode.removeChild(r),t.messages.delete(e)}}))}},a.prototype.prepareFieldContainer=function(e,t){var r=this;if(t.length){var a=t[0].getAttribute("type");"radio"===a||"checkbox"===a?this.prepareElementContainer(e,t[0],t):t.forEach((function(a){return r.prepareElementContainer(e,a,t)}))}},a.prototype.prepareElementContainer=function(e,t,a){var n;if("string"==typeof this.opts.container){var i="#"===this.opts.container.charAt(0)?'[id="'.concat(this.opts.container.substring(1),'"]'):this.opts.container;n=this.core.getFormElement().querySelector(i)}else n=this.opts.container(e,t);var s=document.createElement("div");n.appendChild(s),r(s,{"fv-plugins-message-container":!0,"fv-plugins-message-container--enabled":this.isEnabled,"fv-plugins-message-container--disabled":!this.isEnabled}),this.core.emit("plugins.message.placed",{element:t,elements:a,field:e,messageElement:s}),this.messages.set(t,s)},a.prototype.getMessage=function(e){return"string"==typeof e.message?e.message:e.message[this.core.getLocale()]},a.prototype.onValidatorValidated=function(e){var t,a=e.elements,n=e.element.getAttribute("type"),i=("radio"===n||"checkbox"===n)&&a.length>0?a[0]:e.element;if(this.messages.has(i)){var s=this.messages.get(i),o=s.querySelector('[data-field="'.concat(e.field.replace(/"/g,'\\"'),'"][data-validator="').concat(e.validator.replace(/"/g,'\\"'),'"]'));if(o||e.result.valid)o&&!e.result.valid?(o.innerHTML=this.getMessage(e.result),this.core.emit("plugins.message.displayed",{element:e.element,field:e.field,message:e.result.message,messageElement:o,meta:e.result.meta,validator:e.validator})):o&&e.result.valid&&s.removeChild(o);else{var l=document.createElement("div");l.innerHTML=this.getMessage(e.result),l.setAttribute("data-field",e.field),l.setAttribute("data-validator",e.validator),this.opts.clazz&&r(l,((t={})[this.opts.clazz]=!0,t)),s.appendChild(l),this.core.emit("plugins.message.displayed",{element:e.element,field:e.field,message:e.result.message,messageElement:l,meta:e.result.meta,validator:e.validator})}}},a.prototype.onValidatorNotValidated=function(e){var t=e.elements,r=e.element.getAttribute("type"),a="radio"===r||"checkbox"===r?t[0]:e.element;if(this.messages.has(a)){var n=this.messages.get(a),i=n.querySelector('[data-field="'.concat(e.field.replace(/"/g,'\\"'),'"][data-validator="').concat(e.validator.replace(/"/g,'\\"'),'"]'));i&&n.removeChild(i)}},a.prototype.onElementIgnored=function(e){var t=e.elements,r=e.element.getAttribute("type"),a="radio"===r||"checkbox"===r?t[0]:e.element;if(this.messages.has(a)){var n=this.messages.get(a);[].slice.call(n.querySelectorAll('[data-field="'.concat(e.field.replace(/"/g,'\\"'),'"]'))).forEach((function(e){n.removeChild(e)}))}},a}(e.Plugin);return P.Message=a,P}();var R,D=L.exports;
/** 
     * FormValidation (https://formvalidation.io)
     * The best validation library for JavaScript
     * (c) 2013 - 2023 Nguyen Huu Phuoc <<EMAIL>>
     *
     * @license https://formvalidation.io/license
     * @package @form-validation/plugin-framework
     * @version 2.4.0
     */M.exports=function(){if(R)return T;R=1;var e=i,t=D,r=function(e,t){return r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},r(e,t)},a=e.utils.classSet,n=e.utils.closest,s=function(e){function i(t){var r=e.call(this,t)||this;return r.results=new Map,r.containers=new Map,r.opts=Object.assign({},{defaultMessageContainer:!0,eleInvalidClass:"",eleValidClass:"",rowClasses:"",rowValidatingClass:""},t),r.elementIgnoredHandler=r.onElementIgnored.bind(r),r.elementValidatingHandler=r.onElementValidating.bind(r),r.elementValidatedHandler=r.onElementValidated.bind(r),r.elementNotValidatedHandler=r.onElementNotValidated.bind(r),r.iconPlacedHandler=r.onIconPlaced.bind(r),r.fieldAddedHandler=r.onFieldAdded.bind(r),r.fieldRemovedHandler=r.onFieldRemoved.bind(r),r.messagePlacedHandler=r.onMessagePlaced.bind(r),r}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function a(){this.constructor=e}r(e,t),e.prototype=null===t?Object.create(t):(a.prototype=t.prototype,new a)}(i,e),i.prototype.install=function(){var e,r=this;a(this.core.getFormElement(),((e={})[this.opts.formClass]=!0,e["fv-plugins-framework"]=!0,e)),this.core.on("core.element.ignored",this.elementIgnoredHandler).on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("plugins.icon.placed",this.iconPlacedHandler).on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler),this.opts.defaultMessageContainer&&(this.core.registerPlugin(i.MESSAGE_PLUGIN,new t.Message({clazz:this.opts.messageClass,container:function(e,a){var i="string"==typeof r.opts.rowSelector?r.opts.rowSelector:r.opts.rowSelector(e,a),s=n(a,i);return t.Message.getClosestContainer(a,s,r.opts.rowPattern)}})),this.core.on("plugins.message.placed",this.messagePlacedHandler))},i.prototype.uninstall=function(){var e;this.results.clear(),this.containers.clear(),a(this.core.getFormElement(),((e={})[this.opts.formClass]=!1,e["fv-plugins-framework"]=!1,e)),this.core.off("core.element.ignored",this.elementIgnoredHandler).off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("plugins.icon.placed",this.iconPlacedHandler).off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler),this.opts.defaultMessageContainer&&(this.core.deregisterPlugin(i.MESSAGE_PLUGIN),this.core.off("plugins.message.placed",this.messagePlacedHandler))},i.prototype.onEnabled=function(){var e;a(this.core.getFormElement(),((e={})[this.opts.formClass]=!0,e)),this.opts.defaultMessageContainer&&this.core.enablePlugin(i.MESSAGE_PLUGIN)},i.prototype.onDisabled=function(){var e;a(this.core.getFormElement(),((e={})[this.opts.formClass]=!1,e)),this.opts.defaultMessageContainer&&this.core.disablePlugin(i.MESSAGE_PLUGIN)},i.prototype.onIconPlaced=function(e){},i.prototype.onMessagePlaced=function(e){},i.prototype.onFieldAdded=function(e){var t=this,r=e.elements;r&&(r.forEach((function(e){var r,n=t.containers.get(e);n&&(a(n,((r={})[t.opts.rowInvalidClass]=!1,r[t.opts.rowValidatingClass]=!1,r[t.opts.rowValidClass]=!1,r["fv-plugins-icon-container"]=!1,r)),t.containers.delete(e))})),this.prepareFieldContainer(e.field,r))},i.prototype.onFieldRemoved=function(e){var t=this;e.elements.forEach((function(e){var r,n=t.containers.get(e);n&&a(n,((r={})[t.opts.rowInvalidClass]=!1,r[t.opts.rowValidatingClass]=!1,r[t.opts.rowValidClass]=!1,r))}))},i.prototype.prepareFieldContainer=function(e,t){var r=this;if(t.length){var a=t[0].getAttribute("type");"radio"===a||"checkbox"===a?this.prepareElementContainer(e,t[0]):t.forEach((function(t){return r.prepareElementContainer(e,t)}))}},i.prototype.prepareElementContainer=function(e,t){var r,i="string"==typeof this.opts.rowSelector?this.opts.rowSelector:this.opts.rowSelector(e,t),s=n(t,i);s!==t&&(a(s,((r={})[this.opts.rowClasses]=!0,r["fv-plugins-icon-container"]=!0,r)),this.containers.set(t,s))},i.prototype.onElementValidating=function(e){this.removeClasses(e.element,e.elements)},i.prototype.onElementNotValidated=function(e){this.removeClasses(e.element,e.elements)},i.prototype.onElementIgnored=function(e){this.removeClasses(e.element,e.elements)},i.prototype.removeClasses=function(e,t){var r,n=this,i=e.getAttribute("type"),s="radio"===i||"checkbox"===i?t[0]:e;t.forEach((function(e){var t;a(e,((t={})[n.opts.eleValidClass]=!1,t[n.opts.eleInvalidClass]=!1,t))}));var o=this.containers.get(s);o&&a(o,((r={})[this.opts.rowInvalidClass]=!1,r[this.opts.rowValidatingClass]=!1,r[this.opts.rowValidClass]=!1,r))},i.prototype.onElementValidated=function(e){var t,r,n=this,i=e.elements,s=e.element.getAttribute("type"),o="radio"===s||"checkbox"===s?i[0]:e.element;i.forEach((function(t){var r;a(t,((r={})[n.opts.eleValidClass]=e.valid,r[n.opts.eleInvalidClass]=!e.valid,r))}));var l=this.containers.get(o);if(l)if(e.valid){this.results.delete(o);var d=!0;this.containers.forEach((function(e,t){e===l&&!1===n.results.get(t)&&(d=!1)})),d&&a(l,((r={})[this.opts.rowInvalidClass]=!1,r[this.opts.rowValidatingClass]=!1,r[this.opts.rowValidClass]=!0,r))}else this.results.set(o,!1),a(l,((t={})[this.opts.rowInvalidClass]=!0,t[this.opts.rowValidatingClass]=!1,t[this.opts.rowValidClass]=!1,t))},i.MESSAGE_PLUGIN="___frameworkMessage",i}(e.Plugin);return T.Framework=s,T}();var Z,_=M.exports,B={exports:{}},G={};B.exports=function(){if(Z)return G;Z=1;var e=i,t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},t(e,r)},r=e.utils.classSet,a=function(e){function a(t){var r=e.call(this,t)||this;return r.icons=new Map,r.opts=Object.assign({},{invalid:"fv-plugins-icon--invalid",onPlaced:function(){},onSet:function(){},valid:"fv-plugins-icon--valid",validating:"fv-plugins-icon--validating"},t),r.elementValidatingHandler=r.onElementValidating.bind(r),r.elementValidatedHandler=r.onElementValidated.bind(r),r.elementNotValidatedHandler=r.onElementNotValidated.bind(r),r.elementIgnoredHandler=r.onElementIgnored.bind(r),r.fieldAddedHandler=r.onFieldAdded.bind(r),r}return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}(a,e),a.prototype.install=function(){this.core.on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.ignored",this.elementIgnoredHandler).on("core.field.added",this.fieldAddedHandler)},a.prototype.uninstall=function(){this.icons.forEach((function(e){return e.parentNode.removeChild(e)})),this.icons.clear(),this.core.off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.ignored",this.elementIgnoredHandler).off("core.field.added",this.fieldAddedHandler)},a.prototype.onEnabled=function(){this.icons.forEach((function(e,t,a){r(t,{"fv-plugins-icon--enabled":!0,"fv-plugins-icon--disabled":!1})}))},a.prototype.onDisabled=function(){this.icons.forEach((function(e,t,a){r(t,{"fv-plugins-icon--enabled":!1,"fv-plugins-icon--disabled":!0})}))},a.prototype.onFieldAdded=function(e){var t=this,r=e.elements;r&&(r.forEach((function(e){var r=t.icons.get(e);r&&(r.parentNode.removeChild(r),t.icons.delete(e))})),this.prepareFieldIcon(e.field,r))},a.prototype.prepareFieldIcon=function(e,t){var r=this;if(t.length){var a=t[0].getAttribute("type");"radio"===a||"checkbox"===a?this.prepareElementIcon(e,t[0]):t.forEach((function(t){return r.prepareElementIcon(e,t)}))}},a.prototype.prepareElementIcon=function(e,t){var a=document.createElement("i");a.setAttribute("data-field",e),t.parentNode.insertBefore(a,t.nextSibling),r(a,{"fv-plugins-icon":!0,"fv-plugins-icon--enabled":this.isEnabled,"fv-plugins-icon--disabled":!this.isEnabled});var n={classes:{invalid:this.opts.invalid,valid:this.opts.valid,validating:this.opts.validating},element:t,field:e,iconElement:a};this.core.emit("plugins.icon.placed",n),this.opts.onPlaced(n),this.icons.set(t,a)},a.prototype.onElementValidating=function(e){var t,r=this.setClasses(e.field,e.element,e.elements,((t={})[this.opts.invalid]=!1,t[this.opts.valid]=!1,t[this.opts.validating]=!0,t)),a={element:e.element,field:e.field,iconElement:r,status:"Validating"};this.core.emit("plugins.icon.set",a),this.opts.onSet(a)},a.prototype.onElementValidated=function(e){var t,r=this.setClasses(e.field,e.element,e.elements,((t={})[this.opts.invalid]=!e.valid,t[this.opts.valid]=e.valid,t[this.opts.validating]=!1,t)),a={element:e.element,field:e.field,iconElement:r,status:e.valid?"Valid":"Invalid"};this.core.emit("plugins.icon.set",a),this.opts.onSet(a)},a.prototype.onElementNotValidated=function(e){var t,r=this.setClasses(e.field,e.element,e.elements,((t={})[this.opts.invalid]=!1,t[this.opts.valid]=!1,t[this.opts.validating]=!1,t)),a={element:e.element,field:e.field,iconElement:r,status:"NotValidated"};this.core.emit("plugins.icon.set",a),this.opts.onSet(a)},a.prototype.onElementIgnored=function(e){var t,r=this.setClasses(e.field,e.element,e.elements,((t={})[this.opts.invalid]=!1,t[this.opts.valid]=!1,t[this.opts.validating]=!1,t)),a={element:e.element,field:e.field,iconElement:r,status:"Ignored"};this.core.emit("plugins.icon.set",a),this.opts.onSet(a)},a.prototype.setClasses=function(e,t,a,n){var i=t.getAttribute("type"),s="radio"===i||"checkbox"===i?a[0]:t;if(this.icons.has(s)){var o=this.icons.get(s);return r(o,n),o}return null},a}(e.Plugin);return G.Icon=a,G}();var U,j=B.exports,K={exports:{}},z={};K.exports=function(){if(U)return z;U=1;var e=i,t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},t(e,r)},r=e.utils.removeUndefined,a=function(e){function a(t){var a=e.call(this,t)||this;return a.invalidFields=new Map,a.opts=Object.assign({},{enabled:!0},r(t)),a.validatorHandler=a.onValidatorValidated.bind(a),a.shouldValidateFilter=a.shouldValidate.bind(a),a.fieldAddedHandler=a.onFieldAdded.bind(a),a.elementNotValidatedHandler=a.onElementNotValidated.bind(a),a.elementValidatingHandler=a.onElementValidating.bind(a),a}return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}(a,e),a.prototype.install=function(){this.core.on("core.validator.validated",this.validatorHandler).on("core.field.added",this.fieldAddedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.validating",this.elementValidatingHandler).registerFilter("field-should-validate",this.shouldValidateFilter)},a.prototype.uninstall=function(){this.invalidFields.clear(),this.core.off("core.validator.validated",this.validatorHandler).off("core.field.added",this.fieldAddedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.validating",this.elementValidatingHandler).deregisterFilter("field-should-validate",this.shouldValidateFilter)},a.prototype.shouldValidate=function(e,t,r,a){return!this.isEnabled||!((!0===this.opts.enabled||!0===this.opts.enabled[e])&&this.invalidFields.has(t)&&this.invalidFields.get(t).length&&-1===this.invalidFields.get(t).indexOf(a))},a.prototype.onValidatorValidated=function(e){var t=this.invalidFields.has(e.element)?this.invalidFields.get(e.element):[],r=t.indexOf(e.validator);e.result.valid&&r>=0?t.splice(r,1):e.result.valid||-1!==r||t.push(e.validator),this.invalidFields.set(e.element,t)},a.prototype.onFieldAdded=function(e){e.elements&&this.clearInvalidFields(e.elements)},a.prototype.onElementNotValidated=function(e){this.clearInvalidFields(e.elements)},a.prototype.onElementValidating=function(e){this.clearInvalidFields(e.elements)},a.prototype.clearInvalidFields=function(e){var t=this;e.forEach((function(e){return t.invalidFields.delete(e)}))},a}(e.Plugin);return z.Sequence=a,z}();var Y,J=K.exports,X={exports:{}},W={};X.exports=function(){if(Y)return W;Y=1;var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)},t=function(t){function r(e){var r=t.call(this,e)||this;return r.isFormValid=!1,r.isButtonClicked=!1,r.opts=Object.assign({},{aspNetButton:!1,buttons:function(e){return[].slice.call(e.querySelectorAll('[type="submit"]:not([formnovalidate])'))},liveMode:!0},e),r.submitHandler=r.handleSubmitEvent.bind(r),r.buttonClickHandler=r.handleClickEvent.bind(r),r.ignoreValidationFilter=r.ignoreValidation.bind(r),r}return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function a(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}(r,t),r.prototype.install=function(){var e=this;if(this.core.getFormElement()instanceof HTMLFormElement){var t=this.core.getFormElement();this.submitButtons=this.opts.buttons(t),t.setAttribute("novalidate","novalidate"),t.addEventListener("submit",this.submitHandler),this.hiddenClickedEle=document.createElement("input"),this.hiddenClickedEle.setAttribute("type","hidden"),t.appendChild(this.hiddenClickedEle),this.submitButtons.forEach((function(t){t.addEventListener("click",e.buttonClickHandler)})),this.core.registerFilter("element-ignored",this.ignoreValidationFilter)}},r.prototype.uninstall=function(){var e=this,t=this.core.getFormElement();t instanceof HTMLFormElement&&t.removeEventListener("submit",this.submitHandler),this.submitButtons.forEach((function(t){t.removeEventListener("click",e.buttonClickHandler)})),this.hiddenClickedEle.parentElement.removeChild(this.hiddenClickedEle),this.core.deregisterFilter("element-ignored",this.ignoreValidationFilter)},r.prototype.handleSubmitEvent=function(e){this.validateForm(e)},r.prototype.handleClickEvent=function(e){var t=e.currentTarget;if(this.isButtonClicked=!0,t instanceof HTMLElement)if(this.opts.aspNetButton&&!0===this.isFormValid);else{this.core.getFormElement().removeEventListener("submit",this.submitHandler),this.clickedButton=e.target;var r=this.clickedButton.getAttribute("name"),a=this.clickedButton.getAttribute("value");r&&a&&(this.hiddenClickedEle.setAttribute("name",r),this.hiddenClickedEle.setAttribute("value",a)),this.validateForm(e)}},r.prototype.validateForm=function(e){var t=this;this.isEnabled&&(e.preventDefault(),this.core.validate().then((function(e){"Valid"===e&&t.opts.aspNetButton&&!t.isFormValid&&t.clickedButton&&(t.isFormValid=!0,t.clickedButton.removeEventListener("click",t.buttonClickHandler),t.clickedButton.click())})))},r.prototype.ignoreValidation=function(e,t,r){return!!this.isEnabled&&!this.opts.liveMode&&!this.isButtonClicked},r}(i.Plugin);return W.SubmitButton=t,W}();var q,Q=X.exports,ee={exports:{}},te={};ee.exports=function(){if(q)return te;q=1;var e=i,t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},t(e,r)},r=e.utils.classSet,a=function(e){function a(t){var r=e.call(this,t)||this;return r.messages=new Map,r.opts=Object.assign({},{placement:"top",trigger:"click"},t),r.iconPlacedHandler=r.onIconPlaced.bind(r),r.validatorValidatedHandler=r.onValidatorValidated.bind(r),r.elementValidatedHandler=r.onElementValidated.bind(r),r.documentClickHandler=r.onDocumentClicked.bind(r),r}return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function a(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}(a,e),a.prototype.install=function(){var e;this.tip=document.createElement("div"),r(this.tip,((e={"fv-plugins-tooltip":!0})["fv-plugins-tooltip--".concat(this.opts.placement)]=!0,e)),document.body.appendChild(this.tip),this.core.on("plugins.icon.placed",this.iconPlacedHandler).on("core.validator.validated",this.validatorValidatedHandler).on("core.element.validated",this.elementValidatedHandler),"click"===this.opts.trigger&&document.addEventListener("click",this.documentClickHandler)},a.prototype.uninstall=function(){this.messages.clear(),document.body.removeChild(this.tip),this.core.off("plugins.icon.placed",this.iconPlacedHandler).off("core.validator.validated",this.validatorValidatedHandler).off("core.element.validated",this.elementValidatedHandler),"click"===this.opts.trigger&&document.removeEventListener("click",this.documentClickHandler)},a.prototype.onIconPlaced=function(e){var t=this;r(e.iconElement,{"fv-plugins-tooltip-icon":!0}),"hover"===this.opts.trigger?(e.iconElement.addEventListener("mouseenter",(function(r){return t.show(e.element,r)})),e.iconElement.addEventListener("mouseleave",(function(e){return t.hide()}))):e.iconElement.addEventListener("click",(function(r){return t.show(e.element,r)}))},a.prototype.onValidatorValidated=function(e){if(!e.result.valid){var t=e.elements,r=e.element.getAttribute("type"),a="radio"===r||"checkbox"===r?t[0]:e.element,n="string"==typeof e.result.message?e.result.message:e.result.message[this.core.getLocale()];this.messages.set(a,n)}},a.prototype.onElementValidated=function(e){if(e.valid){var t=e.elements,r=e.element.getAttribute("type"),a="radio"===r||"checkbox"===r?t[0]:e.element;this.messages.delete(a)}},a.prototype.onDocumentClicked=function(e){this.hide()},a.prototype.show=function(e,t){if(this.isEnabled&&(t.preventDefault(),t.stopPropagation(),this.messages.has(e))){r(this.tip,{"fv-plugins-tooltip--hide":!1}),this.tip.innerHTML='<div class="fv-plugins-tooltip__content">'.concat(this.messages.get(e),"</div>");var a=t.target.getBoundingClientRect(),n=this.tip.getBoundingClientRect(),i=n.height,s=n.width,o=0,l=0;switch(this.opts.placement){case"bottom":o=a.top+a.height,l=a.left+a.width/2-s/2;break;case"bottom-left":o=a.top+a.height,l=a.left;break;case"bottom-right":o=a.top+a.height,l=a.left+a.width-s;break;case"left":o=a.top+a.height/2-i/2,l=a.left-s;break;case"right":o=a.top+a.height/2-i/2,l=a.left+a.width;break;case"top-left":o=a.top-i,l=a.left;break;case"top-right":o=a.top-i,l=a.left+a.width-s;break;default:o=a.top-i,l=a.left+a.width/2-s/2}o+=window.scrollY||document.documentElement.scrollTop||document.body.scrollTop||0,l+=window.scrollX||document.documentElement.scrollLeft||document.body.scrollLeft||0,this.tip.setAttribute("style","top: ".concat(o,"px; left: ").concat(l,"px"))}},a.prototype.hide=function(){this.isEnabled&&r(this.tip,{"fv-plugins-tooltip--hide":!0})},a}(e.Plugin);return te.Tooltip=a,te}();var re,ae=ee.exports,ne={exports:{}},ie={};ne.exports=function(){if(re)return ie;re=1;var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},e(t,r)},t=function(t){function r(e){var r=t.call(this,e)||this;r.handlers=[],r.timers=new Map;var a=document.createElement("div");return r.defaultEvent="oninput"in a?"input":"keyup",r.opts=Object.assign({},{delay:0,event:r.defaultEvent,threshold:0},e),r.fieldAddedHandler=r.onFieldAdded.bind(r),r.fieldRemovedHandler=r.onFieldRemoved.bind(r),r}return function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function a(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(a.prototype=r.prototype,new a)}(r,t),r.prototype.install=function(){this.core.on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler)},r.prototype.uninstall=function(){this.handlers.forEach((function(e){return e.element.removeEventListener(e.event,e.handler)})),this.handlers=[],this.timers.forEach((function(e){return window.clearTimeout(e)})),this.timers.clear(),this.core.off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler)},r.prototype.prepareHandler=function(e,t){var r=this;t.forEach((function(t){var a=[];if(r.opts.event&&!1===r.opts.event[e])a=[];else if(r.opts.event&&r.opts.event[e]&&"function"!=typeof r.opts.event[e])a=r.opts.event[e].split(" ");else if("string"==typeof r.opts.event&&r.opts.event!==r.defaultEvent)a=r.opts.event.split(" ");else{var n=t.getAttribute("type"),i=t.tagName.toLowerCase();a=["radio"===n||"checkbox"===n||"file"===n||"select"===i?"change":r.ieVersion>=10&&t.getAttribute("placeholder")?"keyup":r.defaultEvent]}a.forEach((function(a){var n=function(a){return r.handleEvent(a,e,t)};r.handlers.push({element:t,event:a,field:e,handler:n}),t.addEventListener(a,n)}))}))},r.prototype.handleEvent=function(e,t,r){var a=this;if(this.isEnabled&&this.exceedThreshold(t,r)&&this.core.executeFilter("plugins-trigger-should-validate",!0,[t,r])){var n=function(){return a.core.validateElement(t,r).then((function(n){a.core.emit("plugins.trigger.executed",{element:r,event:e,field:t})}))},i=this.opts.delay[t]||this.opts.delay;if(0===i)n();else{var s=this.timers.get(r);s&&window.clearTimeout(s),this.timers.set(r,window.setTimeout(n,1e3*i))}}},r.prototype.onFieldAdded=function(e){this.handlers.filter((function(t){return t.field===e.field})).forEach((function(e){return e.element.removeEventListener(e.event,e.handler)})),this.prepareHandler(e.field,e.elements)},r.prototype.onFieldRemoved=function(e){this.handlers.filter((function(t){return t.field===e.field&&e.elements.indexOf(t.element)>=0})).forEach((function(e){return e.element.removeEventListener(e.event,e.handler)}))},r.prototype.exceedThreshold=function(e,t){var r=0!==this.opts.threshold[e]&&0!==this.opts.threshold&&(this.opts.threshold[e]||this.opts.threshold);if(!r)return!0;var a=t.getAttribute("type");return-1!==["button","checkbox","file","hidden","image","radio","reset","submit"].indexOf(a)||this.core.getElementValue(e,t).length>=r},r}(i.Plugin);return ie.Trigger=t,ie}();var se,oe=ne.exports,le={exports:{}},de={};le.exports=function(){if(se)return de;se=1;var e=i,t=e.utils.format,r=e.utils.removeUndefined;return de.between=function(){var e=function(e){return parseFloat("".concat(e).replace(",","."))};return{validate:function(a){var n=a.value;if(""===n)return{valid:!0};var i=Object.assign({},{inclusive:!0,message:""},r(a.options)),s=e(i.min),o=e(i.max);return i.inclusive?{message:t(a.l10n?i.message||a.l10n.between.default:i.message,["".concat(s),"".concat(o)]),valid:parseFloat(n)>=s&&parseFloat(n)<=o}:{message:t(a.l10n?i.message||a.l10n.between.notInclusive:i.message,["".concat(s),"".concat(o)]),valid:parseFloat(n)>s&&parseFloat(n)<o}}}},de}();var ue,ce=le.exports,fe={exports:{}},pe={};fe.exports=(ue||(ue=1,pe.blank=function(){return{validate:function(e){return{valid:!0}}}}),pe);var ve,he=fe.exports,me={exports:{}},ge={};me.exports=function(){if(ve)return ge;ve=1;var e=i.utils.call;return ge.callback=function(){return{validate:function(t){var r=e(t.options.callback,[t]);return"boolean"==typeof r?{valid:r}:r}}},ge}();var be,Ae=me.exports,ye={exports:{}},Ee={};ye.exports=function(){if(be)return Ee;be=1;var e=i.utils.format;return Ee.choice=function(){return{validate:function(t){var r="select"===t.element.tagName.toLowerCase()?t.element.querySelectorAll("option:checked").length:t.elements.filter((function(e){return e.checked})).length,a=t.options.min?"".concat(t.options.min):"",n=t.options.max?"".concat(t.options.max):"",i=t.l10n?t.options.message||t.l10n.choice.default:t.options.message,s=!(a&&r<parseInt(a,10)||n&&r>parseInt(n,10));switch(!0){case!!a&&!!n:i=e(t.l10n?t.l10n.choice.between:t.options.message,[a,n]);break;case!!a:i=e(t.l10n?t.l10n.choice.more:t.options.message,a);break;case!!n:i=e(t.l10n?t.l10n.choice.less:t.options.message,n)}return{message:i,valid:s}}}},Ee}();var Ie,xe=ye.exports,Ce={exports:{}},Oe={};Ce.exports=function(){if(Ie)return Oe;Ie=1;var e=i.algorithms.luhn,t={AMERICAN_EXPRESS:{length:[15],prefix:["34","37"]},DANKORT:{length:[16],prefix:["5019"]},DINERS_CLUB:{length:[14],prefix:["300","301","302","303","304","305","36"]},DINERS_CLUB_US:{length:[16],prefix:["54","55"]},DISCOVER:{length:[16],prefix:["6011","622126","622127","622128","622129","62213","62214","62215","62216","62217","62218","62219","6222","6223","6224","6225","6226","6227","6228","62290","62291","622920","622921","622922","622923","622924","622925","644","645","646","647","648","649","65"]},ELO:{length:[16],prefix:["4011","4312","4389","4514","4573","4576","5041","5066","5067","509","6277","6362","6363","650","6516","6550"]},FORBRUGSFORENINGEN:{length:[16],prefix:["600722"]},JCB:{length:[16],prefix:["3528","3529","353","354","355","356","357","358"]},LASER:{length:[16,17,18,19],prefix:["6304","6706","6771","6709"]},MAESTRO:{length:[12,13,14,15,16,17,18,19],prefix:["5018","5020","5038","5868","6304","6759","6761","6762","6763","6764","6765","6766"]},MASTERCARD:{length:[16],prefix:["51","52","53","54","55"]},SOLO:{length:[16,18,19],prefix:["6334","6767"]},UNIONPAY:{length:[16,17,18,19],prefix:["622126","622127","622128","622129","62213","62214","62215","62216","62217","62218","62219","6222","6223","6224","6225","6226","6227","6228","62290","62291","622920","622921","622922","622923","622924","622925"]},VISA:{length:[16],prefix:["4"]},VISA_ELECTRON:{length:[16],prefix:["4026","417500","4405","4508","4844","4913","4917"]}};return Oe.CREDIT_CARD_TYPES=t,Oe.creditCard=function(){return{validate:function(r){if(""===r.value)return{meta:{type:null},valid:!0};if(/[^0-9-\s]+/.test(r.value))return{meta:{type:null},valid:!1};var a=r.value.replace(/\D/g,"");if(!e(a))return{meta:{type:null},valid:!1};for(var n=0,i=Object.keys(t);n<i.length;n++){var s=i[n];for(var o in t[s].prefix)if(r.value.substr(0,t[s].prefix[o].length)===t[s].prefix[o]&&-1!==t[s].length.indexOf(a.length))return{meta:{type:s},valid:!0}}return{meta:{type:null},valid:!1}}}},Oe}();var Fe,Ve=Ce.exports,Se={exports:{}},ke={};Se.exports=function(){if(Fe)return ke;Fe=1;var e=i,t=e.utils.format,r=e.utils.isValidDate,a=e.utils.removeUndefined,n=function(e,t,r){var a=t.indexOf("YYYY"),n=t.indexOf("MM"),i=t.indexOf("DD");if(-1===a||-1===n||-1===i)return null;var s=e.split(" "),o=s[0].split(r);if(o.length<3)return null;var l=new Date(parseInt(o[a],10),parseInt(o[n],10)-1,parseInt(o[i],10)),d=s.length>2?s[2]:null;if(s.length>1){var u=s[1].split(":"),c=u.length>0?parseInt(u[0],10):0;l.setHours(d&&"PM"===d.toUpperCase()&&c<12?c+12:c),l.setMinutes(u.length>1?parseInt(u[1],10):0),l.setSeconds(u.length>2?parseInt(u[2],10):0)}return l},s=function(e,t){var r=t.replace(/Y/g,"y").replace(/M/g,"m").replace(/D/g,"d").replace(/:m/g,":M").replace(/:mm/g,":MM").replace(/:S/,":s").replace(/:SS/,":ss"),a=e.getDate(),n=a<10?"0".concat(a):a,i=e.getMonth()+1,s=i<10?"0".concat(i):i,o="".concat(e.getFullYear()).substr(2),l=e.getFullYear(),d=e.getHours()%12||12,u=d<10?"0".concat(d):d,c=e.getHours(),f=c<10?"0".concat(c):c,p=e.getMinutes(),v=p<10?"0".concat(p):p,h=e.getSeconds(),m=h<10?"0".concat(h):h,g={H:"".concat(c),HH:"".concat(f),M:"".concat(p),MM:"".concat(v),d:"".concat(a),dd:"".concat(n),h:"".concat(d),hh:"".concat(u),m:"".concat(i),mm:"".concat(s),s:"".concat(h),ss:"".concat(m),yy:"".concat(o),yyyy:"".concat(l)};return r.replace(/d{1,4}|m{1,4}|yy(?:yy)?|([HhMs])\1?|"[^"]*"|'[^']*'/g,(function(e){return g[e]?g[e]:e.slice(1,e.length-1)}))};return ke.date=function(){return{validate:function(e){if(""===e.value)return{meta:{date:null},valid:!0};var i=Object.assign({},{format:e.element&&"date"===e.element.getAttribute("type")?"YYYY-MM-DD":"MM/DD/YYYY",message:""},a(e.options)),o=e.l10n?e.l10n.date.default:i.message,l={message:"".concat(o),meta:{date:null},valid:!1},d=i.format.split(" "),u=d.length>1?d[1]:null,c=d.length>2?d[2]:null,f=e.value.split(" "),p=f[0],v=f.length>1?f[1]:null,h=f.length>2?f[2]:null;if(d.length!==f.length)return l;var m=i.separator||(-1!==p.indexOf("/")?"/":-1!==p.indexOf("-")?"-":-1!==p.indexOf(".")?".":"/");if(null===m||-1===p.indexOf(m))return l;var g=p.split(m),b=d[0].split(m);if(g.length!==b.length)return l;var A=g[b.indexOf("YYYY")],y=g[b.indexOf("MM")],E=g[b.indexOf("DD")];if(!/^\d+$/.test(A)||!/^\d+$/.test(y)||!/^\d+$/.test(E)||A.length>4||y.length>2||E.length>2)return l;var I=parseInt(A,10),x=parseInt(y,10),C=parseInt(E,10);if(!r(I,x,C))return l;var O=new Date(I,x-1,C);if(u){var F=v.split(":");if(u.split(":").length!==F.length)return l;var V=F.length>0?F[0].length<=2&&/^\d+$/.test(F[0])?parseInt(F[0],10):-1:0,S=F.length>1?F[1].length<=2&&/^\d+$/.test(F[1])?parseInt(F[1],10):-1:0,k=F.length>2?F[2].length<=2&&/^\d+$/.test(F[2])?parseInt(F[2],10):-1:0;if(-1===V||-1===S||-1===k)return l;if(k<0||k>60)return l;if(V<0||V>=24||c&&V>12)return l;if(S<0||S>59)return l;O.setHours(h&&"PM"===h.toUpperCase()&&V<12?V+12:V),O.setMinutes(S),O.setSeconds(k)}var w="function"==typeof i.min?i.min():i.min,H=w instanceof Date?w:w?n(w,b,m):O,$="function"==typeof i.max?i.max():i.max,N=$ instanceof Date?$:$?n($,b,m):O,M=w instanceof Date?s(H,i.format):w,T=$ instanceof Date?s(N,i.format):$;switch(!0){case!!M&&!T:return{message:t(e.l10n?e.l10n.date.min:o,M),meta:{date:O},valid:O.getTime()>=H.getTime()};case!!T&&!M:return{message:t(e.l10n?e.l10n.date.max:o,T),meta:{date:O},valid:O.getTime()<=N.getTime()};case!!T&&!!M:return{message:t(e.l10n?e.l10n.date.range:o,[M,T]),meta:{date:O},valid:O.getTime()<=N.getTime()&&O.getTime()>=H.getTime()};default:return{message:"".concat(o),meta:{date:O},valid:!0}}}}},ke}();var we,He=Se.exports,$e={exports:{}},Ne={};$e.exports=(we||(we=1,Ne.different=function(){return{validate:function(e){var t="function"==typeof e.options.compare?e.options.compare.call(this):e.options.compare;return{valid:""===t||e.value!==t}}}}),Ne);var Me,Te=$e.exports,Le={exports:{}},Pe={};Le.exports=(Me||(Me=1,Pe.digits=function(){return{validate:function(e){return{valid:""===e.value||/^\d+$/.test(e.value)}}}}),Pe);var Re,De=Le.exports,Ze={exports:{}},_e={};Ze.exports=function(){if(Re)return _e;Re=1;var e=i.utils.removeUndefined,t=/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,r=/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$/;return _e.emailAddress=function(){return{validate:function(a){if(""===a.value)return{valid:!0};var n=Object.assign({},{multiple:!1,requireGlobalDomain:!1,separator:/[,;]/},e(a.options)),i=n.requireGlobalDomain?r:t;if(!0===n.multiple||"true"==="".concat(n.multiple)){for(var s=n.separator||/[,;]/,o=function(e,t){for(var r=e.split(/"/),a=r.length,n=[],i="",s=0;s<a;s++)if(s%2==0){var o=r[s].split(t),l=o.length;if(1===l)i+=o[0];else{n.push(i+o[0]);for(var d=1;d<l-1;d++)n.push(o[d]);i=o[l-1]}}else i+='"'+r[s],s<a-1&&(i+='"');return n.push(i),n}(a.value,s),l=o.length,d=0;d<l;d++)if(!i.test(o[d]))return{valid:!1};return{valid:!0}}return{valid:i.test(a.value)}}}},_e}();var Be,Ge=Ze.exports,Ue={exports:{}},je={};Ue.exports=function(){if(Be)return je;Be=1;var e=function(e){return-1===e.indexOf(".")?e:e.split(".").slice(0,-1).join(".")};return je.file=function(){return{validate:function(t){if(""===t.value)return{valid:!0};var r,a,n=t.options.extension?t.options.extension.toLowerCase().split(",").map((function(e){return e.trim()})):[],i=t.options.type?t.options.type.toLowerCase().split(",").map((function(e){return e.trim()})):[];if(window.File&&window.FileList&&window.FileReader){var s=t.element.files,o=s.length,l=0;if(t.options.maxFiles&&o>parseInt("".concat(t.options.maxFiles),10))return{meta:{error:"INVALID_MAX_FILES"},valid:!1};if(t.options.minFiles&&o<parseInt("".concat(t.options.minFiles),10))return{meta:{error:"INVALID_MIN_FILES"},valid:!1};for(var d={},u=0;u<o;u++){if(l+=s[u].size,d={ext:r=s[u].name.substr(s[u].name.lastIndexOf(".")+1),file:s[u],size:s[u].size,type:s[u].type},t.options.minSize&&s[u].size<parseInt("".concat(t.options.minSize),10))return{meta:Object.assign({},{error:"INVALID_MIN_SIZE"},d),valid:!1};if(t.options.maxSize&&s[u].size>parseInt("".concat(t.options.maxSize),10))return{meta:Object.assign({},{error:"INVALID_MAX_SIZE"},d),valid:!1};if(n.length>0&&-1===n.indexOf(r.toLowerCase()))return{meta:Object.assign({},{error:"INVALID_EXTENSION"},d),valid:!1};if(i.length>0&&s[u].type&&-1===i.indexOf(s[u].type.toLowerCase()))return{meta:Object.assign({},{error:"INVALID_TYPE"},d),valid:!1};if(t.options.validateFileName&&!t.options.validateFileName(e(s[u].name)))return{meta:Object.assign({},{error:"INVALID_NAME"},d),valid:!1}}if(t.options.maxTotalSize&&l>parseInt("".concat(t.options.maxTotalSize),10))return{meta:Object.assign({},{error:"INVALID_MAX_TOTAL_SIZE",totalSize:l},d),valid:!1};if(t.options.minTotalSize&&l<parseInt("".concat(t.options.minTotalSize),10))return{meta:Object.assign({},{error:"INVALID_MIN_TOTAL_SIZE",totalSize:l},d),valid:!1}}else{if(r=t.value.substr(t.value.lastIndexOf(".")+1),n.length>0&&-1===n.indexOf(r.toLowerCase()))return{meta:{error:"INVALID_EXTENSION",ext:r},valid:!1};if(a=e(t.value),t.options.validateFileName&&!t.options.validateFileName(a))return{meta:{error:"INVALID_NAME",name:a},valid:!1}}return{valid:!0}}}},je}();var Ke,ze=Ue.exports,Ye={exports:{}},Je={};Ye.exports=function(){if(Ke)return Je;Ke=1;var e=i,t=e.utils.format,r=e.utils.removeUndefined;return Je.greaterThan=function(){return{validate:function(e){if(""===e.value)return{valid:!0};var a=Object.assign({},{inclusive:!0,message:""},r(e.options)),n=parseFloat("".concat(a.min).replace(",","."));return a.inclusive?{message:t(e.l10n?a.message||e.l10n.greaterThan.default:a.message,"".concat(n)),valid:parseFloat(e.value)>=n}:{message:t(e.l10n?a.message||e.l10n.greaterThan.notInclusive:a.message,"".concat(n)),valid:parseFloat(e.value)>n}}}},Je}();var Xe,We=Ye.exports,qe={exports:{}},Qe={};qe.exports=(Xe||(Xe=1,Qe.identical=function(){return{validate:function(e){var t="function"==typeof e.options.compare?e.options.compare.call(this):e.options.compare;return{valid:""===t||e.value===t}}}}),Qe);var et,tt=qe.exports,rt={exports:{}},at={};rt.exports=function(){if(et)return at;et=1;var e=i.utils.removeUndefined;return at.integer=function(){return{validate:function(t){if(""===t.value)return{valid:!0};var r=Object.assign({},{decimalSeparator:".",thousandsSeparator:""},e(t.options)),a="."===r.decimalSeparator?"\\.":r.decimalSeparator,n="."===r.thousandsSeparator?"\\.":r.thousandsSeparator,i=new RegExp("^-?[0-9]{1,3}(".concat(n,"[0-9]{3})*(").concat(a,"[0-9]+)?$")),s=new RegExp(n,"g"),o="".concat(t.value);if(!i.test(o))return{valid:!1};n&&(o=o.replace(s,"")),a&&(o=o.replace(a,"."));var l=parseFloat(o);return{valid:!isNaN(l)&&isFinite(l)&&Math.floor(l)===l}}}},at}();var nt,it=rt.exports,st={exports:{}},ot={};st.exports=function(){if(nt)return ot;nt=1;var e=i.utils.removeUndefined;return ot.ip=function(){return{validate:function(t){if(""===t.value)return{valid:!0};var r=Object.assign({},{ipv4:!0,ipv6:!0},e(t.options)),a=/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)(\/([0-9]|[1-2][0-9]|3[0-2]))?$/,n=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*(\/(\d|\d\d|1[0-1]\d|12[0-8]))?$/;switch(!0){case r.ipv4&&!r.ipv6:return{message:t.l10n?r.message||t.l10n.ip.ipv4:r.message,valid:a.test(t.value)};case!r.ipv4&&r.ipv6:return{message:t.l10n?r.message||t.l10n.ip.ipv6:r.message,valid:n.test(t.value)};case r.ipv4&&r.ipv6:default:return{message:t.l10n?r.message||t.l10n.ip.default:r.message,valid:a.test(t.value)||n.test(t.value)}}}}},ot}();var lt,dt=st.exports,ut={exports:{}},ct={};ut.exports=function(){if(lt)return ct;lt=1;var e=i,t=e.utils.format,r=e.utils.removeUndefined;return ct.lessThan=function(){return{validate:function(e){if(""===e.value)return{valid:!0};var a=Object.assign({},{inclusive:!0,message:""},r(e.options)),n=parseFloat("".concat(a.max).replace(",","."));return a.inclusive?{message:t(e.l10n?a.message||e.l10n.lessThan.default:a.message,"".concat(n)),valid:parseFloat(e.value)<=n}:{message:t(e.l10n?a.message||e.l10n.lessThan.notInclusive:a.message,"".concat(n)),valid:parseFloat(e.value)<n}}}},ct}();var ft,pt=ut.exports,vt={exports:{}},ht={};vt.exports=(ft||(ft=1,ht.notEmpty=function(){return{validate:function(e){var t=!!e.options&&!!e.options.trim,r=e.value;return{valid:!t&&""!==r||t&&""!==r&&""!==r.trim()}}}}),ht);var mt,gt=vt.exports,bt={exports:{}},At={};bt.exports=function(){if(mt)return At;mt=1;var e=i.utils.removeUndefined;return At.numeric=function(){return{validate:function(t){if(""===t.value)return{valid:!0};var r=Object.assign({},{decimalSeparator:".",thousandsSeparator:""},e(t.options)),a="".concat(t.value);a.substr(0,1)===r.decimalSeparator?a="0".concat(r.decimalSeparator).concat(a.substr(1)):a.substr(0,2)==="-".concat(r.decimalSeparator)&&(a="-0".concat(r.decimalSeparator).concat(a.substr(2)));var n="."===r.decimalSeparator?"\\.":r.decimalSeparator,i="."===r.thousandsSeparator?"\\.":r.thousandsSeparator,s=new RegExp("^-?[0-9]{1,3}(".concat(i,"[0-9]{3})*(").concat(n,"[0-9]+)?$")),o=new RegExp(i,"g");if(!s.test(a))return{valid:!1};i&&(a=a.replace(o,"")),n&&(a=a.replace(n,"."));var l=parseFloat(a);return{valid:!isNaN(l)&&isFinite(l)}}}},At}();var yt,Et=bt.exports,It={exports:{}},xt={};It.exports=function(){if(yt)return xt;yt=1;var e=i.utils.call;return xt.promise=function(){return{validate:function(t){return e(t.options.promise,[t])}}},xt}();var Ct,Ot=It.exports,Ft={exports:{}},Vt={};Ft.exports=(Ct||(Ct=1,Vt.regexp=function(){return{validate:function(e){if(""===e.value)return{valid:!0};var t=e.options.regexp;if(t instanceof RegExp)return{valid:t.test(e.value)};var r=t.toString();return{valid:(e.options.flags?new RegExp(r,e.options.flags):new RegExp(r)).test(e.value)}}}}),Vt);var St,kt=Ft.exports,wt={exports:{}},Ht={};wt.exports=function(){if(St)return Ht;St=1;var e=i,t=e.utils.fetch,r=e.utils.removeUndefined;return Ht.remote=function(){var e={crossDomain:!1,data:{},headers:{},method:"GET",validKey:"valid"};return{validate:function(a){if(""===a.value)return Promise.resolve({valid:!0});var n=Object.assign({},e,r(a.options)),i=n.data;"function"==typeof n.data&&(i=n.data.call(this,a)),"string"==typeof i&&(i=JSON.parse(i)),i[n.name||a.field]=a.value;var s="function"==typeof n.url?n.url.call(this,a):n.url;return t(s,{crossDomain:n.crossDomain,headers:n.headers,method:n.method,params:i}).then((function(e){return Promise.resolve({message:e.message,meta:e,valid:"true"==="".concat(e[n.validKey])})})).catch((function(e){return Promise.reject({valid:!1})}))}}},Ht}();var $t,Nt=wt.exports,Mt={exports:{}},Tt={};Mt.exports=function(){if($t)return Tt;$t=1;var e=i.utils.removeUndefined;return Tt.stringCase=function(){return{validate:function(t){if(""===t.value)return{valid:!0};var r=Object.assign({},{case:"lower"},e(t.options)),a=(r.case||"lower").toLowerCase();return{message:r.message||(t.l10n?"upper"===a?t.l10n.stringCase.upper:t.l10n.stringCase.default:r.message),valid:"upper"===a?t.value===t.value.toUpperCase():t.value===t.value.toLowerCase()}}}},Tt}();var Lt,Pt=Mt.exports,Rt={exports:{}},Dt={};Rt.exports=function(){if(Lt)return Dt;Lt=1;var e=i,t=e.utils.format,r=e.utils.removeUndefined;return Dt.stringLength=function(){return{validate:function(e){var a=Object.assign({},{message:"",trim:!1,utf8Bytes:!1},r(e.options)),n=!0===a.trim||"true"==="".concat(a.trim)?e.value.trim():e.value;if(""===n)return{valid:!0};var i=a.min?"".concat(a.min):"",s=a.max?"".concat(a.max):"",o=a.utf8Bytes?function(e){for(var t=e.length,r=e.length-1;r>=0;r--){var a=e.charCodeAt(r);a>127&&a<=2047?t++:a>2047&&a<=65535&&(t+=2),a>=56320&&a<=57343&&r--}return t}(n):n.length,l=!0,d=e.l10n?a.message||e.l10n.stringLength.default:a.message;switch((i&&o<parseInt(i,10)||s&&o>parseInt(s,10))&&(l=!1),!0){case!!i&&!!s:d=t(e.l10n?a.message||e.l10n.stringLength.between:a.message,[i,s]);break;case!!i:d=t(e.l10n?a.message||e.l10n.stringLength.more:a.message,"".concat(parseInt(i,10)));break;case!!s:d=t(e.l10n?a.message||e.l10n.stringLength.less:a.message,"".concat(parseInt(s,10)))}return{message:d,valid:l}}}},Dt}();var Zt,_t=Rt.exports,Bt={exports:{}},Gt={};Bt.exports=function(){if(Zt)return Gt;Zt=1;var e=i.utils.removeUndefined;return Gt.uri=function(){var t={allowEmptyProtocol:!1,allowLocal:!1,protocol:"http, https, ftp"};return{validate:function(r){if(""===r.value)return{valid:!0};var a=Object.assign({},t,e(r.options)),n=!0===a.allowLocal||"true"==="".concat(a.allowLocal),i=!0===a.allowEmptyProtocol||"true"==="".concat(a.allowEmptyProtocol),s=a.protocol.split(",").join("|").replace(/\s/g,"");return{valid:new RegExp("^(?:(?:"+s+")://)"+(i?"?":"")+"(?:\\S+(?::\\S*)?@)?(?:"+(n?"":"(?!(?:10|127)(?:\\.\\d{1,3}){3})(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})")+"(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]-?)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-?)*[a-z\\u00a1-\\uffff0-9])*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))"+(n?"?":"")+")(?::\\d{2,5})?(?:/[^\\s]*)?$","i").test(r.value)}}}},Gt}();var Ut,jt=Bt.exports,Kt={exports:{}},zt={};Kt.exports=(Ut||(Ut=1,zt.base64=function(){return{validate:function(e){return{valid:""===e.value||/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{4})$/.test(e.value)}}}}),zt);var Yt,Jt=Kt.exports,Xt={exports:{}},Wt={};Xt.exports=(Yt||(Yt=1,Wt.bic=function(){return{validate:function(e){return{valid:""===e.value||/^[a-zA-Z]{6}[a-zA-Z0-9]{2}([a-zA-Z0-9]{3})?$/.test(e.value)}}}}),Wt);var qt,Qt=Xt.exports,er={exports:{}},tr={};er.exports=(qt||(qt=1,tr.color=function(){var e=["hex","rgb","rgba","hsl","hsla","keyword"],t=["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","green","greenyellow","grey","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","transparent","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"],r=function(e){return/^hsl\((\s*(-?\d+)\s*,)(\s*(\b(0?\d{1,2}|100)\b%)\s*,)(\s*(\b(0?\d{1,2}|100)\b%)\s*)\)$/.test(e)},a=function(e){return/^hsla\((\s*(-?\d+)\s*,)(\s*(\b(0?\d{1,2}|100)\b%)\s*,){2}(\s*(0?(\.\d+)?|1(\.0+)?)\s*)\)$/.test(e)},n=function(e){return t.indexOf(e)>=0},i=function(e){return/^rgb\((\s*(\b([01]?\d{1,2}|2[0-4]\d|25[0-5])\b)\s*,){2}(\s*(\b([01]?\d{1,2}|2[0-4]\d|25[0-5])\b)\s*)\)$/.test(e)||/^rgb\((\s*(\b(0?\d{1,2}|100)\b%)\s*,){2}(\s*(\b(0?\d{1,2}|100)\b%)\s*)\)$/.test(e)},s=function(e){return/^rgba\((\s*(\b([01]?\d{1,2}|2[0-4]\d|25[0-5])\b)\s*,){3}(\s*(0?(\.\d+)?|1(\.0+)?)\s*)\)$/.test(e)||/^rgba\((\s*(\b(0?\d{1,2}|100)\b%)\s*,){3}(\s*(0?(\.\d+)?|1(\.0+)?)\s*)\)$/.test(e)};return{validate:function(t){if(""===t.value)return{valid:!0};for(var o,l=0,d="string"==typeof t.options.type?t.options.type.toString().replace(/s/g,"").split(","):t.options.type||e;l<d.length;l++){var u=d[l].toLowerCase();if(-1!==e.indexOf(u)){var c=!0;switch(u){case"hex":o=t.value,c=/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(o);break;case"hsl":c=r(t.value);break;case"hsla":c=a(t.value);break;case"keyword":c=n(t.value);break;case"rgb":c=i(t.value);break;case"rgba":c=s(t.value)}if(c)return{valid:!0}}}return{valid:!1}}}}),tr);var rr,ar=er.exports,nr={exports:{}},ir={};nr.exports=(rr||(rr=1,ir.cusip=function(){return{validate:function(e){if(""===e.value)return{valid:!0};var t=e.value.toUpperCase();if(!/^[0123456789ABCDEFGHJKLMNPQRSTUVWXYZ*@#]{9}$/.test(t))return{valid:!1};var r=t.split(""),a=r.pop(),n=r.map((function(e){var t=e.charCodeAt(0);switch(!0){case"*"===e:return 36;case"@"===e:return 37;case"#"===e:return 38;case t>="A".charCodeAt(0)&&t<="Z".charCodeAt(0):return t-"A".charCodeAt(0)+10;default:return parseInt(e,10)}})).map((function(e,t){var r=t%2==0?e:2*e;return Math.floor(r/10)+r%10})).reduce((function(e,t){return e+t}),0);return{valid:a==="".concat((10-n%10)%10)}}}}),ir);var sr,or=nr.exports,lr={exports:{}},dr={};lr.exports=(sr||(sr=1,dr.ean=function(){return{validate:function(e){if(""===e.value)return{valid:!0};if(!/^(\d{8}|\d{12}|\d{13}|\d{14})$/.test(e.value))return{valid:!1};for(var t=e.value.length,r=0,a=8===t?[3,1]:[1,3],n=0;n<t-1;n++)r+=parseInt(e.value.charAt(n),10)*a[n%2];return{valid:"".concat(r=(10-r%10)%10)===e.value.charAt(t-1)}}}}),dr);var ur,cr=lr.exports,fr={exports:{}},pr={};fr.exports=(ur||(ur=1,pr.ein=function(){var e={ANDOVER:["10","12"],ATLANTA:["60","67"],AUSTIN:["50","53"],BROOKHAVEN:["01","02","03","04","05","06","11","13","14","16","21","22","23","25","34","51","52","54","55","56","57","58","59","65"],CINCINNATI:["30","32","35","36","37","38","61"],FRESNO:["15","24"],INTERNET:["20","26","27","45","46","47"],KANSAS_CITY:["40","44"],MEMPHIS:["94","95"],OGDEN:["80","90"],PHILADELPHIA:["33","39","41","42","43","48","62","63","64","66","68","71","72","73","74","75","76","77","81","82","83","84","85","86","87","88","91","92","93","98","99"],SMALL_BUSINESS_ADMINISTRATION:["31"]};return{validate:function(t){if(""===t.value)return{meta:null,valid:!0};if(!/^[0-9]{2}-?[0-9]{7}$/.test(t.value))return{meta:null,valid:!1};var r="".concat(t.value.substr(0,2));for(var a in e)if(-1!==e[a].indexOf(r))return{meta:{campus:a},valid:!0};return{meta:null,valid:!1}}}}),pr);var vr,hr=fr.exports,mr={exports:{}},gr={};mr.exports=function(){if(vr)return gr;vr=1;var e=i.algorithms.mod37And36;return gr.grid=function(){return{validate:function(t){if(""===t.value)return{valid:!0};var r=t.value.toUpperCase();return/^[GRID:]*([0-9A-Z]{2})[-\s]*([0-9A-Z]{5})[-\s]*([0-9A-Z]{10})[-\s]*([0-9A-Z]{1})$/g.test(r)?("GRID:"===(r=r.replace(/\s/g,"").replace(/-/g,"")).substr(0,5)&&(r=r.substr(5)),{valid:e(r)}):{valid:!1}}}},gr}();var br,Ar=mr.exports,yr={exports:{}},Er={};yr.exports=(br||(br=1,Er.hex=function(){return{validate:function(e){return{valid:""===e.value||/^[0-9a-fA-F]+$/.test(e.value)}}}}),Er);var Ir,xr=yr.exports,Cr={exports:{}},Or={};Cr.exports=function(){if(Ir)return Or;Ir=1;var e=i,t=e.utils.format,r=e.utils.removeUndefined;return Or.iban=function(){var e={AD:"AD[0-9]{2}[0-9]{4}[0-9]{4}[A-Z0-9]{12}",AE:"AE[0-9]{2}[0-9]{3}[0-9]{16}",AL:"AL[0-9]{2}[0-9]{8}[A-Z0-9]{16}",AO:"AO[0-9]{2}[0-9]{21}",AT:"AT[0-9]{2}[0-9]{5}[0-9]{11}",AZ:"AZ[0-9]{2}[A-Z]{4}[A-Z0-9]{20}",BA:"BA[0-9]{2}[0-9]{3}[0-9]{3}[0-9]{8}[0-9]{2}",BE:"BE[0-9]{2}[0-9]{3}[0-9]{7}[0-9]{2}",BF:"BF[0-9]{2}[0-9]{23}",BG:"BG[0-9]{2}[A-Z]{4}[0-9]{4}[0-9]{2}[A-Z0-9]{8}",BH:"BH[0-9]{2}[A-Z]{4}[A-Z0-9]{14}",BI:"BI[0-9]{2}[0-9]{12}",BJ:"BJ[0-9]{2}[A-Z]{1}[0-9]{23}",BR:"BR[0-9]{2}[0-9]{8}[0-9]{5}[0-9]{10}[A-Z][A-Z0-9]",CH:"CH[0-9]{2}[0-9]{5}[A-Z0-9]{12}",CI:"CI[0-9]{2}[A-Z]{1}[0-9]{23}",CM:"CM[0-9]{2}[0-9]{23}",CR:"CR[0-9]{2}[0-9][0-9]{3}[0-9]{14}",CV:"CV[0-9]{2}[0-9]{21}",CY:"CY[0-9]{2}[0-9]{3}[0-9]{5}[A-Z0-9]{16}",CZ:"CZ[0-9]{2}[0-9]{20}",DE:"DE[0-9]{2}[0-9]{8}[0-9]{10}",DK:"DK[0-9]{2}[0-9]{14}",DO:"DO[0-9]{2}[A-Z0-9]{4}[0-9]{20}",DZ:"DZ[0-9]{2}[0-9]{20}",EE:"EE[0-9]{2}[0-9]{2}[0-9]{2}[0-9]{11}[0-9]{1}",ES:"ES[0-9]{2}[0-9]{4}[0-9]{4}[0-9]{1}[0-9]{1}[0-9]{10}",FI:"FI[0-9]{2}[0-9]{6}[0-9]{7}[0-9]{1}",FO:"FO[0-9]{2}[0-9]{4}[0-9]{9}[0-9]{1}",FR:"FR[0-9]{2}[0-9]{5}[0-9]{5}[A-Z0-9]{11}[0-9]{2}",GB:"GB[0-9]{2}[A-Z]{4}[0-9]{6}[0-9]{8}",GE:"GE[0-9]{2}[A-Z]{2}[0-9]{16}",GI:"GI[0-9]{2}[A-Z]{4}[A-Z0-9]{15}",GL:"GL[0-9]{2}[0-9]{4}[0-9]{9}[0-9]{1}",GR:"GR[0-9]{2}[0-9]{3}[0-9]{4}[A-Z0-9]{16}",GT:"GT[0-9]{2}[A-Z0-9]{4}[A-Z0-9]{20}",HR:"HR[0-9]{2}[0-9]{7}[0-9]{10}",HU:"HU[0-9]{2}[0-9]{3}[0-9]{4}[0-9]{1}[0-9]{15}[0-9]{1}",IE:"IE[0-9]{2}[A-Z]{4}[0-9]{6}[0-9]{8}",IL:"IL[0-9]{2}[0-9]{3}[0-9]{3}[0-9]{13}",IR:"IR[0-9]{2}[0-9]{22}",IS:"IS[0-9]{2}[0-9]{4}[0-9]{2}[0-9]{6}[0-9]{10}",IT:"IT[0-9]{2}[A-Z]{1}[0-9]{5}[0-9]{5}[A-Z0-9]{12}",JO:"JO[0-9]{2}[A-Z]{4}[0-9]{4}[0]{8}[A-Z0-9]{10}",KW:"KW[0-9]{2}[A-Z]{4}[0-9]{22}",KZ:"KZ[0-9]{2}[0-9]{3}[A-Z0-9]{13}",LB:"LB[0-9]{2}[0-9]{4}[A-Z0-9]{20}",LI:"LI[0-9]{2}[0-9]{5}[A-Z0-9]{12}",LT:"LT[0-9]{2}[0-9]{5}[0-9]{11}",LU:"LU[0-9]{2}[0-9]{3}[A-Z0-9]{13}",LV:"LV[0-9]{2}[A-Z]{4}[A-Z0-9]{13}",MC:"MC[0-9]{2}[0-9]{5}[0-9]{5}[A-Z0-9]{11}[0-9]{2}",MD:"MD[0-9]{2}[A-Z0-9]{20}",ME:"ME[0-9]{2}[0-9]{3}[0-9]{13}[0-9]{2}",MG:"MG[0-9]{2}[0-9]{23}",MK:"MK[0-9]{2}[0-9]{3}[A-Z0-9]{10}[0-9]{2}",ML:"ML[0-9]{2}[A-Z]{1}[0-9]{23}",MR:"MR13[0-9]{5}[0-9]{5}[0-9]{11}[0-9]{2}",MT:"MT[0-9]{2}[A-Z]{4}[0-9]{5}[A-Z0-9]{18}",MU:"MU[0-9]{2}[A-Z]{4}[0-9]{2}[0-9]{2}[0-9]{12}[0-9]{3}[A-Z]{3}",MZ:"MZ[0-9]{2}[0-9]{21}",NL:"NL[0-9]{2}[A-Z]{4}[0-9]{10}",NO:"NO[0-9]{2}[0-9]{4}[0-9]{6}[0-9]{1}",PK:"PK[0-9]{2}[A-Z]{4}[A-Z0-9]{16}",PL:"PL[0-9]{2}[0-9]{8}[0-9]{16}",PS:"PS[0-9]{2}[A-Z]{4}[A-Z0-9]{21}",PT:"PT[0-9]{2}[0-9]{4}[0-9]{4}[0-9]{11}[0-9]{2}",QA:"QA[0-9]{2}[A-Z]{4}[A-Z0-9]{21}",RO:"RO[0-9]{2}[A-Z]{4}[A-Z0-9]{16}",RS:"RS[0-9]{2}[0-9]{3}[0-9]{13}[0-9]{2}",SA:"SA[0-9]{2}[0-9]{2}[A-Z0-9]{18}",SE:"SE[0-9]{2}[0-9]{3}[0-9]{16}[0-9]{1}",SI:"SI[0-9]{2}[0-9]{5}[0-9]{8}[0-9]{2}",SK:"SK[0-9]{2}[0-9]{4}[0-9]{6}[0-9]{10}",SM:"SM[0-9]{2}[A-Z]{1}[0-9]{5}[0-9]{5}[A-Z0-9]{12}",SN:"SN[0-9]{2}[A-Z]{1}[0-9]{23}",TL:"TL38[0-9]{3}[0-9]{14}[0-9]{2}",TN:"TN59[0-9]{2}[0-9]{3}[0-9]{13}[0-9]{2}",TR:"TR[0-9]{2}[0-9]{5}[A-Z0-9]{1}[A-Z0-9]{16}",VG:"VG[0-9]{2}[A-Z]{4}[0-9]{16}",XK:"XK[0-9]{2}[0-9]{4}[0-9]{10}[0-9]{2}"},a=["AT","BE","BG","CH","CY","CZ","DE","DK","EE","ES","FI","FR","GB","GI","GR","HR","HU","IE","IS","IT","LI","LT","LU","LV","MC","MT","NL","NO","PL","PT","RO","SE","SI","SK","SM"];return{validate:function(n){if(""===n.value)return{valid:!0};var i=Object.assign({},{message:""},r(n.options)),s=n.value.replace(/[^a-zA-Z0-9]/g,"").toUpperCase(),o=i.country||s.substr(0,2);if(!e[o])return{message:i.message,valid:!1};if(void 0!==i.sepa){var l=-1!==a.indexOf(o);if(("true"===i.sepa||!0===i.sepa)&&!l||("false"===i.sepa||!1===i.sepa)&&l)return{message:i.message,valid:!1}}var d=t(n.l10n?i.message||n.l10n.iban.country:i.message,n.l10n?n.l10n.iban.countries[o]:o);if(!new RegExp("^".concat(e[o],"$")).test(n.value))return{message:d,valid:!1};s=(s="".concat(s.substr(4)).concat(s.substr(0,4))).split("").map((function(e){var t=e.charCodeAt(0);return t>="A".charCodeAt(0)&&t<="Z".charCodeAt(0)?t-"A".charCodeAt(0)+10:e})).join("");for(var u=parseInt(s.substr(0,1),10),c=s.length,f=1;f<c;++f)u=(10*u+parseInt(s.substr(f,1),10))%97;return{message:d,valid:1===u}}}},Or}();var Fr,Vr=Cr.exports,Sr={exports:{}},kr={};Sr.exports=function(){if(Fr)return kr;Fr=1;var e=i;function t(e,t){if(!/^\d{13}$/.test(e))return!1;var r=parseInt(e.substr(0,2),10),a=parseInt(e.substr(2,2),10),n=parseInt(e.substr(7,2),10),i=parseInt(e.substr(12,1),10);if(r>31||a>12)return!1;for(var s=0,o=0;o<6;o++)s+=(7-o)*(parseInt(e.charAt(o),10)+parseInt(e.charAt(o+6),10));if(10!=(s=11-s%11)&&11!==s||(s=0),s!==i)return!1;switch(t.toUpperCase()){case"BA":return 10<=n&&n<=19;case"MK":return 41<=n&&n<=49;case"ME":return 20<=n&&n<=29;case"RS":return 70<=n&&n<=99;case"SI":return 50<=n&&n<=59;default:return!0}}var r=e.utils.isValidDate,a=e.utils.isValidDate,n=e.utils.isValidDate;function s(e){if(!/^\d{9,10}$/.test(e))return{meta:{},valid:!1};var t=1900+parseInt(e.substr(0,2),10),r=parseInt(e.substr(2,2),10)%50%20,a=parseInt(e.substr(4,2),10);if(9===e.length){if(t>=1980&&(t-=100),t>1953)return{meta:{},valid:!1}}else t<1954&&(t+=100);if(!n(t,r,a))return{meta:{},valid:!1};if(10===e.length){var i=parseInt(e.substr(0,9),10)%11;return t<1985&&(i%=10),{meta:{},valid:"".concat(i)===e.substr(9,1)}}return{meta:{},valid:!0}}var o=e.utils.isValidDate,l=e.utils.isValidDate,d=e.algorithms.mod11And10,u=e.algorithms.verhoeff,c=e.algorithms.luhn,f=e.utils.isValidDate,p=e.utils.isValidDate,v=e.utils.isValidDate;function h(e){if(!/^[0-9]{11}$/.test(e))return{meta:{},valid:!1};var t=parseInt(e.charAt(0),10),r=parseInt(e.substr(1,2),10),a=parseInt(e.substr(3,2),10),n=parseInt(e.substr(5,2),10);if(!v(r=100*(t%2==0?17+t/2:17+(t+1)/2)+r,a,n,!0))return{meta:{},valid:!1};var i,s=[1,2,3,4,5,6,7,8,9,1],o=0;for(i=0;i<10;i++)o+=parseInt(e.charAt(i),10)*s[i];if(10!=(o%=11))return{meta:{},valid:"".concat(o)===e.charAt(10)};for(o=0,s=[3,4,5,6,7,8,9,1,2,3],i=0;i<10;i++)o+=parseInt(e.charAt(i),10)*s[i];return 10==(o%=11)&&(o=0),{meta:{},valid:"".concat(o)===e.charAt(10)}}var m=e.utils.isValidDate,g=e.utils.isValidDate,b=e.utils.isValidDate,A=e.utils.isValidDate,y=e.algorithms.luhn,E=e.utils.isValidDate,I=e.algorithms.luhn,x=e.utils.isValidDate,C=e.utils.format,O=e.utils.removeUndefined;return kr.id=function(){var e=["AR","BA","BG","BR","CH","CL","CN","CO","CZ","DK","EE","ES","FI","FR","HK","HR","ID","IE","IL","IS","KR","LT","LV","ME","MK","MX","MY","NL","NO","PE","PL","RO","RS","SE","SI","SK","SM","TH","TR","TW","UY","ZA"];return{validate:function(n){if(""===n.value)return{valid:!0};var i=Object.assign({},{message:""},O(n.options)),v=n.value.substr(0,2);if(v="function"==typeof i.country?i.country.call(this):i.country,-1===e.indexOf(v))return{valid:!0};var F,V={meta:{},valid:!0};switch(v.toLowerCase()){case"ar":F=n.value.replace(/\./g,""),V={meta:{},valid:/^\d{7,8}$/.test(F)};break;case"ba":V=function(e){return{meta:{},valid:t(e,"BA")}}(n.value);break;case"bg":V=function(e){if(!/^\d{10}$/.test(e)&&!/^\d{6}\s\d{3}\s\d{1}$/.test(e))return{meta:{},valid:!1};var t=e.replace(/\s/g,""),a=parseInt(t.substr(0,2),10)+1900,n=parseInt(t.substr(2,2),10),i=parseInt(t.substr(4,2),10);if(n>40?(a+=100,n-=40):n>20&&(a-=100,n-=20),!r(a,n,i))return{meta:{},valid:!1};for(var s=0,o=[2,4,8,5,10,9,7,3,6],l=0;l<9;l++)s+=parseInt(t.charAt(l),10)*o[l];return{meta:{},valid:"".concat(s=s%11%10)===t.substr(9,1)}}(n.value);break;case"br":V=function(e){var t=e.replace(/\D/g,"");if(!/^\d{11}$/.test(t)||/^1{11}|2{11}|3{11}|4{11}|5{11}|6{11}|7{11}|8{11}|9{11}|0{11}$/.test(t))return{meta:{},valid:!1};var r,a=0;for(r=0;r<9;r++)a+=(10-r)*parseInt(t.charAt(r),10);if(10!=(a=11-a%11)&&11!==a||(a=0),"".concat(a)!==t.charAt(9))return{meta:{},valid:!1};var n=0;for(r=0;r<10;r++)n+=(11-r)*parseInt(t.charAt(r),10);return 10!=(n=11-n%11)&&11!==n||(n=0),{meta:{},valid:"".concat(n)===t.charAt(10)}}(n.value);break;case"ch":V=function(e){if(!/^756[.]{0,1}[0-9]{4}[.]{0,1}[0-9]{4}[.]{0,1}[0-9]{2}$/.test(e))return{meta:{},valid:!1};for(var t=e.replace(/\D/g,"").substr(3),r=t.length,a=8===r?[3,1]:[1,3],n=0,i=0;i<r-1;i++)n+=parseInt(t.charAt(i),10)*a[i%2];return{meta:{},valid:"".concat(n=10-n%10)===t.charAt(r-1)}}(n.value);break;case"cl":V=function(e){if(!/^\d{7,8}[-]{0,1}[0-9K]$/i.test(e))return{meta:{},valid:!1};for(var t=e.replace(/-/g,"");t.length<9;)t="0".concat(t);for(var r=[3,2,7,6,5,4,3,2],a=0,n=0;n<8;n++)a+=parseInt(t.charAt(n),10)*r[n];var i="".concat(a=11-a%11);return 11===a?i="0":10===a&&(i="K"),{meta:{},valid:i===t.charAt(8).toUpperCase()}}(n.value);break;case"cn":V=function(e){var t=e.trim();if(!/^\d{15}$/.test(t)&&!/^\d{17}[\dXx]{1}$/.test(t))return{meta:{},valid:!1};var r={11:{0:[0],1:[[0,9],[11,17]],2:[0,28,29]},12:{0:[0],1:[[0,16]],2:[0,21,23,25]},13:{0:[0],1:[[0,5],7,8,21,[23,33],[81,85]],2:[[0,5],[7,9],[23,25],27,29,30,81,83],3:[[0,4],[21,24]],4:[[0,4],6,21,[23,35],81],5:[[0,3],[21,35],81,82],6:[[0,4],[21,38],[81,84]],7:[[0,3],5,6,[21,33]],8:[[0,4],[21,28]],9:[[0,3],[21,30],[81,84]],10:[[0,3],[22,26],28,81,82],11:[[0,2],[21,28],81,82]},14:{0:[0],1:[0,1,[5,10],[21,23],81],2:[[0,3],11,12,[21,27]],3:[[0,3],11,21,22],4:[[0,2],11,21,[23,31],81],5:[[0,2],21,22,24,25,81],6:[[0,3],[21,24]],7:[[0,2],[21,29],81],8:[[0,2],[21,30],81,82],9:[[0,2],[21,32],81],10:[[0,2],[21,34],81,82],11:[[0,2],[21,30],81,82],23:[[0,3],22,23,[25,30],32,33]},15:{0:[0],1:[[0,5],[21,25]],2:[[0,7],[21,23]],3:[[0,4]],4:[[0,4],[21,26],[28,30]],5:[[0,2],[21,26],81],6:[[0,2],[21,27]],7:[[0,3],[21,27],[81,85]],8:[[0,2],[21,26]],9:[[0,2],[21,29],81],22:[[0,2],[21,24]],25:[[0,2],[22,31]],26:[[0,2],[24,27],[29,32],34],28:[0,1,[22,27]],29:[0,[21,23]]},21:{0:[0],1:[[0,6],[11,14],[22,24],81],2:[[0,4],[11,13],24,[81,83]],3:[[0,4],11,21,23,81],4:[[0,4],11,[21,23]],5:[[0,5],21,22],6:[[0,4],24,81,82],7:[[0,3],11,26,27,81,82],8:[[0,4],11,81,82],9:[[0,5],11,21,22],10:[[0,5],11,21,81],11:[[0,3],21,22],12:[[0,2],4,21,23,24,81,82],13:[[0,3],21,22,24,81,82],14:[[0,4],21,22,81]},22:{0:[0],1:[[0,6],12,22,[81,83]],2:[[0,4],11,21,[81,84]],3:[[0,3],22,23,81,82],4:[[0,3],21,22],5:[[0,3],21,23,24,81,82],6:[[0,2],4,5,[21,23],25,81],7:[[0,2],[21,24],81],8:[[0,2],21,22,81,82],24:[[0,6],24,26]},23:{0:[0],1:[[0,12],21,[23,29],[81,84]],2:[[0,8],21,[23,25],27,[29,31],81],3:[[0,7],21,81,82],4:[[0,7],21,22],5:[[0,3],5,6,[21,24]],6:[[0,6],[21,24]],7:[[0,16],22,81],8:[[0,5],11,22,26,28,33,81,82],9:[[0,4],21],10:[[0,5],24,25,81,[83,85]],11:[[0,2],21,23,24,81,82],12:[[0,2],[21,26],[81,83]],27:[[0,4],[21,23]]},31:{0:[0],1:[0,1,[3,10],[12,20]],2:[0,30]},32:{0:[0],1:[[0,7],11,[13,18],24,25],2:[[0,6],11,81,82],3:[[0,5],11,12,[21,24],81,82],4:[[0,2],4,5,11,12,81,82],5:[[0,9],[81,85]],6:[[0,2],11,12,21,23,[81,84]],7:[0,1,3,5,6,[21,24]],8:[[0,4],11,26,[29,31]],9:[[0,3],[21,25],28,81,82],10:[[0,3],11,12,23,81,84,88],11:[[0,2],11,12,[81,83]],12:[[0,4],[81,84]],13:[[0,2],11,[21,24]]},33:{0:[0],1:[[0,6],[8,10],22,27,82,83,85],2:[0,1,[3,6],11,12,25,26,[81,83]],3:[[0,4],22,24,[26,29],81,82],4:[[0,2],11,21,24,[81,83]],5:[[0,3],[21,23]],6:[[0,2],21,24,[81,83]],7:[[0,3],23,26,27,[81,84]],8:[[0,3],22,24,25,81],9:[[0,3],21,22],10:[[0,4],[21,24],81,82],11:[[0,2],[21,27],81]},34:{0:[0],1:[[0,4],11,[21,24],81],2:[[0,4],7,8,[21,23],25],3:[[0,4],11,[21,23]],4:[[0,6],21],5:[[0,4],6,[21,23]],6:[[0,4],21],7:[[0,3],11,21],8:[[0,3],11,[22,28],81],10:[[0,4],[21,24]],11:[[0,3],22,[24,26],81,82],12:[[0,4],21,22,25,26,82],13:[[0,2],[21,24]],14:[[0,2],[21,24]],15:[[0,3],[21,25]],16:[[0,2],[21,23]],17:[[0,2],[21,23]],18:[[0,2],[21,25],81]},35:{0:[0],1:[[0,5],11,[21,25],28,81,82],2:[[0,6],[11,13]],3:[[0,5],22],4:[[0,3],21,[23,30],81],5:[[0,5],21,[24,27],[81,83]],6:[[0,3],[22,29],81],7:[[0,2],[21,25],[81,84]],8:[[0,2],[21,25],81],9:[[0,2],[21,26],81,82]},36:{0:[0],1:[[0,5],11,[21,24]],2:[[0,3],22,81],3:[[0,2],13,[21,23]],4:[[0,3],21,[23,30],81,82],5:[[0,2],21],6:[[0,2],22,81],7:[[0,2],[21,35],81,82],8:[[0,3],[21,30],81],9:[[0,2],[21,26],[81,83]],10:[[0,2],[21,30]],11:[[0,2],[21,30],81]},37:{0:[0],1:[[0,5],12,13,[24,26],81],2:[[0,3],5,[11,14],[81,85]],3:[[0,6],[21,23]],4:[[0,6],81],5:[[0,3],[21,23]],6:[[0,2],[11,13],34,[81,87]],7:[[0,5],24,25,[81,86]],8:[[0,2],11,[26,32],[81,83]],9:[[0,3],11,21,23,82,83],10:[[0,2],[81,83]],11:[[0,3],21,22],12:[[0,3]],13:[[0,2],11,12,[21,29]],14:[[0,2],[21,28],81,82],15:[[0,2],[21,26],81],16:[[0,2],[21,26]],17:[[0,2],[21,28]]},41:{0:[0],1:[[0,6],8,22,[81,85]],2:[[0,5],11,[21,25]],3:[[0,7],11,[22,29],81],4:[[0,4],11,[21,23],25,81,82],5:[[0,3],5,6,22,23,26,27,81],6:[[0,3],11,21,22],7:[[0,4],11,21,[24,28],81,82],8:[[0,4],11,[21,23],25,[81,83]],9:[[0,2],22,23,[26,28]],10:[[0,2],[23,25],81,82],11:[[0,4],[21,23]],12:[[0,2],21,22,24,81,82],13:[[0,3],[21,30],81],14:[[0,3],[21,26],81],15:[[0,3],[21,28]],16:[[0,2],[21,28],81],17:[[0,2],[21,29]],90:[0,1]},42:{0:[0],1:[[0,7],[11,17]],2:[[0,5],22,81],3:[[0,3],[21,25],81],5:[[0,6],[25,29],[81,83]],6:[[0,2],6,7,[24,26],[82,84]],7:[[0,4]],8:[[0,2],4,21,22,81],9:[[0,2],[21,23],81,82,84],10:[[0,3],[22,24],81,83,87],11:[[0,2],[21,27],81,82],12:[[0,2],[21,24],81],13:[[0,3],21,81],28:[[0,2],22,23,[25,28]],90:[0,[4,6],21]},43:{0:[0],1:[[0,5],11,12,21,22,24,81],2:[[0,4],11,21,[23,25],81],3:[[0,2],4,21,81,82],4:[0,1,[5,8],12,[21,24],26,81,82],5:[[0,3],11,[21,25],[27,29],81],6:[[0,3],11,21,23,24,26,81,82],7:[[0,3],[21,26],81],8:[[0,2],11,21,22],9:[[0,3],[21,23],81],10:[[0,3],[21,28],81],11:[[0,3],[21,29]],12:[[0,2],[21,30],81],13:[[0,2],21,22,81,82],31:[0,1,[22,27],30]},44:{0:[0],1:[[0,7],[11,16],83,84],2:[[0,5],21,22,24,29,32,33,81,82],3:[0,1,[3,8]],4:[[0,4]],5:[0,1,[6,15],23,82,83],6:[0,1,[4,8]],7:[0,1,[3,5],81,[83,85]],8:[[0,4],11,23,25,[81,83]],9:[[0,3],23,[81,83]],12:[[0,3],[23,26],83,84],13:[[0,3],[22,24],81],14:[[0,2],[21,24],26,27,81],15:[[0,2],21,23,81],16:[[0,2],[21,25]],17:[[0,2],21,23,81],18:[[0,3],21,23,[25,27],81,82],19:[0],20:[0],51:[[0,3],21,22],52:[[0,3],21,22,24,81],53:[[0,2],[21,23],81]},45:{0:[0],1:[[0,9],[21,27]],2:[[0,5],[21,26]],3:[[0,5],11,12,[21,32]],4:[0,1,[3,6],11,[21,23],81],5:[[0,3],12,21],6:[[0,3],21,81],7:[[0,3],21,22],8:[[0,4],21,81],9:[[0,3],[21,24],81],10:[[0,2],[21,31]],11:[[0,2],[21,23]],12:[[0,2],[21,29],81],13:[[0,2],[21,24],81],14:[[0,2],[21,25],81]},46:{0:[0],1:[0,1,[5,8]],2:[0,1],3:[0,[21,23]],90:[[0,3],[5,7],[21,39]]},50:{0:[0],1:[[0,19]],2:[0,[22,38],[40,43]],3:[0,[81,84]]},51:{0:[0],1:[0,1,[4,8],[12,15],[21,24],29,31,32,[81,84]],3:[[0,4],11,21,22],4:[[0,3],11,21,22],5:[[0,4],21,22,24,25],6:[0,1,3,23,26,[81,83]],7:[0,1,3,4,[22,27],81],8:[[0,2],11,12,[21,24]],9:[[0,4],[21,23]],10:[[0,2],11,24,25,28],11:[[0,2],[11,13],23,24,26,29,32,33,81],13:[[0,4],[21,25],81],14:[[0,2],[21,25]],15:[[0,3],[21,29]],16:[[0,3],[21,23],81],17:[[0,3],[21,25],81],18:[[0,3],[21,27]],19:[[0,3],[21,23]],20:[[0,2],21,22,81],32:[0,[21,33]],33:[0,[21,38]],34:[0,1,[22,37]]},52:{0:[0],1:[[0,3],[11,15],[21,23],81],2:[0,1,3,21,22],3:[[0,3],[21,30],81,82],4:[[0,2],[21,25]],5:[[0,2],[21,27]],6:[[0,3],[21,28]],22:[0,1,[22,30]],23:[0,1,[22,28]],24:[0,1,[22,28]],26:[0,1,[22,36]],27:[[0,2],22,23,[25,32]]},53:{0:[0],1:[[0,3],[11,14],21,22,[24,29],81],3:[[0,2],[21,26],28,81],4:[[0,2],[21,28]],5:[[0,2],[21,24]],6:[[0,2],[21,30]],7:[[0,2],[21,24]],8:[[0,2],[21,29]],9:[[0,2],[21,27]],23:[0,1,[22,29],31],25:[[0,4],[22,32]],26:[0,1,[21,28]],27:[0,1,[22,30]],28:[0,1,22,23],29:[0,1,[22,32]],31:[0,2,3,[22,24]],34:[0,[21,23]],33:[0,21,[23,25]],35:[0,[21,28]]},54:{0:[0],1:[[0,2],[21,27]],21:[0,[21,29],32,33],22:[0,[21,29],[31,33]],23:[0,1,[22,38]],24:[0,[21,31]],25:[0,[21,27]],26:[0,[21,27]]},61:{0:[0],1:[[0,4],[11,16],22,[24,26]],2:[[0,4],22],3:[[0,4],[21,24],[26,31]],4:[[0,4],[22,31],81],5:[[0,2],[21,28],81,82],6:[[0,2],[21,32]],7:[[0,2],[21,30]],8:[[0,2],[21,31]],9:[[0,2],[21,29]],10:[[0,2],[21,26]]},62:{0:[0],1:[[0,5],11,[21,23]],2:[0,1],3:[[0,2],21],4:[[0,3],[21,23]],5:[[0,3],[21,25]],6:[[0,2],[21,23]],7:[[0,2],[21,25]],8:[[0,2],[21,26]],9:[[0,2],[21,24],81,82],10:[[0,2],[21,27]],11:[[0,2],[21,26]],12:[[0,2],[21,28]],24:[0,21,[24,29]],26:[0,21,[23,30]],29:[0,1,[21,27]],30:[0,1,[21,27]]},63:{0:[0],1:[[0,5],[21,23]],2:[0,2,[21,25]],21:[0,[21,23],[26,28]],22:[0,[21,24]],23:[0,[21,24]],25:[0,[21,25]],26:[0,[21,26]],27:[0,1,[21,26]],28:[[0,2],[21,23]]},64:{0:[0],1:[0,1,[4,6],21,22,81],2:[[0,3],5,[21,23]],3:[[0,3],[21,24],81],4:[[0,2],[21,25]],5:[[0,2],21,22]},65:{0:[0],1:[[0,9],21],2:[[0,5]],21:[0,1,22,23],22:[0,1,22,23],23:[[0,3],[23,25],27,28],28:[0,1,[22,29]],29:[0,1,[22,29]],30:[0,1,[22,24]],31:[0,1,[21,31]],32:[0,1,[21,27]],40:[0,2,3,[21,28]],42:[[0,2],21,[23,26]],43:[0,1,[21,26]],90:[[0,4]],27:[[0,2],22,23]},71:{0:[0]},81:{0:[0]},82:{0:[0]}},n=parseInt(t.substr(0,2),10),i=parseInt(t.substr(2,2),10),s=parseInt(t.substr(4,2),10);if(!r[n]||!r[n][i])return{meta:{},valid:!1};var o,l,d=!1,u=r[n][i];for(o=0;o<u.length;o++)if(Array.isArray(u[o])&&u[o][0]<=s&&s<=u[o][1]||!Array.isArray(u[o])&&s===u[o]){d=!0;break}if(!d)return{meta:{},valid:!1};l=18===t.length?t.substr(6,8):"19".concat(t.substr(6,6));var c=parseInt(l.substr(0,4),10),f=parseInt(l.substr(4,2),10),p=parseInt(l.substr(6,2),10);if(!a(c,f,p))return{meta:{},valid:!1};if(18===t.length){var v=[7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2],h=0;for(o=0;o<17;o++)h+=parseInt(t.charAt(o),10)*v[o];return h=(12-h%11)%11,{meta:{},valid:("X"!==t.charAt(17).toUpperCase()?parseInt(t.charAt(17),10):10)===h}}return{meta:{},valid:!0}}(n.value);break;case"co":V=function(e){var t=e.replace(/\./g,"").replace("-","");if(!/^\d{8,16}$/.test(t))return{meta:{},valid:!1};for(var r=t.length,a=[3,7,13,17,19,23,29,37,41,43,47,53,59,67,71],n=0,i=r-2;i>=0;i--)n+=parseInt(t.charAt(i),10)*a[i];return(n%=11)>=2&&(n=11-n),{meta:{},valid:"".concat(n)===t.substr(r-1)}}(n.value);break;case"cz":case"sk":V=s(n.value);break;case"dk":V=function(e){if(!/^[0-9]{6}[-]{0,1}[0-9]{4}$/.test(e))return{meta:{},valid:!1};var t=e.replace(/-/g,""),r=parseInt(t.substr(0,2),10),a=parseInt(t.substr(2,2),10),n=parseInt(t.substr(4,2),10);switch(!0){case-1!=="5678".indexOf(t.charAt(6))&&n>=58:n+=1800;break;case-1!=="0123".indexOf(t.charAt(6)):case-1!=="49".indexOf(t.charAt(6))&&n>=37:n+=1900;break;default:n+=2e3}return{meta:{},valid:o(n,a,r)}}(n.value);break;case"ee":case"lt":V=h(n.value);break;case"es":V=function(e){var t=/^[0-9]{8}[-]{0,1}[A-HJ-NP-TV-Z]$/.test(e),r=/^[XYZ][-]{0,1}[0-9]{7}[-]{0,1}[A-HJ-NP-TV-Z]$/.test(e),a=/^[A-HNPQS][-]{0,1}[0-9]{7}[-]{0,1}[0-9A-J]$/.test(e);if(!t&&!r&&!a)return{meta:{},valid:!1};var n,i,s=e.replace(/-/g,"");if(t||r){i="DNI";var o="XYZ".indexOf(s.charAt(0));return-1!==o&&(s=o+s.substr(1)+"",i="NIE"),{meta:{type:i},valid:(n="TRWAGMYFPDXBNJZSQVHLCKE"[(n=parseInt(s.substr(0,8),10))%23])===s.substr(8,1)}}n=s.substr(1,7),i="CIF";for(var l=s[0],d=s.substr(-1),u=0,c=0;c<n.length;c++)if(c%2!=0)u+=parseInt(n[c],10);else{var f=""+2*parseInt(n[c],10);u+=parseInt(f[0],10),2===f.length&&(u+=parseInt(f[1],10))}var p=u-10*Math.floor(u/10);return 0!==p&&(p=10-p),{meta:{type:i},valid:-1!=="KQS".indexOf(l)?d==="JABCDEFGHI"[p]:-1!=="ABEH".indexOf(l)?d===""+p:d===""+p||d==="JABCDEFGHI"[p]}}(n.value);break;case"fi":V=function(e){if(!/^[0-9]{6}[-+A][0-9]{3}[0-9ABCDEFHJKLMNPRSTUVWXY]$/.test(e))return{meta:{},valid:!1};var t=parseInt(e.substr(0,2),10),r=parseInt(e.substr(2,2),10),a=parseInt(e.substr(4,2),10);if(a={"+":1800,"-":1900,A:2e3}[e.charAt(6)]+a,!l(a,r,t))return{meta:{},valid:!1};if(parseInt(e.substr(7,3),10)<2)return{meta:{},valid:!1};var n=parseInt(e.substr(0,6)+e.substr(7,3)+"",10);return{meta:{},valid:"0123456789ABCDEFHJKLMNPRSTUVWXY".charAt(n%31)===e.charAt(10)}}(n.value);break;case"fr":V=function(e){var t=e.toUpperCase();if(!/^(1|2)\d{2}\d{2}(\d{2}|\d[A-Z]|\d{3})\d{2,3}\d{3}\d{2}$/.test(t))return{meta:{},valid:!1};var r=t.substr(5,2);switch(!0){case/^\d{2}$/.test(r):t=e;break;case"2A"===r:t="".concat(e.substr(0,5),"19").concat(e.substr(7));break;case"2B"===r:t="".concat(e.substr(0,5),"18").concat(e.substr(7));break;default:return{meta:{},valid:!1}}var a=97-parseInt(t.substr(0,13),10)%97;return{meta:{},valid:(a<10?"0".concat(a):"".concat(a))===t.substr(13)}}(n.value);break;case"hk":V=function(e){var t=e.toUpperCase();if(!/^[A-MP-Z]{1,2}[0-9]{6}[0-9A]$/.test(t))return{meta:{},valid:!1};var r="ABCDEFGHIJKLMNOPQRSTUVWXYZ",a=t.charAt(0),n=t.charAt(1),i=0,s=t;/^[A-Z]$/.test(n)?(i+=9*(10+r.indexOf(a)),i+=8*(10+r.indexOf(n)),s=t.substr(2)):(i+=324,i+=8*(10+r.indexOf(a)),s=t.substr(1));for(var o=s.length,l=0;l<o-1;l++)i+=(7-l)*parseInt(s.charAt(l),10);var d=i%11;return{meta:{},valid:(0===d?"0":11-d==10?"A":"".concat(11-d))===s.charAt(o-1)}}(n.value);break;case"hr":V=function(e){return{meta:{},valid:/^[0-9]{11}$/.test(e)&&d(e)}}(n.value);break;case"id":V=function(e){if(!/^[2-9]\d{11}$/.test(e))return{meta:{},valid:!1};var t=e.split("").map((function(e){return parseInt(e,10)}));return{meta:{},valid:u(t)}}(n.value);break;case"ie":V=function(e){if(!/^\d{7}[A-W][AHWTX]?$/.test(e))return{meta:{},valid:!1};var t=function(e){for(var t=e;t.length<7;)t="0".concat(t);for(var r="WABCDEFGHIJKLMNOPQRSTUV",a=0,n=0;n<7;n++)a+=parseInt(t.charAt(n),10)*(8-n);return a+=9*r.indexOf(t.substr(7)),r[a%23]};return{meta:{},valid:9!==e.length||"A"!==e.charAt(8)&&"H"!==e.charAt(8)?e.charAt(7)===t(e.substr(0,7)):e.charAt(7)===t(e.substr(0,7)+e.substr(8)+"")}}(n.value);break;case"il":V=function(e){return/^\d{1,9}$/.test(e)?{meta:{},valid:c(e)}:{meta:{},valid:!1}}(n.value);break;case"is":V=function(e){if(!/^[0-9]{6}[-]{0,1}[0-9]{4}$/.test(e))return{meta:{},valid:!1};var t=e.replace(/-/g,""),r=parseInt(t.substr(0,2),10),a=parseInt(t.substr(2,2),10),n=parseInt(t.substr(4,2),10),i=parseInt(t.charAt(9),10);if(!f(n=9===i?1900+n:100*(20+i)+n,a,r,!0))return{meta:{},valid:!1};for(var s=[3,2,7,6,5,4,3,2],o=0,l=0;l<8;l++)o+=parseInt(t.charAt(l),10)*s[l];return{meta:{},valid:"".concat(o=11-o%11)===t.charAt(8)}}(n.value);break;case"kr":V=function(e){var t=e.replace("-","");if(!/^\d{13}$/.test(t))return{meta:{},valid:!1};var r=t.charAt(6),a=parseInt(t.substr(0,2),10),n=parseInt(t.substr(2,2),10),i=parseInt(t.substr(4,2),10);switch(r){case"1":case"2":case"5":case"6":a+=1900;break;case"3":case"4":case"7":case"8":a+=2e3;break;default:a+=1800}if(!p(a,n,i))return{meta:{},valid:!1};for(var s=[2,3,4,5,6,7,8,9,2,3,4,5],o=t.length,l=0,d=0;d<o-1;d++)l+=s[d]*parseInt(t.charAt(d),10);return{meta:{},valid:"".concat((11-l%11)%10)===t.charAt(o-1)}}(n.value);break;case"lv":V=function(e){if(!/^[0-9]{6}[-]{0,1}[0-9]{5}$/.test(e))return{meta:{},valid:!1};var t=e.replace(/\D/g,""),r=parseInt(t.substr(0,2),10),a=parseInt(t.substr(2,2),10),n=parseInt(t.substr(4,2),10);if(n=n+1800+100*parseInt(t.charAt(6),10),!m(n,a,r,!0))return{meta:{},valid:!1};for(var i=0,s=[10,5,8,4,2,1,6,3,7,9],o=0;o<10;o++)i+=parseInt(t.charAt(o),10)*s[o];return{meta:{},valid:"".concat(i=(i+1)%11%10)===t.charAt(10)}}(n.value);break;case"me":V=function(e){return{meta:{},valid:t(e,"ME")}}(n.value);break;case"mk":V=function(e){return{meta:{},valid:t(e,"MK")}}(n.value);break;case"mx":V=function(e){var t=e.toUpperCase();if(!/^[A-Z]{4}\d{6}[A-Z]{6}[0-9A-Z]\d$/.test(t))return{meta:{},valid:!1};var r=t.substr(0,4);if(["BACA","BAKA","BUEI","BUEY","CACA","CACO","CAGA","CAGO","CAKA","CAKO","COGE","COGI","COJA","COJE","COJI","COJO","COLA","CULO","FALO","FETO","GETA","GUEI","GUEY","JETA","JOTO","KACA","KACO","KAGA","KAGO","KAKA","KAKO","KOGE","KOGI","KOJA","KOJE","KOJI","KOJO","KOLA","KULO","LILO","LOCA","LOCO","LOKA","LOKO","MAME","MAMO","MEAR","MEAS","MEON","MIAR","MION","MOCO","MOKO","MULA","MULO","NACA","NACO","PEDA","PEDO","PENE","PIPI","PITO","POPO","PUTA","PUTO","QULO","RATA","ROBA","ROBE","ROBO","RUIN","SENO","TETA","VACA","VAGA","VAGO","VAKA","VUEI","VUEY","WUEI","WUEY"].indexOf(r)>=0)return{meta:{},valid:!1};var a=parseInt(t.substr(4,2),10),n=parseInt(t.substr(6,2),10),i=parseInt(t.substr(6,2),10);if(/^[0-9]$/.test(t.charAt(16))?a+=1900:a+=2e3,!g(a,n,i))return{meta:{},valid:!1};var s=t.charAt(10);if("H"!==s&&"M"!==s)return{meta:{},valid:!1};var o=t.substr(11,2);if(-1===["AS","BC","BS","CC","CH","CL","CM","CS","DF","DG","GR","GT","HG","JC","MC","MN","MS","NE","NL","NT","OC","PL","QR","QT","SL","SP","SR","TC","TL","TS","VZ","YN","ZS"].indexOf(o))return{meta:{},valid:!1};for(var l=0,d=t.length,u=0;u<d-1;u++)l+=(18-u)*"0123456789ABCDEFGHIJKLMN&OPQRSTUVWXYZ".indexOf(t.charAt(u));return{meta:{},valid:"".concat(l=(10-l%10)%10)===t.charAt(d-1)}}(n.value);break;case"my":V=function(e){if(!/^\d{12}$/.test(e))return{meta:{},valid:!1};var t=parseInt(e.substr(0,2),10),r=parseInt(e.substr(2,2),10),a=parseInt(e.substr(4,2),10);if(!b(t+1900,r,a)&&!b(t+2e3,r,a))return{meta:{},valid:!1};var n=e.substr(6,2);return{meta:{},valid:-1===["17","18","19","20","69","70","73","80","81","94","95","96","97"].indexOf(n)}}(n.value);break;case"nl":V=function(e){if(e.length<8)return{meta:{},valid:!1};var t=e;if(8===t.length&&(t="0".concat(t)),!/^[0-9]{4}[.]{0,1}[0-9]{2}[.]{0,1}[0-9]{3}$/.test(t))return{meta:{},valid:!1};if(t=t.replace(/\./g,""),0===parseInt(t,10))return{meta:{},valid:!1};for(var r=0,a=t.length,n=0;n<a-1;n++)r+=(9-n)*parseInt(t.charAt(n),10);return 10==(r%=11)&&(r=0),{meta:{},valid:"".concat(r)===t.charAt(a-1)}}(n.value);break;case"no":V=function(e){return/^\d{11}$/.test(e)?{meta:{},valid:"".concat(function(e){for(var t=[3,7,6,1,8,9,4,5,2],r=0,a=0;a<9;a++)r+=t[a]*parseInt(e.charAt(a),10);return 11-r%11}(e))===e.substr(-2,1)&&"".concat(function(e){for(var t=[5,4,3,2,7,6,5,4,3,2],r=0,a=0;a<10;a++)r+=t[a]*parseInt(e.charAt(a),10);return 11-r%11}(e))===e.substr(-1)}:{meta:{},valid:!1}}(n.value);break;case"pe":V=function(e){if(!/^\d{8}[0-9A-Z]*$/.test(e))return{meta:{},valid:!1};if(8===e.length)return{meta:{},valid:!0};for(var t=[3,2,7,6,5,4,3,2],r=0,a=0;a<8;a++)r+=t[a]*parseInt(e.charAt(a),10);var n=r%11,i=[6,5,4,3,2,1,1,0,9,8,7][n],s="KJIHGFEDCBA".charAt(n);return{meta:{},valid:e.charAt(8)==="".concat(i)||e.charAt(8)===s}}(n.value);break;case"pl":V=function(e){if(!/^[0-9]{11}$/.test(e))return{meta:{},valid:!1};for(var t=0,r=e.length,a=[1,3,7,9,1,3,7,9,1,3,7],n=0;n<r-1;n++)t+=a[n]*parseInt(e.charAt(n),10);return 0==(t%=10)&&(t=10),{meta:{},valid:"".concat(t=10-t)===e.charAt(r-1)}}(n.value);break;case"ro":V=function(e){if(!/^[0-9]{13}$/.test(e))return{meta:{},valid:!1};var t=parseInt(e.charAt(0),10);if(0===t||7===t||8===t)return{meta:{},valid:!1};var r=parseInt(e.substr(1,2),10),a=parseInt(e.substr(3,2),10),n=parseInt(e.substr(5,2),10);if(n>31&&a>12)return{meta:{},valid:!1};if(9!==t&&!A(r={1:1900,2:1900,3:1800,4:1800,5:2e3,6:2e3}[t+""]+r,a,n))return{meta:{},valid:!1};for(var i=0,s=[2,7,9,1,4,6,3,5,8,2,7,9],o=e.length,l=0;l<o-1;l++)i+=parseInt(e.charAt(l),10)*s[l];return 10==(i%=11)&&(i=1),{meta:{},valid:"".concat(i)===e.charAt(o-1)}}(n.value);break;case"rs":V=function(e){return{meta:{},valid:t(e,"RS")}}(n.value);break;case"se":V=function(e){if(!/^[0-9]{10}$/.test(e)&&!/^[0-9]{6}[-|+][0-9]{4}$/.test(e))return{meta:{},valid:!1};var t=e.replace(/[^0-9]/g,""),r=parseInt(t.substr(0,2),10)+1900,a=parseInt(t.substr(2,2),10),n=parseInt(t.substr(4,2),10);return E(r,a,n)?{meta:{},valid:y(t)}:{meta:{},valid:!1}}(n.value);break;case"si":V=function(e){return{meta:{},valid:t(e,"SI")}}(n.value);break;case"sm":V=function(e){return{meta:{},valid:/^\d{5}$/.test(e)}}(n.value);break;case"th":V=function(e){if(13!==e.length)return{meta:{},valid:!1};for(var t=0,r=0;r<12;r++)t+=parseInt(e.charAt(r),10)*(13-r);return{meta:{},valid:(11-t%11)%10===parseInt(e.charAt(12),10)}}(n.value);break;case"tr":V=function(e){if(11!==e.length)return{meta:{},valid:!1};for(var t=0,r=0;r<10;r++)t+=parseInt(e.charAt(r),10);return{meta:{},valid:t%10===parseInt(e.charAt(10),10)}}(n.value);break;case"tw":V=function(e){var t=e.toUpperCase();if(!/^[A-Z][12][0-9]{8}$/.test(t))return{meta:{},valid:!1};for(var r=t.length,a="ABCDEFGHJKLMNPQRSTUVXYWZIO".indexOf(t.charAt(0))+10,n=Math.floor(a/10)+a%10*(r-1),i=0,s=1;s<r-1;s++)i+=parseInt(t.charAt(s),10)*(r-1-s);return{meta:{},valid:(n+i+parseInt(t.charAt(r-1),10))%10==0}}(n.value);break;case"uy":V=function(e){if(!/^\d{8}$/.test(e))return{meta:{},valid:!1};for(var t=[2,9,8,7,6,3,4],r=0,a=0;a<7;a++)r+=parseInt(e.charAt(a),10)*t[a];return(r%=10)>0&&(r=10-r),{meta:{},valid:"".concat(r)===e.charAt(7)}}(n.value);break;case"za":V=function(e){if(!/^[0-9]{10}[0|1][8|9][0-9]$/.test(e))return{meta:{},valid:!1};var t=parseInt(e.substr(0,2),10),r=(new Date).getFullYear()%100,a=parseInt(e.substr(2,2),10),n=parseInt(e.substr(4,2),10);return x(t=t>=r?t+1900:t+2e3,a,n)?{meta:{},valid:I(e)}:{meta:{},valid:!1}}(n.value)}var S=C(n.l10n&&n.l10n.id?i.message||n.l10n.id.country:i.message,n.l10n&&n.l10n.id&&n.l10n.id.countries?n.l10n.id.countries[v.toUpperCase()]:v.toUpperCase());return Object.assign({},{message:S},V)}}},kr}();var wr,Hr=Sr.exports,$r={exports:{}},Nr={};$r.exports=function(){if(wr)return Nr;wr=1;var e=i.algorithms.luhn;return Nr.imei=function(){return{validate:function(t){if(""===t.value)return{valid:!0};switch(!0){case/^\d{15}$/.test(t.value):case/^\d{2}-\d{6}-\d{6}-\d{1}$/.test(t.value):case/^\d{2}\s\d{6}\s\d{6}\s\d{1}$/.test(t.value):return{valid:e(t.value.replace(/[^0-9]/g,""))};case/^\d{14}$/.test(t.value):case/^\d{16}$/.test(t.value):case/^\d{2}-\d{6}-\d{6}(|-\d{2})$/.test(t.value):case/^\d{2}\s\d{6}\s\d{6}(|\s\d{2})$/.test(t.value):return{valid:!0};default:return{valid:!1}}}}},Nr}();var Mr,Tr=$r.exports,Lr={exports:{}},Pr={};Lr.exports=(Mr||(Mr=1,Pr.imo=function(){return{validate:function(e){if(""===e.value)return{valid:!0};if(!/^IMO \d{7}$/i.test(e.value))return{valid:!1};for(var t=e.value.replace(/^.*(\d{7})$/,"$1"),r=0,a=6;a>=1;a--)r+=parseInt(t.slice(6-a,-a),10)*(a+1);return{valid:r%10===parseInt(t.charAt(6),10)}}}}),Pr);var Rr,Dr=Lr.exports,Zr={exports:{}},_r={};Zr.exports=(Rr||(Rr=1,_r.isbn=function(){return{validate:function(e){if(""===e.value)return{meta:{type:null},valid:!0};var t;switch(!0){case/^\d{9}[\dX]$/.test(e.value):case 13===e.value.length&&/^(\d+)-(\d+)-(\d+)-([\dX])$/.test(e.value):case 13===e.value.length&&/^(\d+)\s(\d+)\s(\d+)\s([\dX])$/.test(e.value):t="ISBN10";break;case/^(978|979)\d{9}[\dX]$/.test(e.value):case 17===e.value.length&&/^(978|979)-(\d+)-(\d+)-(\d+)-([\dX])$/.test(e.value):case 17===e.value.length&&/^(978|979)\s(\d+)\s(\d+)\s(\d+)\s([\dX])$/.test(e.value):t="ISBN13";break;default:return{meta:{type:null},valid:!1}}var r,a,n=e.value.replace(/[^0-9X]/gi,"").split(""),i=n.length,s=0;switch(t){case"ISBN10":for(s=0,r=0;r<i-1;r++)s+=parseInt(n[r],10)*(10-r);return 11==(a=11-s%11)?a=0:10===a&&(a="X"),{meta:{type:t},valid:"".concat(a)===n[i-1]};case"ISBN13":for(s=0,r=0;r<i-1;r++)s+=r%2==0?parseInt(n[r],10):3*parseInt(n[r],10);return 10==(a=10-s%10)&&(a="0"),{meta:{type:t},valid:"".concat(a)===n[i-1]}}}}}),_r);var Br,Gr=Zr.exports,Ur={exports:{}},jr={};Ur.exports=(Br||(Br=1,jr.isin=function(){return{validate:function(e){if(""===e.value)return{valid:!0};var t=e.value.toUpperCase();if(!new RegExp("^(AF|AX|AL|DZ|AS|AD|AO|AI|AQ|AG|AR|AM|AW|AU|AT|AZ|BS|BH|BD|BB|BY|BE|BZ|BJ|BM|BT|BO|BQ|BA|BW|BV|BR|IO|BN|BG|BF|BI|KH|CM|CA|CV|KY|CF|TD|CL|CN|CX|CC|CO|KM|CG|CD|CK|CR|CI|HR|CU|CW|CY|CZ|DK|DJ|DM|DO|EC|EG|SV|GQ|ER|EE|ET|FK|FO|FJ|FI|FR|GF|PF|TF|GA|GM|GE|DE|GH|GI|GR|GL|GD|GP|GU|GT|GG|GN|GW|GY|HT|HM|VA|HN|HK|HU|IS|IN|ID|IR|IQ|IE|IM|IL|IT|JM|JP|JE|JO|KZ|KE|KI|KP|KR|KW|KG|LA|LV|LB|LS|LR|LY|LI|LT|LU|MO|MK|MG|MW|MY|MV|ML|MT|MH|MQ|MR|MU|YT|MX|FM|MD|MC|MN|ME|MS|MA|MZ|MM|NA|NR|NP|NL|NC|NZ|NI|NE|NG|NU|NF|MP|NO|OM|PK|PW|PS|PA|PG|PY|PE|PH|PN|PL|PT|PR|QA|RE|RO|RU|RW|BL|SH|KN|LC|MF|PM|VC|WS|SM|ST|SA|SN|RS|SC|SL|SG|SX|SK|SI|SB|SO|ZA|GS|SS|ES|LK|SD|SR|SJ|SZ|SE|CH|SY|TW|TJ|TZ|TH|TL|TG|TK|TO|TT|TN|TR|TM|TC|TV|UG|UA|AE|GB|US|UM|UY|UZ|VU|VE|VN|VG|VI|WF|EH|YE|ZM|ZW)[0-9A-Z]{10}$").test(e.value))return{valid:!1};var r,a=t.length,n="";for(r=0;r<a-1;r++){var i=t.charCodeAt(r);n+=i>57?(i-55).toString():t.charAt(r)}var s="",o=n.length,l=o%2!=0?0:1;for(r=0;r<o;r++)s+=parseInt(n[r],10)*(r%2===l?2:1)+"";var d=0;for(r=0;r<s.length;r++)d+=parseInt(s.charAt(r),10);return{valid:"".concat(d=(10-d%10)%10)===t.charAt(a-1)}}}}),jr);var Kr,zr=Ur.exports,Yr={exports:{}},Jr={};Yr.exports=(Kr||(Kr=1,Jr.ismn=function(){return{validate:function(e){if(""===e.value)return{meta:null,valid:!0};var t;switch(!0){case/^M\d{9}$/.test(e.value):case/^M-\d{4}-\d{4}-\d{1}$/.test(e.value):case/^M\s\d{4}\s\d{4}\s\d{1}$/.test(e.value):t="ISMN10";break;case/^9790\d{9}$/.test(e.value):case/^979-0-\d{4}-\d{4}-\d{1}$/.test(e.value):case/^979\s0\s\d{4}\s\d{4}\s\d{1}$/.test(e.value):t="ISMN13";break;default:return{meta:null,valid:!1}}var r=e.value;"ISMN10"===t&&(r="9790".concat(r.substr(1)));for(var a=0,n=(r=r.replace(/[^0-9]/gi,"")).length,i=[1,3],s=0;s<n-1;s++)a+=parseInt(r.charAt(s),10)*i[s%2];return{meta:{type:t},valid:"".concat(a=(10-a%10)%10)===r.charAt(n-1)}}}}),Jr);var Xr,Wr=Yr.exports,qr={exports:{}},Qr={};qr.exports=(Xr||(Xr=1,Qr.issn=function(){return{validate:function(e){if(""===e.value)return{valid:!0};if(!/^\d{4}-\d{3}[\dX]$/.test(e.value))return{valid:!1};var t=e.value.replace(/[^0-9X]/gi,"").split(""),r=t.length,a=0;"X"===t[7]&&(t[7]="10");for(var n=0;n<r;n++)a+=parseInt(t[n],10)*(8-n);return{valid:a%11==0}}}}),Qr);var ea,ta=qr.exports,ra={exports:{}},aa={};ra.exports=(ea||(ea=1,aa.mac=function(){return{validate:function(e){return{valid:""===e.value||/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/.test(e.value)||/^([0-9A-Fa-f]{4}\.){2}([0-9A-Fa-f]{4})$/.test(e.value)}}}}),aa);var na,ia=ra.exports,sa={exports:{}},oa={};sa.exports=function(){if(na)return oa;na=1;var e=i.algorithms.luhn;return oa.meid=function(){return{validate:function(t){if(""===t.value)return{valid:!0};var r=t.value;if(/^[0-9A-F]{15}$/i.test(r)||/^[0-9A-F]{2}[- ][0-9A-F]{6}[- ][0-9A-F]{6}[- ][0-9A-F]$/i.test(r)||/^\d{19}$/.test(r)||/^\d{5}[- ]\d{5}[- ]\d{4}[- ]\d{4}[- ]\d$/.test(r)){var a=r.charAt(r.length-1).toUpperCase();if((r=r.replace(/[- ]/g,"")).match(/^\d*$/i))return{valid:e(r)};r=r.slice(0,-1);var n="",i=void 0;for(i=1;i<=13;i+=2)n+=(2*parseInt(r.charAt(i),16)).toString(16);var s=0;for(i=0;i<n.length;i++)s+=parseInt(n.charAt(i),16);return{valid:s%10==0?"0"===a:a===(2*(10*Math.floor((s+10)/10)-s)).toString(16).toUpperCase()}}return/^[0-9A-F]{14}$/i.test(r)||/^[0-9A-F]{2}[- ][0-9A-F]{6}[- ][0-9A-F]{6}$/i.test(r)||/^\d{18}$/.test(r)||/^\d{5}[- ]\d{5}[- ]\d{4}[- ]\d{4}$/.test(r)?{valid:!0}:{valid:!1}}}},oa}();var la,da=sa.exports,ua={exports:{}},ca={};ua.exports=function(){if(la)return ca;la=1;var e=i,t=e.utils.format,r=e.utils.removeUndefined;return ca.phone=function(){var e=["AE","BG","BR","CN","CZ","DE","DK","ES","FR","GB","IN","MA","NL","PK","RO","RU","SK","TH","US","VE"];return{validate:function(a){if(""===a.value)return{valid:!0};var n=Object.assign({},{message:""},r(a.options)),i=a.value.trim(),s=i.substr(0,2);if(!(s="function"==typeof n.country?n.country.call(this):n.country)||-1===e.indexOf(s.toUpperCase()))return{valid:!0};var o=!0;switch(s.toUpperCase()){case"AE":o=/^(((\+|00)?971[\s.-]?(\(0\)[\s.-]?)?|0)(\(5(0|2|5|6)\)|5(0|2|5|6)|2|3|4|6|7|9)|60)([\s.-]?[0-9]){7}$/.test(i);break;case"BG":o=/^(0|359|00)(((700|900)[0-9]{5}|((800)[0-9]{5}|(800)[0-9]{4}))|(87|88|89)([0-9]{7})|((2[0-9]{7})|(([3-9][0-9])(([0-9]{6})|([0-9]{5})))))$/.test(i.replace(/\+|\s|-|\/|\(|\)/gi,""));break;case"BR":o=/^(([\d]{4}[-.\s]{1}[\d]{2,3}[-.\s]{1}[\d]{2}[-.\s]{1}[\d]{2})|([\d]{4}[-.\s]{1}[\d]{3}[-.\s]{1}[\d]{4})|((\(?\+?[0-9]{2}\)?\s?)?(\(?\d{2}\)?\s?)?\d{4,5}[-.\s]?\d{4}))$/.test(i);break;case"CN":o=/^((00|\+)?(86(?:-| )))?((\d{11})|(\d{3}[- ]{1}\d{4}[- ]{1}\d{4})|((\d{2,4}[- ]){1}(\d{7,8}|(\d{3,4}[- ]{1}\d{4}))([- ]{1}\d{1,4})?))$/.test(i);break;case"CZ":o=/^(((00)([- ]?)|\+)(420)([- ]?))?((\d{3})([- ]?)){2}(\d{3})$/.test(i);break;case"DE":o=/^(((((((00|\+)49[ \-/]?)|0)[1-9][0-9]{1,4})[ \-/]?)|((((00|\+)49\()|\(0)[1-9][0-9]{1,4}\)[ \-/]?))[0-9]{1,7}([ \-/]?[0-9]{1,5})?)$/.test(i);break;case"DK":o=/^(\+45|0045|\(45\))?\s?[2-9](\s?\d){7}$/.test(i);break;case"ES":o=/^(?:(?:(?:\+|00)34\D?))?(?:5|6|7|8|9)(?:\d\D?){8}$/.test(i);break;case"FR":o=/^(?:(?:(?:\+|00)33[ ]?(?:\(0\)[ ]?)?)|0){1}[1-9]{1}([ .-]?)(?:\d{2}\1?){3}\d{2}$/.test(i);break;case"GB":o=/^\(?(?:(?:0(?:0|11)\)?[\s-]?\(?|\+)44\)?[\s-]?\(?(?:0\)?[\s-]?\(?)?|0)(?:\d{2}\)?[\s-]?\d{4}[\s-]?\d{4}|\d{3}\)?[\s-]?\d{3}[\s-]?\d{3,4}|\d{4}\)?[\s-]?(?:\d{5}|\d{3}[\s-]?\d{3})|\d{5}\)?[\s-]?\d{4,5}|8(?:00[\s-]?11[\s-]?11|45[\s-]?46[\s-]?4\d))(?:(?:[\s-]?(?:x|ext\.?\s?|#)\d+)?)$/.test(i);break;case"IN":o=/((\+?)((0[ -]+)*|(91 )*)(\d{12}|\d{10}))|\d{5}([- ]*)\d{6}/.test(i);break;case"MA":o=/^(?:(?:(?:\+|00)212[\s]?(?:[\s]?\(0\)[\s]?)?)|0){1}(?:5[\s.-]?[2-3]|6[\s.-]?[13-9]){1}[0-9]{1}(?:[\s.-]?\d{2}){3}$/.test(i);break;case"NL":o=/^((\+|00(\s|\s?-\s?)?)31(\s|\s?-\s?)?(\(0\)[-\s]?)?|0)[1-9]((\s|\s?-\s?)?[0-9])((\s|\s?-\s?)?[0-9])((\s|\s?-\s?)?[0-9])\s?[0-9]\s?[0-9]\s?[0-9]\s?[0-9]\s?[0-9]$/gm.test(i);break;case"PK":o=/^0?3[0-9]{2}[0-9]{7}$/.test(i);break;case"RO":o=/^(\+4|)?(07[0-8]{1}[0-9]{1}|02[0-9]{2}|03[0-9]{2}){1}?(\s|\.|-)?([0-9]{3}(\s|\.|-|)){2}$/g.test(i);break;case"RU":o=/^((8|\+7|007)[-./ ]?)?([(/.]?\d{3}[)/.]?[-./ ]?)?[\d\-./ ]{7,10}$/g.test(i);break;case"SK":o=/^(((00)([- ]?)|\+)(421)([- ]?))?((\d{3})([- ]?)){2}(\d{3})$/.test(i);break;case"TH":o=/^0\(?([6|8-9]{2})*-([0-9]{3})*-([0-9]{4})$/.test(i);break;case"VE":o=/^0(?:2(?:12|4[0-9]|5[1-9]|6[0-9]|7[0-8]|8[1-35-8]|9[1-5]|3[45789])|4(?:1[246]|2[46]))\d{7}$/.test(i);break;default:o=/^(?:(1-?)|(\+1 ?))?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}$/.test(i)}return{message:t(a.l10n&&a.l10n.phone?n.message||a.l10n.phone.country:n.message,a.l10n&&a.l10n.phone&&a.l10n.phone.countries?a.l10n.phone.countries[s]:s),valid:o}}}},ca}();var fa,pa=ua.exports,va={exports:{}},ha={};va.exports=(fa||(fa=1,ha.rtn=function(){return{validate:function(e){if(""===e.value)return{valid:!0};if(!/^\d{9}$/.test(e.value))return{valid:!1};for(var t=0,r=0;r<e.value.length;r+=3)t+=3*parseInt(e.value.charAt(r),10)+7*parseInt(e.value.charAt(r+1),10)+parseInt(e.value.charAt(r+2),10);return{valid:0!==t&&t%10==0}}}}),ha);var ma,ga=va.exports,ba={exports:{}},Aa={};ba.exports=(ma||(ma=1,Aa.sedol=function(){return{validate:function(e){if(""===e.value)return{valid:!0};var t=e.value.toUpperCase();if(!/^[0-9A-Z]{7}$/.test(t))return{valid:!1};for(var r=[1,3,1,7,3,9,1],a=t.length,n=0,i=0;i<a-1;i++)n+=r[i]*parseInt(t.charAt(i),36);return{valid:"".concat(n=(10-n%10)%10)===t.charAt(a-1)}}}}),Aa);var ya,Ea=ba.exports,Ia={exports:{}},xa={};Ia.exports=function(){if(ya)return xa;ya=1;var e=i.algorithms.luhn;return xa.siren=function(){return{validate:function(t){return{valid:""===t.value||/^\d{9}$/.test(t.value)&&e(t.value)}}}},xa}();var Ca,Oa=Ia.exports,Fa={exports:{}},Va={};Fa.exports=(Ca||(Ca=1,Va.siret=function(){return{validate:function(e){if(""===e.value)return{valid:!0};for(var t,r=e.value.length,a=0,n=0;n<r;n++)t=parseInt(e.value.charAt(n),10),n%2==0&&(t*=2)>9&&(t-=9),a+=t;return{valid:a%10==0}}}}),Va);var Sa,ka=Fa.exports,wa={exports:{}},Ha={};wa.exports=function(){if(Sa)return Ha;Sa=1;var e=i.utils.format;return Ha.step=function(){return{validate:function(t){if(""===t.value)return{valid:!0};var r=parseFloat(t.value);if(isNaN(r)||!isFinite(r))return{valid:!1};var a=Object.assign({},{baseValue:0,message:"",step:1},t.options),n=function(e,t){if(0===t)return 1;var r="".concat(e).split("."),a="".concat(t).split("."),n=(1===r.length?0:r[1].length)+(1===a.length?0:a[1].length);return function(e,t){var r,a=Math.pow(10,t),n=e*a;switch(!0){case 0===n:r=0;break;case n>0:r=1;break;case n<0:r=-1}return n%1==.5*r?(Math.floor(n)+(r>0?1:0))/a:Math.round(n)/a}(e-t*Math.floor(e/t),n)}(r-a.baseValue,a.step);return{message:e(t.l10n?a.message||t.l10n.step.default:a.message,"".concat(a.step)),valid:0===n||n===a.step}}}},Ha}();var $a,Na=wa.exports,Ma={exports:{}},Ta={};Ma.exports=function(){if($a)return Ta;$a=1;var e=i,t=e.utils.format,r=e.utils.removeUndefined;return Ta.uuid=function(){return{validate:function(e){if(""===e.value)return{valid:!0};var a=Object.assign({},{message:""},r(e.options)),n={3:/^[0-9A-F]{8}-[0-9A-F]{4}-3[0-9A-F]{3}-[0-9A-F]{4}-[0-9A-F]{12}$/i,4:/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,5:/^[0-9A-F]{8}-[0-9A-F]{4}-5[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,all:/^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/i},i=a.version?"".concat(a.version):"all";return{message:a.version?t(e.l10n?a.message||e.l10n.uuid.version:a.message,a.version):e.l10n?e.l10n.uuid.default:a.message,valid:null===n[i]||n[i].test(e.value)}}}},Ta}();var La,Pa=Ma.exports,Ra={exports:{}},Da={};Ra.exports=function(){if(La)return Da;La=1;var e=i,t=e.utils.isValidDate,r=e.utils.isValidDate,a=e.algorithms.mod11And10,n=e.algorithms.luhn,s=e.algorithms.mod11And10,o=e.algorithms.luhn,l=e.utils.isValidDate,d=e.algorithms.mod97And10;function u(e){if(e.length<8)return{meta:{},valid:!1};var t=e;if(8===t.length&&(t="0".concat(t)),!/^[0-9]{4}[.]{0,1}[0-9]{2}[.]{0,1}[0-9]{3}$/.test(t))return{meta:{},valid:!1};if(t=t.replace(/\./g,""),0===parseInt(t,10))return{meta:{},valid:!1};for(var r=0,a=t.length,n=0;n<a-1;n++)r+=(9-n)*parseInt(t.charAt(n),10);return 10==(r%=11)&&(r=0),{meta:{},valid:"".concat(r)===t.charAt(a-1)}}var c=e.algorithms.luhn,f=e.utils.format,p=e.utils.removeUndefined;return Da.vat=function(){var e=["AR","AT","BE","BG","BR","CH","CY","CZ","DE","DK","EE","EL","ES","FI","FR","GB","GR","HR","HU","IE","IS","IT","LT","LU","LV","MT","NL","NO","PL","PT","RO","RU","RS","SE","SK","SI","VE","ZA"];return{validate:function(i){var v=i.value;if(""===v)return{valid:!0};var h=Object.assign({},{message:""},p(i.options)),m=v.substr(0,2);if(m="function"==typeof h.country?h.country.call(this):h.country,-1===e.indexOf(m))return{valid:!0};var g={meta:{},valid:!0};switch(m.toLowerCase()){case"ar":g=function(e){var t=e.replace("-","");if(/^AR[0-9]{11}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{11}$/.test(t))return{meta:{},valid:!1};for(var r=[5,4,3,2,7,6,5,4,3,2],a=0,n=0;n<10;n++)a+=parseInt(t.charAt(n),10)*r[n];return 11==(a=11-a%11)&&(a=0),{meta:{},valid:"".concat(a)===t.substr(10)}}(v);break;case"at":g=function(e){var t=e;if(/^ATU[0-9]{8}$/.test(t)&&(t=t.substr(2)),!/^U[0-9]{8}$/.test(t))return{meta:{},valid:!1};t=t.substr(1);for(var r=[1,2,1,2,1,2,1],a=0,n=0,i=0;i<7;i++)(n=parseInt(t.charAt(i),10)*r[i])>9&&(n=Math.floor(n/10)+n%10),a+=n;return 10==(a=10-(a+4)%10)&&(a=0),{meta:{},valid:"".concat(a)===t.substr(7,1)}}(v);break;case"be":g=function(e){var t=e;return/^BE[0]?[0-9]{9}$/.test(t)&&(t=t.substr(2)),/^[0]?[0-9]{9}$/.test(t)?(9===t.length&&(t="0".concat(t)),"0"===t.substr(1,1)?{meta:{},valid:!1}:{meta:{},valid:(parseInt(t.substr(0,8),10)+parseInt(t.substr(8,2),10))%97==0}):{meta:{},valid:!1}}(v);break;case"bg":g=function(e){var r=e;if(/^BG[0-9]{9,10}$/.test(r)&&(r=r.substr(2)),!/^[0-9]{9,10}$/.test(r))return{meta:{},valid:!1};var a=0,n=0;if(9===r.length){for(n=0;n<8;n++)a+=parseInt(r.charAt(n),10)*(n+1);if(10==(a%=11)){for(a=0,n=0;n<8;n++)a+=parseInt(r.charAt(n),10)*(n+3);a%=11}return{meta:{},valid:"".concat(a%=10)===r.substr(8)}}return{meta:{},valid:function(e){var r=parseInt(e.substr(0,2),10)+1900,a=parseInt(e.substr(2,2),10),n=parseInt(e.substr(4,2),10);if(a>40?(r+=100,a-=40):a>20&&(r-=100,a-=20),!t(r,a,n))return!1;for(var i=[2,4,8,5,10,9,7,3,6],s=0,o=0;o<9;o++)s+=parseInt(e.charAt(o),10)*i[o];return"".concat(s=s%11%10)===e.substr(9,1)}(r)||function(e){for(var t=[21,19,17,13,11,9,7,3,1],r=0,a=0;a<9;a++)r+=parseInt(e.charAt(a),10)*t[a];return"".concat(r%=10)===e.substr(9,1)}(r)||function(e){for(var t=[4,3,2,7,6,5,4,3,2],r=0,a=0;a<9;a++)r+=parseInt(e.charAt(a),10)*t[a];return 10!=(r=11-r%11)&&(11===r&&(r=0),"".concat(r)===e.substr(9,1))}(r)}}(v);break;case"br":g=function(e){if(""===e)return{meta:{},valid:!0};var t=e.replace(/[^\d]+/g,"");if(""===t||14!==t.length)return{meta:{},valid:!1};if("00000000000000"===t||"11111111111111"===t||"22222222222222"===t||"33333333333333"===t||"44444444444444"===t||"55555555555555"===t||"66666666666666"===t||"77777777777777"===t||"88888888888888"===t||"99999999999999"===t)return{meta:{},valid:!1};var r,a=t.length-2,n=t.substring(0,a),i=t.substring(a),s=0,o=a-7;for(r=a;r>=1;r--)s+=parseInt(n.charAt(a-r),10)*o--,o<2&&(o=9);var l=s%11<2?0:11-s%11;if(l!==parseInt(i.charAt(0),10))return{meta:{},valid:!1};for(a+=1,n=t.substring(0,a),s=0,o=a-7,r=a;r>=1;r--)s+=parseInt(n.charAt(a-r),10)*o--,o<2&&(o=9);return{meta:{},valid:(l=s%11<2?0:11-s%11)===parseInt(i.charAt(1),10)}}(v);break;case"ch":g=function(e){var t=e;if(/^CHE[0-9]{9}(MWST|TVA|IVA|TPV)?$/.test(t)&&(t=t.substr(2)),!/^E[0-9]{9}(MWST|TVA|IVA|TPV)?$/.test(t))return{meta:{},valid:!1};t=t.substr(1);for(var r=[5,4,3,2,7,6,5,4],a=0,n=0;n<8;n++)a+=parseInt(t.charAt(n),10)*r[n];return 10==(a=11-a%11)?{meta:{},valid:!1}:(11===a&&(a=0),{meta:{},valid:"".concat(a)===t.substr(8,1)})}(v);break;case"cy":g=function(e){var t=e;if(/^CY[0-5|9][0-9]{7}[A-Z]$/.test(t)&&(t=t.substr(2)),!/^[0-5|9][0-9]{7}[A-Z]$/.test(t))return{meta:{},valid:!1};if("12"===t.substr(0,2))return{meta:{},valid:!1};for(var r=0,a={0:1,1:0,2:5,3:7,4:9,5:13,6:15,7:17,8:19,9:21},n=0;n<8;n++){var i=parseInt(t.charAt(n),10);n%2==0&&(i=a["".concat(i)]),r+=i}return{meta:{},valid:"".concat("ABCDEFGHIJKLMNOPQRSTUVWXYZ"[r%26])===t.substr(8,1)}}(v);break;case"cz":g=function(e){var t=e;if(/^CZ[0-9]{8,10}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{8,10}$/.test(t))return{meta:{},valid:!1};var a=0,n=0;if(8===t.length){if("9"==="".concat(t.charAt(0)))return{meta:{},valid:!1};for(a=0,n=0;n<7;n++)a+=parseInt(t.charAt(n),10)*(8-n);return 10==(a=11-a%11)&&(a=0),11===a&&(a=1),{meta:{},valid:"".concat(a)===t.substr(7,1)}}if(9===t.length&&"6"==="".concat(t.charAt(0))){for(a=0,n=0;n<7;n++)a+=parseInt(t.charAt(n+1),10)*(8-n);return 10==(a=11-a%11)&&(a=0),11===a&&(a=1),{meta:{},valid:"".concat(a=[8,7,6,5,4,3,2,1,0,9,10][a-1])===t.substr(8,1)}}if(9===t.length||10===t.length){var i=1900+parseInt(t.substr(0,2),10),s=parseInt(t.substr(2,2),10)%50%20,o=parseInt(t.substr(4,2),10);if(9===t.length){if(i>=1980&&(i-=100),i>1953)return{meta:{},valid:!1}}else i<1954&&(i+=100);if(!r(i,s,o))return{meta:{},valid:!1};if(10===t.length){var l=parseInt(t.substr(0,9),10)%11;return i<1985&&(l%=10),{meta:{},valid:"".concat(l)===t.substr(9,1)}}return{meta:{},valid:!0}}return{meta:{},valid:!1}}(v);break;case"de":g=function(e){var t=e;return/^DE[0-9]{9}$/.test(t)&&(t=t.substr(2)),/^[1-9][0-9]{8}$/.test(t)?{meta:{},valid:a(t)}:{meta:{},valid:!1}}(v);break;case"dk":g=function(e){var t=e;if(/^DK[0-9]{8}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{8}$/.test(t))return{meta:{},valid:!1};for(var r=0,a=[2,7,6,5,4,3,2,1],n=0;n<8;n++)r+=parseInt(t.charAt(n),10)*a[n];return{meta:{},valid:r%11==0}}(v);break;case"ee":g=function(e){var t=e;if(/^EE[0-9]{9}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{9}$/.test(t))return{meta:{},valid:!1};for(var r=0,a=[3,7,1,3,7,1,3,7,1],n=0;n<9;n++)r+=parseInt(t.charAt(n),10)*a[n];return{meta:{},valid:r%10==0}}(v);break;case"el":case"gr":g=function(e){var t=e;if(/^(GR|EL)[0-9]{9}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{9}$/.test(t))return{meta:{},valid:!1};8===t.length&&(t="0".concat(t));for(var r=[256,128,64,32,16,8,4,2],a=0,n=0;n<8;n++)a+=parseInt(t.charAt(n),10)*r[n];return{meta:{},valid:"".concat(a=a%11%10)===t.substr(8,1)}}(v);break;case"es":g=function(e){var t=e;if(/^ES[0-9A-Z][0-9]{7}[0-9A-Z]$/.test(t)&&(t=t.substr(2)),!/^[0-9A-Z][0-9]{7}[0-9A-Z]$/.test(t))return{meta:{},valid:!1};var r,a,n=t.charAt(0);return/^[0-9]$/.test(n)?{meta:{type:"DNI"},valid:(r=t,a=parseInt(r.substr(0,8),10),"".concat("TRWAGMYFPDXBNJZSQVHLCKE"[a%23])===r.substr(8,1))}:/^[XYZ]$/.test(n)?{meta:{type:"NIE"},valid:function(e){var t=["XYZ".indexOf(e.charAt(0)),e.substr(1)].join(""),r="TRWAGMYFPDXBNJZSQVHLCKE"[parseInt(t,10)%23];return"".concat(r)===e.substr(8,1)}(t)}:{meta:{type:"CIF"},valid:function(e){var t,r=e.charAt(0);if(-1!=="KLM".indexOf(r))return t=parseInt(e.substr(1,8),10),"".concat(t="TRWAGMYFPDXBNJZSQVHLCKE"[t%23])===e.substr(8,1);if(-1!=="ABCDEFGHJNPQRSUVW".indexOf(r)){for(var a=[2,1,2,1,2,1,2],n=0,i=0,s=0;s<7;s++)(i=parseInt(e.charAt(s+1),10)*a[s])>9&&(i=Math.floor(i/10)+i%10),n+=i;return 10==(n=10-n%10)&&(n=0),"".concat(n)===e.substr(8,1)||"JABCDEFGHI"[n]===e.substr(8,1)}return!1}(t)}}(v);break;case"fi":g=function(e){var t=e;if(/^FI[0-9]{8}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{8}$/.test(t))return{meta:{},valid:!1};for(var r=[7,9,10,5,8,4,2,1],a=0,n=0;n<8;n++)a+=parseInt(t.charAt(n),10)*r[n];return{meta:{},valid:a%11==0}}(v);break;case"fr":g=function(e){var t=e;if(/^FR[0-9A-Z]{2}[0-9]{9}$/.test(t)&&(t=t.substr(2)),!/^[0-9A-Z]{2}[0-9]{9}$/.test(t))return{meta:{},valid:!1};if("000"!==t.substr(2,4))return{meta:{},valid:n(t.substr(2))};if(/^[0-9]{2}$/.test(t.substr(0,2)))return{meta:{},valid:t.substr(0,2)==="".concat(parseInt(t.substr(2)+"12",10)%97)};var r,a="0123456789ABCDEFGHJKLMNPQRSTUVWXYZ";return r=/^[0-9]$/.test(t.charAt(0))?24*a.indexOf(t.charAt(0))+a.indexOf(t.charAt(1))-10:34*a.indexOf(t.charAt(0))+a.indexOf(t.charAt(1))-100,{meta:{},valid:(parseInt(t.substr(2),10)+1+Math.floor(r/11))%11==r%11}}(v);break;case"gb":g=function(e){var t=e;if((/^GB[0-9]{9}$/.test(t)||/^GB[0-9]{12}$/.test(t)||/^GBGD[0-9]{3}$/.test(t)||/^GBHA[0-9]{3}$/.test(t)||/^GB(GD|HA)8888[0-9]{5}$/.test(t))&&(t=t.substr(2)),!(/^[0-9]{9}$/.test(t)||/^[0-9]{12}$/.test(t)||/^GD[0-9]{3}$/.test(t)||/^HA[0-9]{3}$/.test(t)||/^(GD|HA)8888[0-9]{5}$/.test(t)))return{meta:{},valid:!1};var r=t.length;if(5===r){var a=t.substr(0,2),n=parseInt(t.substr(2),10);return{meta:{},valid:"GD"===a&&n<500||"HA"===a&&n>=500}}if(11===r&&("GD8888"===t.substr(0,6)||"HA8888"===t.substr(0,6)))return"GD"===t.substr(0,2)&&parseInt(t.substr(6,3),10)>=500||"HA"===t.substr(0,2)&&parseInt(t.substr(6,3),10)<500?{meta:{},valid:!1}:{meta:{},valid:parseInt(t.substr(6,3),10)%97===parseInt(t.substr(9,2),10)};if(9===r||12===r){for(var i=[8,7,6,5,4,3,2,10,1],s=0,o=0;o<9;o++)s+=parseInt(t.charAt(o),10)*i[o];return s%=97,{meta:{},valid:parseInt(t.substr(0,3),10)>=100?0===s||42===s||55===s:0===s}}return{meta:{},valid:!0}}(v);break;case"hr":g=function(e){var t=e;return/^HR[0-9]{11}$/.test(t)&&(t=t.substr(2)),/^[0-9]{11}$/.test(t)?{meta:{},valid:s(t)}:{meta:{},valid:!1}}(v);break;case"hu":g=function(e){var t=e;if(/^HU[0-9]{8}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{8}$/.test(t))return{meta:{},valid:!1};for(var r=[9,7,3,1,9,7,3,1],a=0,n=0;n<8;n++)a+=parseInt(t.charAt(n),10)*r[n];return{meta:{},valid:a%10==0}}(v);break;case"ie":g=function(e){var t=e;if(/^IE[0-9][0-9A-Z*+][0-9]{5}[A-Z]{1,2}$/.test(t)&&(t=t.substr(2)),!/^[0-9][0-9A-Z*+][0-9]{5}[A-Z]{1,2}$/.test(t))return{meta:{},valid:!1};var r=function(e){for(var t=e;t.length<7;)t="0".concat(t);for(var r="WABCDEFGHIJKLMNOPQRSTUV",a=0,n=0;n<7;n++)a+=parseInt(t.charAt(n),10)*(8-n);return a+=9*r.indexOf(t.substr(7)),r[a%23]};return/^[0-9]+$/.test(t.substr(0,7))?{meta:{},valid:t.charAt(7)===r("".concat(t.substr(0,7)).concat(t.substr(8)))}:-1!=="ABCDEFGHIJKLMNOPQRSTUVWXYZ+*".indexOf(t.charAt(1))?{meta:{},valid:t.charAt(7)===r("".concat(t.substr(2,5)).concat(t.substr(0,1)))}:{meta:{},valid:!0}}(v);break;case"is":g=function(e){var t=e;return/^IS[0-9]{5,6}$/.test(t)&&(t=t.substr(2)),{meta:{},valid:/^[0-9]{5,6}$/.test(t)}}(v);break;case"it":g=function(e){var t=e;if(/^IT[0-9]{11}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{11}$/.test(t))return{meta:{},valid:!1};if(0===parseInt(t.substr(0,7),10))return{meta:{},valid:!1};var r=parseInt(t.substr(7,3),10);return r<1||r>201&&999!==r&&888!==r?{meta:{},valid:!1}:{meta:{},valid:o(t)}}(v);break;case"lt":g=function(e){var t=e;if(/^LT([0-9]{7}1[0-9]|[0-9]{10}1[0-9])$/.test(t)&&(t=t.substr(2)),!/^([0-9]{7}1[0-9]|[0-9]{10}1[0-9])$/.test(t))return{meta:{},valid:!1};var r,a=t.length,n=0;for(r=0;r<a-1;r++)n+=parseInt(t.charAt(r),10)*(1+r%9);var i=n%11;if(10===i)for(n=0,r=0;r<a-1;r++)n+=parseInt(t.charAt(r),10)*(1+(r+2)%9);return{meta:{},valid:"".concat(i=i%11%10)===t.charAt(a-1)}}(v);break;case"lu":g=function(e){var t=e;return/^LU[0-9]{8}$/.test(t)&&(t=t.substring(2)),/^[0-9]{8}$/.test(t)?{meta:{},valid:parseInt(t.substring(0,6),10)%89===parseInt(t.substring(6,8),10)}:{meta:{},valid:!1}}(v);break;case"lv":g=function(e){var t=e;if(/^LV[0-9]{11}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{11}$/.test(t))return{meta:{},valid:!1};var r,a=parseInt(t.charAt(0),10),n=t.length,i=0,s=[];if(a>3){for(i=0,s=[9,1,4,8,3,10,2,5,7,6,1],r=0;r<n;r++)i+=parseInt(t.charAt(r),10)*s[r];return{meta:{},valid:3==(i%=11)}}var o=parseInt(t.substr(0,2),10),d=parseInt(t.substr(2,2),10),u=parseInt(t.substr(4,2),10);if(u=u+1800+100*parseInt(t.charAt(6),10),!l(u,d,o))return{meta:{},valid:!1};for(i=0,s=[10,5,8,4,2,1,6,3,7,9],r=0;r<n-1;r++)i+=parseInt(t.charAt(r),10)*s[r];return{meta:{},valid:"".concat(i=(i+1)%11%10)===t.charAt(n-1)}}(v);break;case"mt":g=function(e){var t=e;if(/^MT[0-9]{8}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{8}$/.test(t))return{meta:{},valid:!1};for(var r=[3,4,6,7,8,9,10,1],a=0,n=0;n<8;n++)a+=parseInt(t.charAt(n),10)*r[n];return{meta:{},valid:a%37==0}}(v);break;case"nl":g=function(e){var t=e;return/^NL[0-9]{9}B[0-9]{2}$/.test(t)&&(t=t.substr(2)),/^[0-9]{9}B[0-9]{2}$/.test(t)?{meta:{},valid:u(t.substr(0,9)).valid||d("NL".concat(t))}:{meta:{},valid:!1}}(v);break;case"no":g=function(e){var t=e;if(/^NO[0-9]{9}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{9}$/.test(t))return{meta:{},valid:!1};for(var r=[3,2,7,6,5,4,3,2],a=0,n=0;n<8;n++)a+=parseInt(t.charAt(n),10)*r[n];return 11==(a=11-a%11)&&(a=0),{meta:{},valid:"".concat(a)===t.substr(8,1)}}(v);break;case"pl":g=function(e){var t=e;if(/^PL[0-9]{10}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{10}$/.test(t))return{meta:{},valid:!1};for(var r=[6,5,7,2,3,4,5,6,7,-1],a=0,n=0;n<10;n++)a+=parseInt(t.charAt(n),10)*r[n];return{meta:{},valid:a%11==0}}(v);break;case"pt":g=function(e){var t=e;if(/^PT[0-9]{9}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{9}$/.test(t))return{meta:{},valid:!1};for(var r=[9,8,7,6,5,4,3,2],a=0,n=0;n<8;n++)a+=parseInt(t.charAt(n),10)*r[n];return(a=11-a%11)>9&&(a=0),{meta:{},valid:"".concat(a)===t.substr(8,1)}}(v);break;case"ro":g=function(e){var t=e;if(/^RO[1-9][0-9]{1,9}$/.test(t)&&(t=t.substr(2)),!/^[1-9][0-9]{1,9}$/.test(t))return{meta:{},valid:!1};for(var r=t.length,a=[7,5,3,2,1,7,5,3,2].slice(10-r),n=0,i=0;i<r-1;i++)n+=parseInt(t.charAt(i),10)*a[i];return{meta:{},valid:"".concat(n=10*n%11%10)===t.substr(r-1,1)}}(v);break;case"rs":g=function(e){var t=e;if(/^RS[0-9]{9}$/.test(t)&&(t=t.substr(2)),!/^[0-9]{9}$/.test(t))return{meta:{},valid:!1};for(var r=10,a=0,n=0;n<8;n++)0==(a=(parseInt(t.charAt(n),10)+r)%10)&&(a=10),r=2*a%11;return{meta:{},valid:(r+parseInt(t.substr(8,1),10))%10==1}}(v);break;case"ru":g=function(e){var t=e;if(/^RU([0-9]{10}|[0-9]{12})$/.test(t)&&(t=t.substr(2)),!/^([0-9]{10}|[0-9]{12})$/.test(t))return{meta:{},valid:!1};var r=0;if(10===t.length){var a=[2,4,10,3,5,9,4,6,8,0],n=0;for(r=0;r<10;r++)n+=parseInt(t.charAt(r),10)*a[r];return(n%=11)>9&&(n%=10),{meta:{},valid:"".concat(n)===t.substr(9,1)}}if(12===t.length){var i=[7,2,4,10,3,5,9,4,6,8,0],s=[3,7,2,4,10,3,5,9,4,6,8,0],o=0,l=0;for(r=0;r<11;r++)o+=parseInt(t.charAt(r),10)*i[r],l+=parseInt(t.charAt(r),10)*s[r];return(o%=11)>9&&(o%=10),(l%=11)>9&&(l%=10),{meta:{},valid:"".concat(o)===t.substr(10,1)&&"".concat(l)===t.substr(11,1)}}return{meta:{},valid:!0}}(v);break;case"se":g=function(e){var t=e;return/^SE[0-9]{10}01$/.test(t)&&(t=t.substr(2)),/^[0-9]{10}01$/.test(t)?(t=t.substr(0,10),{meta:{},valid:c(t)}):{meta:{},valid:!1}}(v);break;case"si":g=function(e){var t=e.match(/^(SI)?([1-9][0-9]{7})$/);if(!t)return{meta:{},valid:!1};for(var r=t[1]?e.substr(2):e,a=[8,7,6,5,4,3,2],n=0,i=0;i<7;i++)n+=parseInt(r.charAt(i),10)*a[i];return 10==(n=11-n%11)&&(n=0),{meta:{},valid:"".concat(n)===r.substr(7,1)}}(v);break;case"sk":g=function(e){var t=e;return/^SK[1-9][0-9][(2-4)|(6-9)][0-9]{7}$/.test(t)&&(t=t.substr(2)),/^[1-9][0-9][(2-4)|(6-9)][0-9]{7}$/.test(t)?{meta:{},valid:parseInt(t,10)%11==0}:{meta:{},valid:!1}}(v);break;case"ve":g=function(e){var t=e;if(/^VE[VEJPG][0-9]{9}$/.test(t)&&(t=t.substr(2)),!/^[VEJPG][0-9]{9}$/.test(t))return{meta:{},valid:!1};for(var r=[3,2,7,6,5,4,3,2],a={E:8,G:20,J:12,P:16,V:4}[t.charAt(0)],n=0;n<8;n++)a+=parseInt(t.charAt(n+1),10)*r[n];return 11!=(a=11-a%11)&&10!==a||(a=0),{meta:{},valid:"".concat(a)===t.substr(9,1)}}(v);break;case"za":g=function(e){var t=e;return/^ZA4[0-9]{9}$/.test(t)&&(t=t.substr(2)),{meta:{},valid:/^4[0-9]{9}$/.test(t)}}(v)}var b=f(i.l10n&&i.l10n.vat?h.message||i.l10n.vat.country:h.message,i.l10n&&i.l10n.vat&&i.l10n.vat.countries?i.l10n.vat.countries[m.toUpperCase()]:m.toUpperCase());return Object.assign({},{message:b},g)}}},Da}();var Za,_a=Ra.exports,Ba={exports:{}},Ga={};Ba.exports=(Za||(Za=1,Ga.vin=function(){return{validate:function(e){if(""===e.value)return{valid:!0};if(!/^[a-hj-npr-z0-9]{8}[0-9xX][a-hj-npr-z0-9]{8}$/i.test(e.value))return{valid:!1};for(var t=e.value.toUpperCase(),r={A:1,B:2,C:3,D:4,E:5,F:6,G:7,H:8,J:1,K:2,L:3,M:4,N:5,P:7,R:9,S:2,T:3,U:4,V:5,W:6,X:7,Y:8,Z:9,0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9},a=[8,7,6,5,4,3,2,10,0,9,8,7,6,5,4,3,2],n=t.length,i=0,s=0;s<n;s++)i+=r["".concat(t.charAt(s))]*a[s];var o="".concat(i%11);return"10"===o&&(o="X"),{valid:o===t.charAt(8)}}}}),Ga);var Ua,ja=Ba.exports,Ka={exports:{}},za={};Ka.exports=function(){if(Ua)return za;Ua=1;var e=i,t=e.utils.format,r=e.utils.removeUndefined;return za.zipCode=function(){var e=["AT","BG","BR","CA","CH","CZ","DE","DK","ES","FR","GB","IE","IN","IT","MA","NL","PL","PT","RO","RU","SE","SG","SK","US"];return{validate:function(a){var n=Object.assign({},{message:""},r(a.options));if(""===a.value||!n.country)return{valid:!0};var i=a.value.substr(0,2);if(!(i="function"==typeof n.country?n.country.call(this):n.country)||-1===e.indexOf(i.toUpperCase()))return{valid:!0};var s=!1;switch(i=i.toUpperCase()){case"AT":case"CH":s=/^([1-9]{1})(\d{3})$/.test(a.value);break;case"BG":s=/^([1-9]{1}[0-9]{3})$/.test(a.value);break;case"BR":s=/^(\d{2})([.]?)(\d{3})([-]?)(\d{3})$/.test(a.value);break;case"CA":s=/^(?:A|B|C|E|G|H|J|K|L|M|N|P|R|S|T|V|X|Y){1}[0-9]{1}(?:A|B|C|E|G|H|J|K|L|M|N|P|R|S|T|V|W|X|Y|Z){1}\s?[0-9]{1}(?:A|B|C|E|G|H|J|K|L|M|N|P|R|S|T|V|W|X|Y|Z){1}[0-9]{1}$/i.test(a.value);break;case"CZ":case"SK":s=/^(\d{3})([ ]?)(\d{2})$/.test(a.value);break;case"DE":s=/^(?!01000|99999)(0[1-9]\d{3}|[1-9]\d{4})$/.test(a.value);break;case"DK":s=/^(DK(-|\s)?)?\d{4}$/i.test(a.value);break;case"ES":s=/^(?:0[1-9]|[1-4][0-9]|5[0-2])\d{3}$/.test(a.value);break;case"FR":s=/^[0-9]{5}$/i.test(a.value);break;case"GB":s=function(e){for(var t="[ABCDEFGHIJKLMNOPRSTUWYZ]",r="[ABCDEFGHKLMNOPQRSTUVWXY]",a="[ABDEFGHJLNPQRSTUWXYZ]",n=0,i=[new RegExp("^(".concat(t,"{1}").concat(r,"?[0-9]{1,2})(\\s*)([0-9]{1}").concat(a,"{2})$"),"i"),new RegExp("^(".concat(t,"{1}[0-9]{1}").concat("[ABCDEFGHJKPMNRSTUVWXY]","{1})(\\s*)([0-9]{1}").concat(a,"{2})$"),"i"),new RegExp("^(".concat(t,"{1}").concat(r,"{1}?[0-9]{1}").concat("[ABEHMNPRVWXY]","{1})(\\s*)([0-9]{1}").concat(a,"{2})$"),"i"),new RegExp("^(BF1)(\\s*)([0-6]{1}[ABDEFGHJLNPQRST]{1}[ABDEFGHJLNPQRSTUWZYZ]{1})$","i"),/^(GIR)(\s*)(0AA)$/i,/^(BFPO)(\s*)([0-9]{1,4})$/i,/^(BFPO)(\s*)(c\/o\s*[0-9]{1,3})$/i,/^([A-Z]{4})(\s*)(1ZZ)$/i,/^(AI-2640)$/i];n<i.length;n++)if(i[n].test(e))return!0;return!1}(a.value);break;case"IN":s=/^\d{3}\s?\d{3}$/.test(a.value);break;case"IE":s=/^(D6W|[ACDEFHKNPRTVWXY]\d{2})\s[0-9ACDEFHKNPRTVWXY]{4}$/.test(a.value);break;case"IT":s=/^(I-|IT-)?\d{5}$/i.test(a.value);break;case"MA":s=/^[1-9][0-9]{4}$/i.test(a.value);break;case"NL":s=/^[1-9][0-9]{3} ?(?!sa|sd|ss)[a-z]{2}$/i.test(a.value);break;case"PL":s=/^[0-9]{2}-[0-9]{3}$/.test(a.value);break;case"PT":s=/^[1-9]\d{3}-\d{3}$/.test(a.value);break;case"RO":s=/^(0[1-8]{1}|[1-9]{1}[0-5]{1})?[0-9]{4}$/i.test(a.value);break;case"RU":s=/^[0-9]{6}$/i.test(a.value);break;case"SE":s=/^(S-)?\d{3}\s?\d{2}$/i.test(a.value);break;case"SG":s=/^([0][1-9]|[1-6][0-9]|[7]([0-3]|[5-9])|[8][0-2])(\d{4})$/i.test(a.value);break;default:s=/^\d{4,5}([-]?\d{4})?$/.test(a.value)}return{message:t(a.l10n&&a.l10n.zipCode?n.message||a.l10n.zipCode.country:n.message,a.l10n&&a.l10n.zipCode&&a.l10n.zipCode.countries?a.l10n.zipCode.countries[i]:i),valid:s}}}},za}();var Ya=Ka.exports,Ja={Alias:d.Alias,Aria:p.Aria,Declarative:g.Declarative,DefaultSubmit:E.DefaultSubmit,Dependency:O.Dependency,Excluded:k.Excluded,FieldStatus:N.FieldStatus,Framework:_.Framework,Icon:j.Icon,Message:D.Message,Sequence:J.Sequence,SubmitButton:Q.SubmitButton,Tooltip:ae.Tooltip,Trigger:oe.Trigger},Xa={between:ce.between,blank:he.blank,callback:Ae.callback,choice:xe.choice,creditCard:Ve.creditCard,date:He.date,different:Te.different,digits:De.digits,emailAddress:Ge.emailAddress,file:ze.file,greaterThan:We.greaterThan,identical:tt.identical,integer:it.integer,ip:dt.ip,lessThan:pt.lessThan,notEmpty:gt.notEmpty,numeric:Et.numeric,promise:Ot.promise,regexp:kt.regexp,remote:Nt.remote,stringCase:Pt.stringCase,stringLength:_t.stringLength,uri:jt.uri,base64:Jt.base64,bic:Qt.bic,color:ar.color,cusip:or.cusip,ean:cr.ean,ein:hr.ein,grid:Ar.grid,hex:xr.hex,iban:Vr.iban,id:Hr.id,imei:Tr.imei,imo:Dr.imo,isbn:Gr.isbn,isin:zr.isin,ismn:Wr.ismn,issn:ta.issn,mac:ia.mac,meid:da.meid,phone:pa.phone,rtn:ga.rtn,sedol:Ea.sedol,siren:Oa.siren,siret:ka.siret,step:Na.step,uuid:Pa.uuid,vat:_a.vat,vin:ja.vin,zipCode:Ya.zipCode};e.Plugin=i.Plugin,e.algorithms=i.algorithms,e.formValidation=function(e,t){var r=i.formValidation(e,t);return Object.keys(Xa).forEach((function(e){return r.registerValidator(e,Xa[e])})),r},e.plugins=Ja,e.utils=i.utils,e.validators=Xa}));
