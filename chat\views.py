from django.contrib.auth.decorators import login_required
from django.shortcuts import render
from django.utils.safestring import mark_safe
import json


# Create your views here.
def index(request):
    return render(request, 'chat/index.html', {})

@login_required
def chat(request, *args, **kwargs):
    # if not request.user.is_authenticated:
    #     return redirect("login-user")
    context = {}
    return render(request, "chat/char.html", context)

@login_required
def room(request, room_name):
    return render(request, 'chat/room.html', {
        'room_name_json': mark_safe(json.dumps(room_name)),
        'username': mark_safe(json.dumps(request.user.username)),
    })
