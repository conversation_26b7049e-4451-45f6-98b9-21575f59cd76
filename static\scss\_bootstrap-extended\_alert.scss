// Alerts
// *******************************************************************************

// Alert mixins
@each $state, $value in $theme-colors {
  @if $state != primary and $state != light {
    @include template-alert-variant('.alert-#{$state}', $value);
    @include template-alert-outline-variant('.alert-outline-#{$state}', $value);
    @include template-alert-solid-variant('.alert-solid-#{$state}', $value);
  }
}

// Adjust close link position
.alert-dismissible {
  .btn-close {
    padding: $alert-padding-y * 1.42 $alert-padding-x;
  }
}

// RTL
// *******************************************************************************

@include rtl-only {
  .alert-dismissible {
    padding-left: $alert-dismissible-padding-r;
    padding-right: $alert-padding-x;
  }

  .alert-dismissible .btn-close {
    right: auto;
    left: 0;
  }
}
