from django import forms
from django.contrib.auth.models import User
from main_app.models import *

class UserForm(forms.ModelForm):
    password = forms.CharField(widget=forms.PasswordInput())

    class Meta():
        model = User
        fields = ('username','email','password')
        labels = {
            'username':"Nome de Usuário",
            'password':"Palavra-Chave"
        }

    def __init__(self, *args, **kwargs):
        super(UserForm, self).__init__(*args, **kwargs)
        self.fields['username'].help_text = None


class StudentForm(forms.ModelForm):
    class Meta():
        model = Student
        # fields = ('portfolio_site','profile_pic')
        fields = ('lastYearResult','targetResult','gender')
        labels = {
            'studentNumber':"Num de Estudante",
            'countryOrig':"País",
            'gender':"Gênero",
            'course':"Curso",
            'lastYearResult':"Média do ano anterior",
            'targetResult':"Média pretendido",
        }

class LecturerForm(forms.ModelForm):
    class Meta():
        model = Lecturer

        fields = ('subject','course','extrovertedIntroverted','sensingIntuition','thinkingFeeling','judgingPerceiving')
        labels = {
            'studentNumber':"Num de Estudante",
            'countryOrig':"País",
            'extrovertedIntroverted':"Extrovertido ou Introvertido",
            'sensingIntuition':"Sensorial ou Intuitvo",
            'thinkingFeeling':"Pensativo ou Sensorial",
            'judgingPerceiving':"Julgativo ou Perceptivo",
        }


class AssistantForm(forms.ModelForm):
    class Meta():
        model = Assistant

        fields = ('subject','countryOrig','extrovertedIntroverted','sensingIntuition','thinkingFeeling','judgingPerceiving')
        #'ps2clhModel',
        labels = {
            'studentNumber':"Num de Estudante",
            'countryOrig':"País",
            'extrovertedIntroverted':"Extrovertido ou Introvertido",
            'sensingIntuition':"Sensorial ou Intuitvo",
            'thinkingFeeling':"Pensativo ou Sensorial",
            'judgingPerceiving':"Julgativo ou Perceptivo",
        }

class StudentInfoForm(forms.ModelForm):
    class Meta():
        model = StudentInfo

        fields = ('stress','anxietyFear','lowStandards','lowSelfSteem','feelDepressed', 'loneliness',
        'immediacy','notAimExcellence','badTimeManagement','setPriorities','lackSelfControl','achievePersonalGoals',
        'discrimination','longDistance','familyIncome','studyingWorking','dropout','addictedSensualImagesVideo',
        'expressingYourself','fluencyLanguage','grammarVocabulary','understandLecturerClassroom', 'understandingReading','learningProblemImpactCommunication',
        'reread','practiceTests','selfExplanation','prepareSummaries','highlightingUnderlining', 'preparationQuestionnaire',
        'sleepProblems','feelAlwaysTired','eatingUnhealthily','feelMentallyUnhealthy','lackEnergyStudyTime', 'noRegularPhysicalActivityExercise',
        'extrovertedIntroverted','sensingIntuition','thinkingFeeling','judgingPerceiving')


class StudentClusterForm(forms.ModelForm):
    class Meta():
        model = StudentClusterModel

        fields = ('psychology','selfResponsability','psychologySelfResponsability','sociology','communication','sociologyCommunication',
                  'learning','healthWellbeing','learningHealthWellbeing','centroid','cluster')


class AddQAForm(forms.ModelForm):
    class Meta():
        model = AddQAModel

        fields = ('username','context','question','answer','display_picture')


class VisualRepForm(forms.ModelForm):
    class Meta():
        model = VisualRepModel

        fields = ('user','targetVariable','psychology','psychologyVal','self_responsibility','self_responsibilityVal','sociology','sociologyVal',
                  'communication','communicationVal','learning','learningVal','health_wellbeing','health_wellbeing_Val')
