/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-recaptcha
 * @version 2.4.0
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("@form-validation/core")):"function"==typeof define&&define.amd?define(["@form-validation/core"],t):((e="undefined"!=typeof globalThis?globalThis:e||self).FormValidation=e.FormValidation||{},e.FormValidation.plugins=e.FormValidation.plugins||{},e.FormValidation.plugins.Recaptcha=t(e.FormValidation))}(this,(function(e){"use strict";var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},t(e,i)};var i=e.utils.fetch,o=e.utils.removeUndefined;return function(e){function a(t){var i=e.call(this,t)||this;return i.widgetIds=new Map,i.captchaStatus="NotValidated",i.opts=Object.assign({},a.DEFAULT_OPTIONS,o(t)),i.fieldResetHandler=i.onResetField.bind(i),i.preValidateFilter=i.preValidate.bind(i),i.iconPlacedHandler=i.onIconPlaced.bind(i),i}return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function o(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}(a,e),a.prototype.install=function(){var e=this;this.core.on("core.field.reset",this.fieldResetHandler).on("plugins.icon.placed",this.iconPlacedHandler).registerFilter("validate-pre",this.preValidateFilter);var t=void 0===window[a.LOADED_CALLBACK]?function(){}:window[a.LOADED_CALLBACK];window[a.LOADED_CALLBACK]=function(){t();var o={badge:e.opts.badge,callback:function(){""===e.opts.backendVerificationUrl&&(e.captchaStatus="Valid",e.core.updateFieldStatus(a.CAPTCHA_FIELD,"Valid"))},"error-callback":function(){e.captchaStatus="Invalid",e.core.updateFieldStatus(a.CAPTCHA_FIELD,"Invalid")},"expired-callback":function(){e.captchaStatus="NotValidated",e.core.updateFieldStatus(a.CAPTCHA_FIELD,"NotValidated")},sitekey:e.opts.siteKey,size:e.opts.size},n=window.grecaptcha.render(e.opts.element,o);e.widgetIds.set(e.opts.element,n),e.core.addField(a.CAPTCHA_FIELD,{validators:{promise:{message:e.opts.message,promise:function(t){var o,n=e.widgetIds.has(e.opts.element)?window.grecaptcha.getResponse(e.widgetIds.get(e.opts.element)):t.value;return""===n?(e.captchaStatus="Invalid",Promise.resolve({valid:!1})):""===e.opts.backendVerificationUrl?(e.captchaStatus="Valid",Promise.resolve({valid:!0})):"Valid"===e.captchaStatus?Promise.resolve({valid:!0}):i(e.opts.backendVerificationUrl,{method:"POST",params:(o={},o[a.CAPTCHA_FIELD]=n,o)}).then((function(t){var i="true"==="".concat(t.success);return e.captchaStatus=i?"Valid":"Invalid",Promise.resolve({meta:t,valid:i})})).catch((function(t){return e.captchaStatus="NotValidated",Promise.reject({valid:!1})}))}}}})};var o=this.getScript();if(!document.body.querySelector('script[src="'.concat(o,'"]'))){var n=document.createElement("script");n.type="text/javascript",n.async=!0,n.defer=!0,n.src=o,document.body.appendChild(n)}},a.prototype.uninstall=function(){delete window[a.LOADED_CALLBACK],this.timer&&clearTimeout(this.timer),this.core.off("core.field.reset",this.fieldResetHandler).off("plugins.icon.placed",this.iconPlacedHandler).deregisterFilter("validate-pre",this.preValidateFilter),this.widgetIds.clear();var e=this.getScript();[].slice.call(document.body.querySelectorAll('script[src="'.concat(e,'"]'))).forEach((function(e){return e.parentNode.removeChild(e)})),this.core.removeField(a.CAPTCHA_FIELD)},a.prototype.onEnabled=function(){this.core.enableValidator(a.CAPTCHA_FIELD,"promise")},a.prototype.onDisabled=function(){this.core.disableValidator(a.CAPTCHA_FIELD,"promise")},a.prototype.getScript=function(){var e=this.opts.language?"&hl=".concat(this.opts.language):"";return"https://www.google.com/recaptcha/api.js?onload=".concat(a.LOADED_CALLBACK,"&render=explicit").concat(e)},a.prototype.preValidate=function(){var e=this;if(this.isEnabled&&"invisible"===this.opts.size&&this.widgetIds.has(this.opts.element)){var t=this.widgetIds.get(this.opts.element);return"Valid"===this.captchaStatus?Promise.resolve():new Promise((function(i,o){window.grecaptcha.execute(t).then((function(){e.timer&&clearTimeout(e.timer),e.timer=window.setTimeout(i,1e3)}))}))}return Promise.resolve()},a.prototype.onResetField=function(e){if(e.field===a.CAPTCHA_FIELD&&this.widgetIds.has(this.opts.element)){var t=this.widgetIds.get(this.opts.element);window.grecaptcha.reset(t)}},a.prototype.onIconPlaced=function(e){if(e.field===a.CAPTCHA_FIELD)if("invisible"===this.opts.size)e.iconElement.style.display="none";else{var t=document.getElementById(this.opts.element);t&&t.parentNode.insertBefore(e.iconElement,t.nextSibling)}},a.CAPTCHA_FIELD="g-recaptcha-response",a.DEFAULT_OPTIONS={backendVerificationUrl:"",badge:"bottomright",size:"normal",theme:"light"},a.LOADED_CALLBACK="___reCaptchaLoaded___",a}(e.Plugin)}));
