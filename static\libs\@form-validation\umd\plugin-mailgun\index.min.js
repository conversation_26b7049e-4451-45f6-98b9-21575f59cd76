/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-mailgun
 * @version 2.4.0
 */

!function(e,i){"object"==typeof exports&&"undefined"!=typeof module?module.exports=i(require("@form-validation/core"),require("@form-validation/plugin-alias")):"function"==typeof define&&define.amd?define(["@form-validation/core","@form-validation/plugin-alias"],i):((e="undefined"!=typeof globalThis?globalThis:e||self).FormValidation=e.FormValidation||{},e.FormValidation.plugins=e.FormValidation.plugins||{},e.FormValidation.plugins.Mailgun=i(e.FormValidation,e.FormValidation.plugins))}(this,(function(e,i){"use strict";var t=function(e,i){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,i){e.__proto__=i}||function(e,i){for(var t in i)Object.prototype.hasOwnProperty.call(i,t)&&(e[t]=i[t])},t(e,i)};var o=e.utils.removeUndefined;return function(e){function n(i){var t=e.call(this,i)||this;return t.opts=Object.assign({},{suggestion:!1},o(i)),t.messageDisplayedHandler=t.onMessageDisplayed.bind(t),t}return function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function o(){this.constructor=e}t(e,i),e.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)}(n,e),n.prototype.install=function(){this.opts.suggestion&&this.core.on("plugins.message.displayed",this.messageDisplayedHandler);this.core.registerPlugin(n.ALIAS_PLUGIN,new i.Alias({mailgun:"remote"})).addField(this.opts.field,{validators:{mailgun:{crossDomain:!0,data:{api_key:this.opts.apiKey},headers:{"Content-Type":"application/json"},message:this.opts.message,name:"address",url:"https://api.mailgun.net/v3/address/validate",validKey:"is_valid"}}})},n.prototype.uninstall=function(){this.opts.suggestion&&this.core.off("plugins.message.displayed",this.messageDisplayedHandler),this.core.deregisterPlugin(n.ALIAS_PLUGIN),this.core.removeField(this.opts.field)},n.prototype.onEnabled=function(){this.core.enableValidator(this.opts.field,"mailgun").enablePlugin(n.ALIAS_PLUGIN)},n.prototype.onDisabled=function(){this.core.disableValidator(this.opts.field,"mailgun").disablePlugin(n.ALIAS_PLUGIN)},n.prototype.onMessageDisplayed=function(e){this.isEnabled&&e.field===this.opts.field&&"mailgun"===e.validator&&e.meta&&e.meta.did_you_mean&&(e.messageElement.innerHTML="Did you mean ".concat(e.meta.did_you_mean,"?"))},n.ALIAS_PLUGIN="___mailgunAlias",n}(e.Plugin)}));
