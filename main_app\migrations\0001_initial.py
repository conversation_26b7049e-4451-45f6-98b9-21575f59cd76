# Generated by Django 4.2.5 on 2023-11-22 13:21

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AddQAModel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("username", models.CharField(default=None, max_length=50)),
                ("context", models.Char<PERSON>ield(max_length=50)),
                ("question", models.CharField(max_length=100)),
                ("answer", models.Char<PERSON>ield(max_length=300)),
                ("display_picture", models.FileField(upload_to="")),
            ],
        ),
        migrations.CreateModel(
            name="StudentData",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("studentID", models.FloatField(default=None)),
                ("email", models.Char<PERSON>ield(max_length=50)),
                ("lastYearResult", models.IntegerField()),
                ("targetVariable", models.IntegerField()),
                ("stress", models.IntegerField()),
                ("feelDepressed", models.IntegerField()),
                ("anxietyFear", models.IntegerField()),
                ("disturbanceModeBeing", models.IntegerField()),
                ("loneness", models.IntegerField()),
                ("lowSelfsteam", models.IntegerField()),
                ("unspecifiedPsychProb", models.IntegerField()),
                ("totalPsychology", models.IntegerField()),
                ("setPriorities", models.IntegerField()),
                ("establishAchievePersonalGoals", models.IntegerField()),
                ("timeManagement", models.IntegerField()),
                ("aimExcellence", models.IntegerField()),
                ("procrastination", models.IntegerField()),
                ("immediacy", models.IntegerField()),
                ("acceptChange", models.IntegerField()),
                ("hoursStudyPerDay", models.IntegerField()),
                ("totalSelfResponsibility", models.IntegerField()),
                ("studyingWorking", models.IntegerField()),
                ("bullying", models.IntegerField()),
                ("familyIncome", models.IntegerField()),
                ("AdictSensualImages", models.IntegerField()),
                ("discouragementNegativity", models.IntegerField()),
                ("longDistance", models.IntegerField()),
                ("certainNegativeBeliefsHabits", models.IntegerField()),
                ("badConditionsHabitability", models.IntegerField()),
                ("lackElectricity", models.IntegerField()),
                ("lackPublicTransport", models.IntegerField()),
                ("universityCondictions", models.IntegerField()),
                ("hoursPlayDistractionPerDay", models.IntegerField()),
                ("totalSociology", models.IntegerField()),
                ("fluencyInLanguage", models.IntegerField()),
                ("understandLecturerClassroom", models.IntegerField()),
                ("understandingInterpretationOfReading", models.IntegerField()),
                ("expressingYourself", models.IntegerField()),
                ("grammarVocabulary", models.IntegerField()),
                ("stutteringTypologyDisfluencies", models.IntegerField()),
                ("learningProbImpactCommunication", models.IntegerField()),
                ("totalCommunication", models.IntegerField()),
                ("preparationQuestionnaire", models.IntegerField()),
                ("highlightingUnderlining", models.IntegerField()),
                ("practiceTests", models.IntegerField()),
                ("reread", models.IntegerField()),
                ("distributedStudy", models.IntegerField()),
                ("selfExplanation", models.IntegerField()),
                ("prepareSummaries", models.IntegerField()),
                ("totalLearningTec", models.IntegerField()),
                ("sleepProblems", models.IntegerField()),
                ("feelMentallyHealthy", models.IntegerField()),
                ("regularPhysicalActivityExercise", models.IntegerField()),
                ("plentyEnergyDuringStudy", models.IntegerField()),
                ("eatingHealthily", models.IntegerField()),
                ("restBodyMind", models.IntegerField()),
                ("totalHealthWellbeing", models.IntegerField()),
            ],
        ),
        migrations.CreateModel(
            name="VisualRepModel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("user", models.CharField(default=None, max_length=50)),
                ("targetVariable", models.IntegerField()),
                ("psychology", models.CharField(max_length=50)),
                ("psychologyVal", models.IntegerField()),
                ("self_responsibility", models.CharField(max_length=50)),
                ("self_responsibilityVal", models.IntegerField()),
                ("sociology", models.CharField(max_length=50)),
                ("sociologyVal", models.IntegerField()),
                ("communication", models.CharField(max_length=50)),
                ("communicationVal", models.IntegerField()),
                ("learning", models.CharField(max_length=50)),
                ("learningVal", models.IntegerField()),
                ("health_wellbeing", models.CharField(max_length=50)),
                ("health_wellbeing_Val", models.IntegerField()),
            ],
        ),
        migrations.CreateModel(
            name="WeightPS2CLH",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("country", models.CharField(max_length=25)),
                ("university", models.CharField(max_length=50)),
                ("dateModel", models.DateTimeField()),
                ("stress", models.CharField(max_length=25)),
                ("stressW", models.FloatField()),
                ("anxietyFear", models.CharField(max_length=25)),
                ("anxietyFearW", models.FloatField()),
                ("lowStandards", models.CharField(max_length=25)),
                ("lowStandardsW", models.FloatField()),
                ("lowSelfSteem", models.CharField(max_length=25)),
                ("lowSelfSteemW", models.FloatField()),
                ("feelDepressed", models.CharField(max_length=25)),
                ("feelDepressedW", models.FloatField()),
                ("loneliness", models.CharField(max_length=25)),
                ("lonelinessW", models.FloatField()),
                ("immediacy", models.CharField(max_length=25)),
                ("immediacyW", models.FloatField()),
                ("notAimExcellence", models.CharField(max_length=25)),
                ("notAimExcellenceW", models.FloatField()),
                ("badTimeManagement", models.CharField(max_length=25)),
                ("badTimeManagementW", models.FloatField()),
                ("setPriorities", models.CharField(max_length=25)),
                ("setPrioritiesW", models.FloatField()),
                ("lackSelfControl", models.CharField(max_length=25)),
                ("lackSelfControlW", models.FloatField()),
                ("achievePersonalGoals", models.CharField(max_length=25)),
                ("achievePersonalGoalsW", models.FloatField()),
                ("discrimination", models.CharField(max_length=25)),
                ("discriminationW", models.FloatField()),
                ("longDistance", models.CharField(max_length=25)),
                ("longDistanceW", models.FloatField()),
                ("familyIncome", models.CharField(max_length=25)),
                ("familyIncomeW", models.FloatField()),
                ("studyingWorking", models.CharField(max_length=25)),
                ("studyingWorkingW", models.FloatField()),
                ("dropout", models.CharField(max_length=25)),
                ("dropoutW", models.FloatField()),
                ("addictedSensualImagesVideo", models.CharField(max_length=25)),
                ("addictedSensualImagesVideoW", models.FloatField()),
                ("expressingYourself", models.CharField(max_length=25)),
                ("expressingYourselfW", models.FloatField()),
                ("fluencyLanguage", models.CharField(max_length=25)),
                ("fluencyLanguageW", models.FloatField()),
                ("grammarVocabulary", models.CharField(max_length=25)),
                ("grammarVocabularyW", models.FloatField()),
                ("understandLecturerClassroom", models.CharField(max_length=25)),
                ("understandLecturerClassroomW", models.FloatField()),
                ("understandingReading", models.CharField(max_length=25)),
                ("understandingReadingW", models.FloatField()),
                ("learningProblemImpactCommunication", models.CharField(max_length=25)),
                ("learningProblemImpactCommunicationW", models.FloatField()),
                ("reread", models.CharField(max_length=25)),
                ("rereadW", models.FloatField()),
                ("practiceTests", models.CharField(max_length=25)),
                ("practiceTestsW", models.FloatField()),
                ("selfExplanation", models.CharField(max_length=25)),
                ("selfExplanationW", models.FloatField()),
                ("prepareSummaries", models.CharField(max_length=25)),
                ("prepareSummariesW", models.FloatField()),
                ("highlightingUnderlining", models.CharField(max_length=25)),
                ("highlightingUnderliningW", models.FloatField()),
                ("preparationQuestionnaire", models.CharField(max_length=25)),
                ("preparationQuestionnaireW", models.FloatField()),
                ("sleepProblems", models.CharField(max_length=25)),
                ("sleepProblemsW", models.FloatField()),
                ("feelAlwaysTired", models.CharField(max_length=25)),
                ("feelAlwaysTiredW", models.FloatField()),
                ("eatingUnhealthily", models.CharField(max_length=25)),
                ("eatingUnhealthilyW", models.FloatField()),
                ("feelMentallyUnhealthy", models.CharField(max_length=25)),
                ("feelMentallyUnhealthyW", models.FloatField()),
                ("lackEnergyStudyTime", models.CharField(max_length=25)),
                ("lackEnergyStudyTimeW", models.FloatField()),
                ("noRegularPhysicalActivityExercise", models.CharField(max_length=25)),
                ("noRegularPhysicalActivityExerciseW", models.FloatField()),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="StudentInfo",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("stress", models.IntegerField()),
                ("anxietyFear", models.IntegerField()),
                ("lowStandards", models.IntegerField()),
                ("lowSelfSteem", models.IntegerField()),
                ("feelDepressed", models.IntegerField()),
                ("loneliness", models.IntegerField()),
                ("immediacy", models.IntegerField()),
                ("notAimExcellence", models.IntegerField()),
                ("badTimeManagement", models.IntegerField()),
                ("setPriorities", models.IntegerField()),
                ("lackSelfControl", models.IntegerField()),
                ("achievePersonalGoals", models.IntegerField()),
                ("discrimination", models.IntegerField()),
                ("longDistance", models.IntegerField()),
                ("familyIncome", models.IntegerField()),
                ("studyingWorking", models.IntegerField()),
                ("dropout", models.IntegerField()),
                ("addictedSensualImagesVideo", models.IntegerField()),
                ("expressingYourself", models.IntegerField()),
                ("fluencyLanguage", models.IntegerField()),
                ("grammarVocabulary", models.IntegerField()),
                ("understandLecturerClassroom", models.IntegerField()),
                ("understandingReading", models.IntegerField()),
                ("learningProblemImpactCommunication", models.IntegerField()),
                ("reread", models.IntegerField()),
                ("practiceTests", models.IntegerField()),
                ("selfExplanation", models.IntegerField()),
                ("prepareSummaries", models.IntegerField()),
                ("highlightingUnderlining", models.IntegerField()),
                ("preparationQuestionnaire", models.IntegerField()),
                ("sleepProblems", models.IntegerField()),
                ("feelAlwaysTired", models.IntegerField()),
                ("eatingUnhealthily", models.IntegerField()),
                ("feelMentallyUnhealthy", models.IntegerField()),
                ("lackEnergyStudyTime", models.IntegerField()),
                ("noRegularPhysicalActivityExercise", models.IntegerField()),
                ("extrovertedIntroverted", models.IntegerField()),
                ("sensingIntuition", models.IntegerField()),
                ("thinkingFeeling", models.IntegerField()),
                ("judgingPerceiving", models.IntegerField()),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="StudentClusterModel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("psychology", models.FloatField(default=None)),
                ("selfResponsability", models.FloatField(default=None)),
                ("psychologySelfResponsability", models.FloatField(default=None)),
                ("sociology", models.FloatField(default=None)),
                ("communication", models.FloatField(default=None)),
                ("sociologyCommunication", models.FloatField(default=None)),
                ("learning", models.FloatField(default=None)),
                ("healthWellbeing", models.FloatField(default=None)),
                ("learningHealthWellbeing", models.FloatField(default=None)),
                ("centroid", models.CharField(default=None, max_length=60)),
                ("cluster", models.IntegerField()),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Student",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("lastYearResult", models.IntegerField()),
                ("targetResult", models.IntegerField()),
                ("gender", models.IntegerField()),
                ("studentNumber", models.CharField(max_length=25)),
                ("countryOrig", models.CharField(max_length=25)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Lecturer",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("subject", models.CharField(max_length=25)),
                ("extrovertedIntroverted", models.IntegerField()),
                ("sensingIntuition", models.IntegerField()),
                ("thinkingFeeling", models.IntegerField()),
                ("judgingPerceiving", models.IntegerField()),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Assistant",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("subject", models.CharField(max_length=25)),
                ("ps2clhModel", models.CharField(max_length=50)),
                ("countryOrig", models.CharField(max_length=25)),
                ("extrovertedIntroverted", models.IntegerField()),
                ("sensingIntuition", models.IntegerField()),
                ("thinkingFeeling", models.IntegerField()),
                ("judgingPerceiving", models.IntegerField()),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
