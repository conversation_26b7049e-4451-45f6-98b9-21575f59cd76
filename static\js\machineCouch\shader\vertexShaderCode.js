/**
 * Vertex shader program. The program is included as concatenated string in a constant variable.
 * <AUTHOR> <PERSON>
 * @since 2019
 */
machineCouch.shader.VERTEX_SHADER_CODE = "\
// Attributes.\n\
attribute vec3 a_position;\
attribute float a_pointIndex;\
attribute vec3 a_pointColor;\
\
// Uniforms.\n\
uniform float u_clusterIndex;\
uniform float u_clusterSelectedIndex;\
uniform float u_pointSize;\
uniform vec4 u_color;\
uniform int u_isClusterVS;\
uniform float u_pointSelectedIndex;\
uniform mat4 u_modelMatrix;\
uniform mat4 u_viewMatrix;\
uniform mat4 u_projMatrix;\
\
// Varyings.\n\
varying vec4 v_color;\
\
void main() {\
  gl_Position = u_projMatrix * u_viewMatrix * u_modelMatrix * vec4(a_position, 1.0);\
  gl_PointSize = u_pointSize;\
\
  if (u_isClusterVS == 1) {\
    if (u_clusterIndex == u_clusterSelectedIndex && a_pointIndex == u_pointSelectedIndex) {\
      v_color = vec4(0.0, 0.0, 0.0, -1.0);\
    } else {\
      v_color = vec4(a_pointColor, 1.0);\
    }\
  } else {\
    v_color = u_color;\
  }\
}";
