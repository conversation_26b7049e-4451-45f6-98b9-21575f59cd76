from django.urls import include, re_path
from main_app import views

# SET THE NAMESPACE!
app_name = 'main_app'

# Be careful setting the name to just /login use userlogin instead!
urlpatterns=[
    re_path(r'^register/$',views.register,name='register'),
    re_path(r'^user_login/$',views.user_login,name='user_login'),
    re_path(r'^assistant_login/$',views.assistant_login,name='assistant_login'),
    re_path(r'^lecturer_login/$',views.lecturer_login,name='lecturer_login'),
    re_path(r'^user_login/$',views.user_profile,name='profile'),
    # re_path(r'^user_login/$',views.user_questionnaire,name='user_questionnaire'),
    
    re_path(r'^page_user_login/',views.page_user_login,name='page_user_login'),
    re_path(r'^page_lect_login/',views.page_lect_login,name='page_lect_login'),
    re_path(r'^page_assist_login/',views.page_assist_login,name='page_assist_login'),
    re_path(r'^page_assist_visualRep/',views.page_assist_visualRep,name='page_assist_visualRep'),
    re_path(r'^process_visualRep/',views.process_visualRep,name='process_visualRep'),
    
    re_path(r'^regist_assist/',views.regist_assist,name='regist_assist'),
    re_path(r'^regist_lecturer/',views.regist_lecturer,name='regist_lecturer'),

    re_path(r'^registration_assistant/',views.registration_assistant,name='registration_assistant'),
    re_path(r'^registration_lecturer/',views.registration_lecturer,name='registration_lecturer'),


    re_path(r'^user_profile_4/',views.user_profile_4,name='user_profile_4'),
    re_path(r'^form_wizard_view/',views.form_wizard_view,name='student_wizard'),
    re_path(r'^form_cluster/',views.form_cluster,name='student_cluster'),

    re_path(r'^chatbot_student/',views.chatbot_student,name='chatbot_student'),
    
    re_path(r'^addQA_assistant/',views.addQA_assistant,name='addQA_assistant'),
    re_path(r'^addQA_assistant_form/',views.addQA_assistant_form,name='addQA_assistant_form'),
    
    #ssssssssssssssssssssssssssssssssssssssssssssssssssssssssssss
    
    # re_path(r'^machine_couch/',views.machine_couch,name='machine_couch'),
    re_path(r'^student_machine_coach_start/',views.student_machine_coach_start,name='student_machine_coach_start'),
    re_path(r'^student_machine_coach_profile3/',views.student_machine_coach_profile3,name='student_machine_coach_profile3'),
    re_path(r'^student_machine_coach_profile2/',views.student_machine_coach_profile2,name='student_machine_coach_profile2'),
    re_path(r'^student_machine_coach_overview/',views.student_machine_coach_overview,name='student_machine_coach_overview'),
    re_path(r'^student_machine_coach_personal_information/',views.student_machine_coach_personal_information,name='student_machine_coach_personal_information'),
    
    re_path(r'^forgot_password_assistant/',views.forgot_password_assistant,name='forgot_password_assistant'),
    re_path(r'^lecturer_forgot/',views.lecturer_forgot,name='lecturer_forgot'),
    # re_path(r'^chatbot/',views.chatbot,name='chatbot'),
    # re_path(r'^user_login/$',views.user_questionnaire,name='questionnaire'),
    # re_path(r'^page_user_login/',views.page_user_login,name='page_user_login'),
]