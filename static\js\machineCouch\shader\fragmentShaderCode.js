/**
 * Fragment shader program. The program is included as concatenated string in a constant variable.
 * <AUTHOR> <PERSON>
 * @since 2019
 */
machineCouch.shader.FRAGMENT_SHADER_CODE = "\
precision mediump float;\
\
// Vertex attributes.\n\
uniform int u_isClusterFS;\
\
// Material attributes.\n\
varying vec4 v_color;\
\
void main() {\
	if (u_isClusterFS == 1) {\
		float dist = distance(gl_PointCoord, vec2(0.5, 0.5));\
\
		if (dist >= 0.5) {\
			discard;\
		} else if (v_color.a < 0.0) {\
			if (dist >= 0.4) {\
				gl_FragColor = vec4(1.0, 1.0, 1.0, 1.0);\
			} else {\
				gl_FragColor = vec4(0.0, 0.0, 0.0, 1.0);\
			}\
		} else {\
			gl_FragColor = v_color;\
		}\
	} else {\
		gl_FragColor = v_color;\
	}\
}";
