@use '../../scss/_bootstrap-extended/include' as light;
@use '../../scss/_bootstrap-extended/include-dark' as dark;
@import '../../scss/_custom-variables/libs';

@import '../../node_modules/datatables.net-rowgroup-bs5/css/rowGroup.bootstrap5';

// Light style
@if $enable-light-style {
  .light-style {
    tr.group,
    tr.group:hover {
      background-color: light.$gray-100 !important;
    }
  }
}

// Dark style
@if $enable-dark-style {
  .dark-style {
    tr.group,
    tr.group:hover {
      background-color: dark.$gray-200 !important;
    }
  }
}
