// * Switches
// *******************************************************************************

@mixin template-switch-size-base(
  $size,
  $width,
  $height,
  $font-size,
  $form-label-font-size,
  $label-line-height,
  $inner-spacer
) {
  min-height: $height;

  font-size: $form-label-font-size;
  line-height: $label-line-height;

  $delta: px-to-rem(ceil(rem-to-px($height * 0.125)));
  $line-height-computed: $form-label-font-size * $label-line-height;
  .switch-label:first-child {
    padding-right: $switch-gutter;
  }
  .switch-input ~ .switch-label {
    padding-left: $width + $switch-gutter;
  }

  .switch-toggle-slider {
    width: $width;
    height: $height - ($delta * 2);
    font-size: $font-size;
    line-height: $height;

    i {
      position: relative;
      font-size: $form-label-font-size;
      @if ($size== 'lg') {
        top: -2px;
      } @else if ($size== 'sm') {
        top: -2px;
      } @else {
        top: -1.35px;
      }
    }

    @if ($line-height-computed > $height) {
      top: (($line-height-computed - $height) * 0.5) + $delta;
    } @else {
      top: 0 + $delta;
    }
  }

  .switch-label {
    @if ($line-height-computed < $height) {
      top: ($height - $line-height-computed) * 0.5;
    } @else {
      top: 0;
    }
  }

  .switch-input:checked ~ .switch-toggle-slider::after {
    left: $width - $height;
  }

  .switch-toggle-slider::after {
    top: $delta * -1;
    margin: $inner-spacer 0 0 $inner-spacer;
    width: floor(rem-to-px($height - $inner-spacer * 2));
    height: floor(rem-to-px($height - $inner-spacer * 2));
  }

  .switch-on {
    padding-left: $inner-spacer;
    padding-right: $height - $inner-spacer;
  }

  .switch-off {
    padding-left: $height - $inner-spacer;
    padding-right: $inner-spacer;
  }

  .switch-input:checked ~ .switch-toggle-slider::before {
    left: $width - $height;
  }

  .switch-toggle-slider::before {
    top: $delta * -1;
    width: $height;
    height: $height;
  }

  @if $rtl-support {
    [dir='rtl'] & .switch-label {
      padding-right: $width + $switch-gutter;
      padding-left: 0;
    }

    [dir='rtl'] & .switch-input:checked ~ .switch-toggle-slider::after {
      left: auto;
      right: $width - $height;
    }

    [dir='rtl'] & .switch-toggle-slider::after {
      margin-left: 0;
      margin-right: $inner-spacer;
    }

    [dir='rtl'] & .switch-input:checked ~ .switch-toggle-slider::before {
      right: $width - $height;
      left: auto;
    }

    [dir='rtl'] & .switch-on {
      padding-left: $height - $inner-spacer;
      padding-right: $inner-spacer;
    }

    [dir='rtl'] & .switch-off {
      padding-left: $inner-spacer;
      padding-right: $height - $inner-spacer;
    }
  }
}

// Switch size
@mixin template-switch-size(
  $size,
  $width,
  $height,
  $font-size,
  $form-label-font-size,
  $label-line-height,
  $inner-spacer: $switch-inner-spacer
) {
  .switch-#{$size} {
    @include template-switch-size-base(
      $size,
      $width,
      $height,
      $font-size,
      $form-label-font-size,
      $label-line-height,
      $inner-spacer
    );
  }
}

// Switch variant
@mixin template-material-switch-variant($parent, $background, $color: null) {
  $selector: if($parent== '', '', '#{$parent}.switch');

  #{$selector} .switch-input:checked ~ .switch-toggle-slider {
    background: rgba($background, 0.5);

    &::after {
      background: $background;
    }
  }
  #{$selector} .switch-input:checked:hover ~ .switch-toggle-slider {
    &::before {
      background: rgba($background, 0.15);
    }
  }
}

// Switch theme
@mixin template-switch-theme($parent, $background) {
  @include template-material-switch-variant($parent, $background);
}

// Switch validation
@mixin template-material-switch-validation-state($state, $color) {
  .switch-input {
    //BS & jQuery validation
    .was-validated &:#{$state},
    &.invalid,
    //jq
    &.is-#{$state} {
      ~ .switch-label {
        color: $color;
      }

      ~ .#{$state}-feedback,
      ~ .#{$state}-tooltip {
        display: block;
      }

      ~ .switch-toggle-slider[class] {
        background: rgba($color, 0.5);
      }

      &:checked ~ .switch-toggle-slider {
        &::after {
          background: $color;
        }

        &::before {
          background: rgba($color, 0.15) !important;
        }
      }
      ~ .switcher-label {
        color: $color;
      }
    }
  }
}
