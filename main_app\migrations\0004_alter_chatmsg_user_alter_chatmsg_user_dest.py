# Generated by Django 4.2.5 on 2023-12-16 08:01

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("main_app", "0003_student_year_dailychallenge_chatmsg"),
    ]

    operations = [
        migrations.AlterField(
            model_name="chatmsg",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="user",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name="chatmsg",
            name="user_dest",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                related_name="user_dest",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
