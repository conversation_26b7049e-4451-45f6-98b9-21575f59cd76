import math
import json
import pandas as pd
import time

from pandasql import sqldf
#import sqlite3


from django.http.response import Http404
from django.shortcuts import render,redirect

from main_app.forms import *
from main_app.models import *

# from django.contrib.auth.models import User

# Extra Imports for the Login and Logout Capabilities
from django.contrib.auth import authenticate, login, logout
from django.http import HttpResponseRedirect, HttpResponse
from django.urls import reverse
from django.contrib.auth.decorators import login_required

from django.conf import settings
from django.core.files.storage import FileSystemStorage

import main_app.utils as utl
from .utils import handle_userinput,get_qst
# IMAGE_FILE_TYPES = ['png', 'jpg', 'jpeg']

#_____________________________CURRENT PROJECT____________________________

def index(request):
    return HttpResponseRedirect('/main_app/register?type=student')
    return render(request,'main_app/index.html')

@login_required
def sign_out(request):
    # Log out the user.
    print("LOGOUT")
    logout(request)
    # Return to homepage.
    return HttpResponseRedirect('/')

def register(request):

    registered = False
    user_form = UserForm(request.POST or None)
    type_reg = request.GET.get('type')
    if (type_reg == 'student'):
        profile_form = StudentForm(request.POST or None)
    if (type_reg == 'lecturer'):
        profile_form = LecturerForm(request.POST or None)
    if (type_reg == 'assistant'):
        profile_form = AssistantForm(request.POST or None)

    if request.method == 'POST':


        # Get info from "both" forms
        # It appears as one form to the user on the .html page

        # Check to see both forms are valid
        if user_form.is_valid() and profile_form.is_valid():

            # Save User Form to Database
            user = user_form.save()

            # Hash the password
            user.set_password(user.password)

            # Update with Hashed password
            user.save()

            # Now we deal with the extra info!

            # Can't commit yet because we still need to manipulate
            profile = profile_form.save(commit=False)

            # Set One to One relationship between
            # UserForm and UserProfileInfoForm
            profile.user = user

            # Check if they provided a profile picture
            # if 'profile_pic' in request.FILES:
            #     print('found it')
                # If yes, then grab it from the POST form reply
                # profile.profile_pic = request.FILES['profile_pic']

            # Now save model
            profile.save()
            # Registration Successful!
            # registered = True

            # if (type_reg == 'student'):
            #     HttpResponseRedirect(reverse('main_app:index'))
            # else:
            return HttpResponseRedirect(reverse('main_app:sign_in'))

        else:
            # One of the forms was invalid if this else gets called.
            print(user_form.errors,profile_form.errors)

    # else:
    #     # Was not an HTTP post so we just render the forms as blank.
    #     user_form = UserForm()
    #     profile_form = UserProfileInfoForm()

    # This is the render and context dictionary to feed
    # back to the registration.html file page.

    # return render(request,'main_app/registration.html',
    #                       {'user_form':user_form,
    #                        'profile_form':profile_form,
    #                        'registered':registered})

    return render(request,'main_app/register.html',
                          {'user_form':user_form,
                           'profile_form':profile_form,
                           'registered':registered})

def sign_in(request):
    if request.method == 'POST':
        # First get the username and password supplied
        username = request.POST.get('username')
        password = request.POST.get('password')

        # Django's built-in authentication function:
        user = authenticate(username=username, password=password)
        # user =  User.objects.get(username = username)

        # If we have a user
        if user:
            #Check it the account is active
            if user.is_active:
                # Log the user in.
                login(request,user)

                if utl.is_student(user):
                    if StudentInfo.objects.filter(user=user):
                        return HttpResponseRedirect(reverse('main_app:student_profile'))
                    return HttpResponseRedirect(reverse('main_app:student_info_wizard'))
                elif utl.is_lecturer(user):
                    return HttpResponseRedirect(reverse('main_app:lecturer_profile'))
                if utl.is_assistant(user):
                    return HttpResponseRedirect(reverse('main_app:assistant_profile'))
            else:
                print("They used username: {} and password: {}".format(username,password))
        else:
            # print("Someone tried to login and failed.")
            print("They used username: {} and password: {}".format(username,password))

            # return HttpResponse("Invalid login details supplied.")
    return render(request, 'main_app/sign_in.html',{'type':"Student"})

def student_profile(request):
    if StudentInfo.objects.filter(user=request.user).exists():
        student_profile=Student.objects.get(user=request.user)
        student_info = StudentInfo.objects.get(user = request.user)
        studentForm=StudentInfo.objects.filter(user=request.user).values()
        studentFormWeight=WeightPS2CLH.objects.values()
        msgs = (ChatMsg.objects.filter(user = request.user)|ChatMsg.objects.filter(user_dest = request.user))#.orderby('id')
        # studentCluster = StudentCluster.objects.get(user=user)

        student_dict = {}
        student_dict2 = {}
        list_prob_student = {}

        #print(studentForm.immediacy)
        #print(studentForm.all())
        #print(student_profile.user.username)

        # student_cluster = studentCluster.cluster
        student_number = student_profile.studentNumber


        # Filtering the variables with student problem, less then 3...
        # count2 = 0
        # for studentF in studentForm.all():
        #     #print(studentF)
        #     for std in studentF.items():
        #         a, b = std
        #         count2 = count2 + 1
        #         #print(count2)
        #         if b < 4 and count2 >= 3 and count2 <= 45:
        #             list_prob_student[a] = b
                    #print(list_prob_student)
            #print(list_prob_student)

        # Merge the variable name format and weight variable
        # count=0
        # for student in studentFormWeight:
        #         for student_tuple in student.items():
        #             count = count + 1
        #             a, b = student_tuple

        #             if (count % 2) == 0:
        #                 c = b
        #                 d = a
        #             else:
        #                 if count > 6:
        #                     #student_dict = { c : b}
        #                     student_dict[c] = b
        #                     student_dict2[d] = b
                    #print(student_tuple)
                    #print(student_dict)
        #for std in student_dict.items():
            #print(std)


        # new_list_est = {}
        # new_list_est2 = {}
        # new_list_est3 = {}


        # for student_lis_cont1 in list_prob_student.items():
        #     for student_lis_cont2 in student_dict2.items():
        #         a, b = student_lis_cont1
        #         c, d = student_lis_cont2
        #         if a.__contains__(c):
        #             new_list_est[a] = b
        #             new_list_est2[c] = d
                    #print(new_list_est)
                    #print(new_list_est2)
        #sorted(new_list_est2.values())
        #print(sorted(new_list_est2.values()))
        #print(new_list_est)
        #print(new_list_est2)

        #new_list_est3 = sorted(new_list_est2, key=new_list_est2.__getitem__)

        # Final merge weight and the right variable name
        # list_student1 = {}
        # list_student2 = {}

        # for student_merg in new_list_est2.items():
        #     for student in student_dict.items():
        #         a, b = student_merg
        #         c, d = student
        #         if b == d:
        #             list_student1[a] = b
        #             list_student2[c] = d

        #print(list_student1)
        #print(list_student2)
        #new_list_est3 = dict(sorted(new_list_est2.items(), key = lambda kv: kv[1], reverse=True))
        # new_list_est3 = dict(sorted(list_student2.items(), key = lambda kv: kv[1], reverse=True))

        #print(new_list_est3)
        #as_json_factor_list3 = json.dumps(new_list_est3)
        #as_json_student_number = json.dumps(student_number)

        return render(request, 'main_app/student_profile.html', {'student_questionneir':student_dict,
                                                            'studentForm':studentForm,
                                                            'student_profile':student_profile,
                                                            # 'student_cluster':student_cluster,
                                                            'msgs':msgs,
                                                            # 'new_list_est3': new_list_est3,
                                                            'StudentClusterModel': student_number,
                                                            'list_prob_student': list_prob_student,
                                                            'student_info':student_info})

@login_required
def student_info_wizard(request):
    user = request.user

    student_form = StudentInfoForm(request.POST or None)
    if request.method == 'POST':
        #ADD FUNCTION TO UPDATE STUDENT INFO

        # Check to see form is valid
        if student_form.is_valid():

            psychology = student_form.cleaned_data['stress'] + student_form.cleaned_data['anxietyFear'] + student_form.cleaned_data['lowStandards'] + student_form.cleaned_data['lowSelfSteem'] + student_form.cleaned_data['feelDepressed'] + student_form.cleaned_data['loneliness']

            selfResponsability = student_form.cleaned_data['immediacy'] + student_form.cleaned_data['notAimExcellence'] + student_form.cleaned_data['badTimeManagement'] + student_form.cleaned_data['setPriorities'] + student_form.cleaned_data['lackSelfControl'] + student_form.cleaned_data['achievePersonalGoals']

            sociology = student_form.cleaned_data['discrimination'] + student_form.cleaned_data['longDistance'] + student_form.cleaned_data['familyIncome'] + student_form.cleaned_data['studyingWorking'] + student_form.cleaned_data['dropout'] + student_form.cleaned_data['addictedSensualImagesVideo']

            communication = student_form.cleaned_data['expressingYourself'] + student_form.cleaned_data['fluencyLanguage'] + student_form.cleaned_data['grammarVocabulary'] + student_form.cleaned_data['understandLecturerClassroom'] + student_form.cleaned_data['understandingReading'] + student_form.cleaned_data['learningProblemImpactCommunication']

            learning = student_form.cleaned_data['reread'] + student_form.cleaned_data['practiceTests'] + student_form.cleaned_data['selfExplanation'] + student_form.cleaned_data['prepareSummaries'] + student_form.cleaned_data['highlightingUnderlining'] + student_form.cleaned_data['preparationQuestionnaire']

            healthWellbeing = student_form.cleaned_data['sleepProblems'] + student_form.cleaned_data['feelAlwaysTired'] + student_form.cleaned_data['eatingUnhealthily'] + student_form.cleaned_data['feelMentallyUnhealthy'] + student_form.cleaned_data['lackEnergyStudyTime'] + student_form.cleaned_data['noRegularPhysicalActivityExercise']

            psychologySelfResponsability = ((psychology + selfResponsability)*100)/60

            sociologyCommunication = ((sociology + communication)*100)/60

            learningHealthWellbeing = ((learning + healthWellbeing)*100)/60

            centroid = str(psychologySelfResponsability)+","+str(sociologyCommunication)+","+str(learningHealthWellbeing)


            #  12.5,25,37.5,50,62.5,75,87.5
            # dist = math.sqrt((x2-x1)**2 + (y2-y1)**2 + (z2-z1)**2)

            centroid1 = {'x1':12.5 , 'y1':12.5 , 'z1':12.5 }
            centroid2 = {'x2':25 , 'y2':25 , 'z2':25 }
            centroid3 = {'x3':37.5 , 'y3':37.5 , 'z3':37.5 }
            centroid4 = {'x4':50 , 'y4':50 , 'z4':50 }
            centroid5 = {'x5':62.5 , 'y5':62.5 , 'z5':62.5 }
            centroid6 = {'x6':75 , 'y6':75 , 'z6':75 }
            centroid7 = {'x7':87.5 , 'y7':87.5 , 'z7':87.5 }


            dist1 = math.sqrt((centroid1['x1']-psychologySelfResponsability)**2 + (centroid1['y1']-sociologyCommunication)**2+ (centroid1['z1']-learningHealthWellbeing)**2)
            dist2 = math.sqrt((centroid2['x2']-psychologySelfResponsability)**2 + (centroid2['y2']-sociologyCommunication)**2+ (centroid2['z2']-learningHealthWellbeing)**2)
            dist3 = math.sqrt((centroid3['x3']-psychologySelfResponsability)**2 + (centroid3['y3']-sociologyCommunication)**2+ (centroid3['z3']-learningHealthWellbeing)**2)
            dist4 = math.sqrt((centroid4['x4']-psychologySelfResponsability)**2 + (centroid4['y4']-sociologyCommunication)**2+ (centroid4['z4']-learningHealthWellbeing)**2)
            dist5 = math.sqrt((centroid5['x5']-psychologySelfResponsability)**2 + (centroid5['y5']-sociologyCommunication)**2+ (centroid5['z5']-learningHealthWellbeing)**2)
            dist6 = math.sqrt((centroid6['x6']-psychologySelfResponsability)**2 + (centroid6['y6']-sociologyCommunication)**2+ (centroid6['z6']-learningHealthWellbeing)**2)
            dist7 = math.sqrt((centroid7['x7']-psychologySelfResponsability)**2 + (centroid7['y7']-sociologyCommunication)**2+ (centroid7['z7']-learningHealthWellbeing)**2)

            if dist1 <= dist2 and dist1 <= dist3 and dist1 <= dist4 and dist1 <= dist5 and dist1 <= dist6 and dist1 <= dist7 :
                cluster = 1
            elif  dist2 <= dist1 and dist2 <= dist3 and dist2 <= dist4 and dist2 <= dist5 and dist2 <= dist6 and dist2 <= dist7 :
                cluster = 2
            elif  dist3 <= dist1 and dist3 <= dist2 and dist3 <= dist4 and dist3 <= dist5 and dist3 <= dist6 and dist3 <= dist7 :
                cluster = 3
            elif  dist4 <= dist1 and dist4 <= dist2 and dist4 <= dist3 and dist4 <= dist5 and dist4 <= dist6 and dist4 <= dist7 :
                cluster = 4
            elif  dist5 <= dist1 and dist5 <= dist2 and dist5 <= dist3 and dist5 <= dist4 and dist5 <= dist6 and dist5 <= dist7 :
                cluster = 5
            elif  dist6 <= dist1 and dist6 <= dist2 and dist6 <= dist3 and dist6 <= dist4 and dist6 <= dist5 and dist6 <= dist7 :
                cluster = 6
            elif  dist7 <= dist1 and dist7 <= dist2 and dist7 <= dist3 and dist7 <= dist4 and dist7 <= dist5 and dist7 <= dist6 :
                cluster = 7

            print(cluster)


            student_info = student_form.save(commit=False)
            # commit=False tells Django that "Don't send this to database yet.
            # I have more things I want to do with it."

            student_info.user = user # Set the user object here
            student_info.save() # Now you can send it to DB

            StudentClusterModel(
                user = request.user,
                psychology =psychology,
                selfResponsability = selfResponsability,
                psychologySelfResponsability = psychologySelfResponsability,
                sociology = sociology,
                communication = communication,
                sociologyCommunication = sociologyCommunication,
                learning = learning,
                healthWellbeing = healthWellbeing,
                learningHealthWellbeing = learningHealthWellbeing,
                centroid = centroid,
                cluster = cluster
            ).save()

            return HttpResponseRedirect(reverse('main_app:student_profile'))

        else:
            # One of the forms was invalid if this else gets called.
            print(student_form.errors)


    # This is the render and context dictionary to feed
    # back to the registration.html file page.

    return render(request,'main_app/student_info_wizard.html',
                          {'user':user,
                        #    'psychology':psychology,
                        #    'selfResponsability':selfResponsability,
                        #    'psychologySelfResponsability':psychologySelfResponsability,
                        #    'sociology':sociology,
                        #    'communication':communication,
                        #    'sociologyCommunication':sociologyCommunication,
                        #    'learning':learning,
                        #    'healthWellbeing':healthWellbeing,
                        #    'learningHealthWellbeing':learningHealthWellbeing,
                        #    'centroid':centroid,
                        #    'student_form':student_form,
                        #    'cluster':cluster
                           })

def lecturer_profile(request):
    students = Student.objects.filter()
    courses = Course.objects.filter()
    course = request.GET.get("course","")
    year = request.GET.get("year","")
    print(request.GET)
    if course:
        students = students.filter(course__id = course)
    if year:
        students = students.filter(year = year)
    args = {"students":students,"courses":courses,"course":course,"year":year}
    return render(request,'main_app/lecturer_profile.html',args)

def assistant_profile(request):
    student = Student.objects.all()
    courses = Course.objects.filter()
    course = request.GET.get("course")
    # turma = request.GET.get("class")
    year = request.GET.get("year")
    if course:
        students = students.filter(course__id = course)
    # if turma:
    #     students = students.filter(classRoom = turma)
    if year:
        students = students.filter(year = year)
    args = {"students":students,"courses":courses,"course":course,"year":year}
    return render(request,'main_app/assistant_profile.html',args)

def chat(request):
    return render(request,'main_app/assistant_profile.html')

def submit_chat(request):
    if request.method == "POST":
        user = request.user
        user_dest = request.POST.get('user_dest',None)
        message = request.POST.get('message')
        answare = ""
        print(request.POST)

        ChatMsg(user = user, user_dest = User.objects.filter(id=user_dest).last() if user_dest else None, message = message).save()
        if not user_dest:
            # answare = ask_bot(user,message)
            response = handle_userinput(message)
            answare = response["answer"]
            ChatMsg(user = None, user_dest = user, message = response["answer"],bot_reply = True).save()

        return HttpResponse(json.dumps({'answare':answare}), content_type='application/json')

def get_all_chat(request):
    if request.method == "GET":
        # print(request.GET.get('user'))
        user = request.GET.get('user')

        msgs = (ChatMsg.objects.filter(user = request.user)|ChatMsg.objects.filter(user_dest = request.user))
        if user:
            msgs = (msgs.filter(user__id = user)|msgs.filter(user_dest__id = user))
        # else:
        #     msgs = (msgs.filter(user = None)|msgs.filter(user_dest = None))

        msgs = msgs.order_by("id")
        return HttpResponse(json.dumps({'msgs':list(msgs.values("user__id","user_dest__id","message"))}), content_type='application/json')

def get_all_users(request):
    if request.method == "GET":

        users = User.objects.exclude(id = request.user.id).values("username","id")

        return HttpResponse(json.dumps({'users':list(users)}), content_type='application/json')

def submit_challenge(request):

    if request.method == 'POST':
        challenges = {}
        DailyChallenge(user = request.user, challenges = challenges)
        return HttpResponse(json.dumps({'success':True}), content_type='application/json')

def get_info(request):
    if request.method == "GET":
        user = request.user
        label = request.GET.get('label',None)

        body = get_qst(label)

        return HttpResponse(json.dumps({'body':body}), content_type='application/json')
#_________________________________________________________________


@login_required
def user_questionnaire(request):
    return render(request,'main_app/form_wizard.html')


@login_required
def user_profile_1(request):
    return render(request,'main_app/profile/profile-1/overview.html')


@login_required
def user_profile_4(request):
    return render(request,'main_app/profile-4.html')


@login_required
def special(request):
    # Remember to also set login url in settings.py!
    # LOGIN_URL = '/main_app/user_login/'
    return HttpResponse("You are logged in. Nice!")


def pysqldf(q):
    return sqldf(q, locals())


def creat_query_from_form(targetVariable, psychology, psychologyVal, self_responsibility, self_responsibilityVal, sociology, sociologyVal,
                          communication, communicationVal, learning, learningVal, health_wellbeing, health_wellbeing_Val, studentData):

    query_concat = ""

    if targetVariable != -1:
        query_concat = " targetVariable = "+ str(targetVariable)
    if psychology != "":
        if psychologyVal != 0:
            if query_concat == "":
                query_concat += psychology +" = "+ str(psychologyVal)
            else:
                query_concat += " and " + psychology +" = "+ str(psychologyVal)
    if self_responsibility != "":
        if self_responsibilityVal != 0:
            if query_concat == "":
                query_concat += self_responsibility +" = "+ str(self_responsibilityVal)
            else:
                query_concat += " and " + self_responsibility +" = "+ str(self_responsibilityVal)
    if sociology != "":
        if sociologyVal != 0:
            if query_concat == "":
                query_concat += sociology +" = "+ str(sociologyVal)
            else:
                query_concat += " and " + sociology +" = "+ str(sociologyVal)
    if communication != "":
        if communicationVal != 0:
            if query_concat == "":
                query_concat += communication +" = "+ str(communicationVal)
            else:
                query_concat += " and " + communication +" = "+ str(communicationVal)
    if learning != "":
        if learningVal != 0:
            if query_concat == "":
                query_concat += learning +" = "+ str(learningVal)
            else:
                query_concat += " and " + learning +" = "+ str(learningVal )
    if health_wellbeing != "":
        if health_wellbeing_Val != 0:
            if query_concat == "":
                query_concat += health_wellbeing +" = "+ str(health_wellbeing_Val)
            else:
                query_concat += " and " + health_wellbeing +" = "+ str(health_wellbeing_Val)


    final_query = '''select studentID, targetVariable from studentData where '''+ query_concat +''' order by lastYearResult'''

    return final_query


def process_visualRep(request):
    #user = request.user

    resultConvert2 = {}


    if request.method == 'POST':

        visualRep_form = VisualRepForm(data=request.POST)


        # Check to see form is valid
        if visualRep_form.is_valid():

            targetVariable = visualRep_form.cleaned_data['targetVariable']
            psychology = visualRep_form.cleaned_data['psychology']
            psychologyVal = visualRep_form.cleaned_data['psychologyVal']
            self_responsibility = visualRep_form.cleaned_data['self_responsibility']
            self_responsibilityVal = visualRep_form.cleaned_data['self_responsibilityVal']
            sociology = visualRep_form.cleaned_data['sociology']
            sociologyVal = visualRep_form.cleaned_data['sociologyVal']
            communication = visualRep_form.cleaned_data['communication']
            communicationVal = visualRep_form.cleaned_data['communicationVal']
            learning = visualRep_form.cleaned_data['learning']
            learningVal = visualRep_form.cleaned_data['learningVal']
            health_wellbeing = visualRep_form.cleaned_data['health_wellbeing']
            health_wellbeing_Val = visualRep_form.cleaned_data['health_wellbeing_Val']

            #db = sqlite3.connect(':memory:')

            #cursor = db.cursor()




            studentData = pd.read_excel('/home/<USER>/Proj/ProactiveChatbot/static/dadosAlun2018.xls','Table1', index_col=None, na_value=['NA'])
            studentCoord = pd.read_excel('/home/<USER>/Proj/ProactiveChatbot/static/studCoord.xls','T1', index_col=None, na_value=['NA'])

            final_query = creat_query_from_form(targetVariable, psychology, psychologyVal, self_responsibility, self_responsibilityVal, sociology, sociologyVal,
                          communication, communicationVal, learning, learningVal, health_wellbeing, health_wellbeing_Val, studentData)

            print(final_query)


            studentData.set_index('studentID',inplace=True)
            studentCoord.set_index('studentID',inplace=True)

            test = sqldf("select * from studentCoord;", locals())

            print(test.head(7))

            query1 = sqldf(final_query, locals())
            #pysqldf(final_query)

            print(query1.head(10))

            #query2 = pysqldf('''select studentCoord.studentID, studentCoord.coord_x, studentCoord.coord_y, studentCoord.coord_z from studentCoord, query1 where studentCoord.studentID = query1.studentID''')

            query2 = '''select sc.studentID, sc.coord_x, sc.coord_y, sc.coord_z
            from studentCoord  sc
            join query1        q1
                on (sc.studentID = q1.studentID) group by q1.studentID '''

            finalResult = sqldf(query2, locals())
            #pysqldf(query2)
            print(finalResult.head(20))

            result = finalResult.to_json(orient="split")
            parsed = json.loads(result)
            resultConvert2 = json.dumps(parsed, indent=4)

            #print(finalResult.head(10).to_json(orient="split"))
            print(resultConvert2)

            time.sleep(5)

        else:
            # One of the forms was invalid if this else gets called.
            print(visualRep_form.errors)

    else:
        # Was not an HTTP post so we just render the forms as blank.
        visualRep_form = VisualRepForm()

    # This is the render and context dictionary to feed
    # back to the process_visualRep.html file page. 'main_app/process_visualRep.html' /start.html  /assistant_viewstud.html

    return render(request,'main_app/process_visualRep.html',
                          {'student_form':visualRep_form,
                          'resultConvert':resultConvert2})


@login_required
def form_wizard_view_(request):

    user = request.user

    if request.method == 'POST':

        student_form = StudentForm(data=request.POST)

        # Check to see form is valid
        if student_form.is_valid():

            psychology = student_form.cleaned_data['stress'] + student_form.cleaned_data['anxietyFear'] + student_form.cleaned_data['lowStandards'] + student_form.cleaned_data['lowSelfSteem'] + student_form.cleaned_data['feelDepressed'] + student_form.cleaned_data['loneliness']

            selfResponsability = student_form.cleaned_data['immediacy'] + student_form.cleaned_data['notAimExcellence'] + student_form.cleaned_data['badTimeManagement'] + student_form.cleaned_data['setPriorities'] + student_form.cleaned_data['lackSelfControl'] + student_form.cleaned_data['achievePersonalGoals']

            sociology = student_form.cleaned_data['discrimination'] + student_form.cleaned_data['longDistance'] + student_form.cleaned_data['familyIncome'] + student_form.cleaned_data['studyingWorking'] + student_form.cleaned_data['dropout'] + student_form.cleaned_data['addictedSensualImagesVideo']

            communication = student_form.cleaned_data['expressingYourself'] + student_form.cleaned_data['fluencyLanguage'] + student_form.cleaned_data['grammarVocabulary'] + student_form.cleaned_data['understandLecturerClassroom'] + student_form.cleaned_data['understandingReading'] + student_form.cleaned_data['learningProblemImpactCommunication']

            learning = student_form.cleaned_data['reread'] + student_form.cleaned_data['practiceTests'] + student_form.cleaned_data['selfExplanation'] + student_form.cleaned_data['prepareSummaries'] + student_form.cleaned_data['highlightingUnderlining'] + student_form.cleaned_data['preparationQuestionnaire']

            healthWellbeing = student_form.cleaned_data['sleepProblems'] + student_form.cleaned_data['feelAlwaysTired'] + student_form.cleaned_data['eatingUnhealthily'] + student_form.cleaned_data['feelMentallyUnhealthy'] + student_form.cleaned_data['lackEnergyStudyTime'] + student_form.cleaned_data['noRegularPhysicalActivityExercise']

            psychologySelfResponsability = ((psychology + selfResponsability)*100)/60

            sociologyCommunication = ((sociology + communication)*100)/60

            learningHealthWellbeing = ((learning + healthWellbeing)*100)/60

            centroid = str(psychologySelfResponsability)+","+str(sociologyCommunication)+","+str(learningHealthWellbeing)


            #  12.5,25,37.5,50,62.5,75,87.5
            # dist = math.sqrt((x2-x1)**2 + (y2-y1)**2 + (z2-z1)**2)

            centroid1 = {'x1':12.5 , 'y1':12.5 , 'z1':12.5 }
            centroid2 = {'x2':25 , 'y2':25 , 'z2':25 }
            centroid3 = {'x3':37.5 , 'y3':37.5 , 'z3':37.5 }
            centroid4 = {'x4':50 , 'y4':50 , 'z4':50 }
            centroid5 = {'x5':62.5 , 'y5':62.5 , 'z5':62.5 }
            centroid6 = {'x6':75 , 'y6':75 , 'z6':75 }
            centroid7 = {'x7':87.5 , 'y7':87.5 , 'z7':87.5 }


            dist1 = math.sqrt((centroid1['x1']-psychologySelfResponsability)**2 + (centroid1['y1']-sociologyCommunication)**2+ (centroid1['z1']-learningHealthWellbeing)**2)
            dist2 = math.sqrt((centroid2['x2']-psychologySelfResponsability)**2 + (centroid2['y2']-sociologyCommunication)**2+ (centroid2['z2']-learningHealthWellbeing)**2)
            dist3 = math.sqrt((centroid3['x3']-psychologySelfResponsability)**2 + (centroid3['y3']-sociologyCommunication)**2+ (centroid3['z3']-learningHealthWellbeing)**2)
            dist4 = math.sqrt((centroid4['x4']-psychologySelfResponsability)**2 + (centroid4['y4']-sociologyCommunication)**2+ (centroid4['z4']-learningHealthWellbeing)**2)
            dist5 = math.sqrt((centroid5['x5']-psychologySelfResponsability)**2 + (centroid5['y5']-sociologyCommunication)**2+ (centroid5['z5']-learningHealthWellbeing)**2)
            dist6 = math.sqrt((centroid6['x6']-psychologySelfResponsability)**2 + (centroid6['y6']-sociologyCommunication)**2+ (centroid6['z6']-learningHealthWellbeing)**2)
            dist7 = math.sqrt((centroid7['x7']-psychologySelfResponsability)**2 + (centroid7['y7']-sociologyCommunication)**2+ (centroid7['z7']-learningHealthWellbeing)**2)

            if dist1 <= dist2 and dist1 <= dist3 and dist1 <= dist4 and dist1 <= dist5 and dist1 <= dist6 and dist1 <= dist7 :
                cluster = 1
            elif  dist2 <= dist1 and dist2 <= dist3 and dist2 <= dist4 and dist2 <= dist5 and dist2 <= dist6 and dist2 <= dist7 :
                cluster = 2
            elif  dist3 <= dist1 and dist3 <= dist2 and dist3 <= dist4 and dist3 <= dist5 and dist3 <= dist6 and dist3 <= dist7 :
                cluster = 3
            elif  dist4 <= dist1 and dist4 <= dist2 and dist4 <= dist3 and dist4 <= dist5 and dist4 <= dist6 and dist4 <= dist7 :
                cluster = 4
            elif  dist5 <= dist1 and dist5 <= dist2 and dist5 <= dist3 and dist5 <= dist4 and dist5 <= dist6 and dist5 <= dist7 :
                cluster = 5
            elif  dist6 <= dist1 and dist6 <= dist2 and dist6 <= dist3 and dist6 <= dist4 and dist6 <= dist5 and dist6 <= dist7 :
                cluster = 6
            elif  dist7 <= dist1 and dist7 <= dist2 and dist7 <= dist3 and dist7 <= dist4 and dist7 <= dist5 and dist7 <= dist6 :
                cluster = 7

            print(cluster)


            student = student_form.save(commit=False)
            # commit=False tells Django that "Don't send this to database yet.
            # I have more things I want to do with it."

            student.user = user # Set the user object here
            student.save() # Now you can send it to DB

        else:
            # One of the forms was invalid if this else gets called.
            print(student_form.errors)

    else:
        # Was not an HTTP post so we just render the forms as blank.
        #user_form = UserForm()
        student_form = StudentForm()

    # This is the render and context dictionary to feed
    # back to the registration.html file page.

    return render(request,'main_app/student_cluster_form.html',
                          {'user':user,
                           'psychology':psychology,
                           'selfResponsability':selfResponsability,
                           'psychologySelfResponsability':psychologySelfResponsability,
                           'sociology':sociology,
                           'communication':communication,
                           'sociologyCommunication':sociologyCommunication,
                           'learning':learning,
                           'healthWellbeing':healthWellbeing,
                           'learningHealthWellbeing':learningHealthWellbeing,
                           'centroid':centroid,
                           'student_form':student_form,
                           'cluster':cluster})


@login_required
def form_cluster(request):

    user = request.user

    if request.method == 'POST':

        student_form = StudentClusterForm(data=request.POST)

        # Check to see form is valid
        if student_form.is_valid():



            student = student_form.save(commit=False)

            student.user = user # Set the user object here
            student.save() # Now you can send it to DB

        else:
            # One of the forms was invalid if this else gets called.
            print(student_form.errors)

    else:
        # Was not an HTTP post so we just render the forms as blank.
        #user_form = UserForm()
        student_form = StudentForm()

    # This is the render and context dictionary to feed
    # back to the registration.html file page.

    return render(request,'main_app/page_user_login_4.html',
                          {'user':user,
                           'student_form':student_form})


def simple_upload(request):
    if request.method == 'POST' and request.FILES['myfile']:
        myfile = request.FILES['myfile']
        fs = FileSystemStorage()
        filename = fs.save(myfile.name, myfile)
        uploaded_file_url = fs.url(filename)
        return render(request, 'core/simple_upload.html', {
            'uploaded_file_url': uploaded_file_url
        })
    return render(request, 'core/simple_upload.html')


def write_json(data, filename="data_set_test.json"):
	with open (filename, "w") as f:
        #print(data)
		json.dump(data, f, indent=4)

def random(request):
    return render(request, 'main_app/profile_lecturer.html', {})

#_______________________STUDENT______________________

@login_required
def chatbot_student(request):
    return render(request,'main_app/chatbot_student.html')
#_______________________ASSISTANT______________________

def registration_assistant(request):

    registered = False

    if request.method == 'POST':

        user_form = UserForm(data=request.POST)
        assistant_form = AssistantForm(data=request.POST)

        if user_form.is_valid() and assistant_form.is_valid():

            user = user_form.save()

            user.set_password(user.password)

            user.save()

            profile = assistant_form.save(commit=False)

            profile.user = user


            profile.save()

            registered = True

        else:
            print(user_form.errors,assistant_form.errors)
    else:
        user_form = UserForm()
        assistant_form = AssistantForm()

    return render(request,'main_app/page_assist_login.html',
                          {'user_form':user_form,
                           'profile_form':assistant_form,
                           'registered':registered})

def assistant_login(request):
    print("Here1")
    if request.method == 'POST':
        print("Here2")
        username = request.POST.get('username')
        password = request.POST.get('password')

        user = authenticate(username=username, password=password)
        print("Here3")
        if user:

            if user.is_active:
                print("Here4")
                login(request,user)
                return render(request, 'main_app/profile_assistant.html', {"user":user})

            else:
                print("Here5")
                return HttpResponse("Your account is not active.")
        else:
            print("Someone tried to login and failed.")
            print("They used username: {} and password: {}".format(username,password))
            return HttpResponse("Invalid login details supplied.")
    else:
        print("Here6")
        return render(request, 'main_app/page_assist_login.html', {})

@login_required
def addQA_assistant(request):
    user = request.user
    return render(request,'main_app/addQuestionAnswer_assist.html', {"user":user})

def addQA_assistant_form(request):

    if request.method == 'POST' and request.FILES['display_picture']:

        #user_form = request.user
        #user_form = UserForm(data=request.POST)
        addQA_form = AddQAForm(request.POST, request.FILES)

        myfile = request.FILES['display_picture']
        fs = FileSystemStorage()
        filename = fs.save(myfile.name, myfile)
        uploaded_file_url = fs.url(filename)

        #addQA_form
        #user_form.userrname = request.user.username
        #user_form.password = request.user.password


        #print("Here 1. "+user_form.username)
        #print("Here 1. "+user_form.username)

        if addQA_form.is_valid():

            #user = user_form.save()
            #user.set_password(user.password)
            #user.save()
            print("Here 2.")
            addQA = addQA_form.save(commit=False)
            #addQA.user = user

            addQA.display_picture = request.FILES['display_picture']
            file_type = addQA.display_picture.url.split('.')[-1]
            file_type = file_type.lower()
            if file_type not in ['png', 'jpg', 'jpeg']:
                print("Here 3.")
                return render(request, 'main_app/addQA_assist_error.html')
            addQA.save()
            print("Here 4.")
            #return render(request, 'main_app/addQA_assist_details', {'addQA': addQA})

            with open ("data_set_test.json") as json_file:
                data = json.load(json_file)
                temp = data
                #y = {}
                y = {
                    "context": addQA.context,
                    "qas": [
                        {
                            "id": "1",
                            "is_impossible": False,
                            "question": addQA.question,
                            "answers": addQA.answer,
                            "rating": [],
                            "urlImg": uploaded_file_url,
                            "studentQ": [{"idSQ":"1", "stdQ":"", "ratingSQ": []}]
                        }
                    ]
                }
                print(y)
                #dct = dict((b, a) for a, b in y)
                #dct = dict(map(y))
                #dct = dict(y)
                #print(dct)
                #temp.append(",")
                temp.append(y)
                print("Here 4.1")

            write_json(data)
            print("Here 4.2")

        else:
            #print(user_form.errors,addQA_form.errors)
            print(addQA_form.errors)
            print("Here 5.")
    else:
        #user_form = UserForm()
        addQA_form = AddQAForm()
        print("Here 6.")

    print("Here 7.")
    return render(request, 'main_app/addQuestionAnswer_assist.html', {"addQA_form": addQA_form})

def page_assist_login(request):
    return render(request,'main_app/page_user_login_4.html',{'type':"Assistant"})

def page_assist_visualRep(request):
    return render(request,'main_app/visual_rep_students.html')

def regist_assist(request):
    return render(request,'main_app/regist_assist.html')

#_______________________LECTCURER______________________

def registration_lecturer(request):

    registered = False

    if request.method == 'POST':

        user_form = UserForm(data=request.POST)
        lecturer_form = LecturerForm(data=request.POST)

        if user_form.is_valid() and lecturer_form.is_valid():

            user = user_form.save()

            user.set_password(user.password)

            user.save()

            profile = lecturer_form.save(commit=False)

            profile.user = user


            profile.save()

            registered = True

        else:
            print(user_form.errors,lecturer_form.errors)
    else:
        user_form = UserForm()
        lecturer_form = LecturerForm()

    return render(request,'main_app/page_lect_login.html',
                          {'user_form':user_form,
                           'lecturer_form':lecturer_form,
                           'registered':registered})

def lecturer_login(request):
    print("oororo")
    if request.method == 'POST':

        username = request.POST.get('username')
        password = request.POST.get('password')

        user = authenticate(username=username, password=password)

        if user:

            if user.is_active:

                login(request,user)
                return render(request, 'main_app/profile_lecturer.html', {})

            else:
                return HttpResponse("Your account is not active.")
        else:
            print("Someone tried to login and failed.")
            print("They used username: {} and password: {}".format(username,password))
            return HttpResponse("Invalid login details supplied.")
    else:
        return render(request, 'main_app/page_lect_login.html', {})

def page_lect_login(request):
    return render(request,'main_app/page_user_login_4.html',{'type':"Lecturer"})

def regist_lecturer(request):
    return render(request,'main_app/regist_lecturer.html')

#________________________________________________________________________________

# def studentStart(url):
#     return render(request,'main_app/profileStudent/dist/machineCouch2.1/start.html')

def get_cluster(request):
    if request.method == "GET":

        client_cl = StudentClusterModel.objects.all()
        client_cluster = '{"columns": ["studentID", "coord_x", "coord_y", "coord_z"], "data":['
        # client_cluster = "["
        for cl in client_cl:
            client_cluster+="["+str(cl.id)+","+cl.get_x()+","+cl.get_y()+","+cl.get_z()+"],"
        client_cluster+="]}"

        return HttpResponse(json.dumps({'client_cluster':client_cluster}), content_type='application/json')

def graph(request):
    client_cl = StudentClusterModel.objects.all()

    if(request.GET.get("year")):
        client_cl = client_cl.filter(user__id__in = list(Student.objects.filter(year = request.GET.get("year")).values("user__id")))

    if(request.GET.get("course")):
        client_cl = client_cl.filter(user__id__in = list(Student.objects.filter(course__id = request.GET.get("course")).values("user__id")))

    client_cluster_lst = ""
    for cl in client_cl:
        client_cluster_lst+="["+str(cl.id)+","+cl.get_x()+","+cl.get_y()+","+cl.get_z()+"],"

    client_cluster='{"columns": ["studentID", "coord_x", "coord_y", "coord_z"], "data":['+client_cluster_lst[0:-1]+"]}"

    return render(request,'main_app/graph.html',{"client_cluster":client_cluster})

def student_machine_coach_profile3(request):
    return render(request,'main_app/profileStudent/dist/assets/plugins/custom/apps/user/profile-3.html')

def student_machine_coach_profile2(request):
    return render(request,'main_app/profileStudent/dist/assets/plugins/custom/apps/user/profile-2.html')

def student_machine_coach_overview(request):
    return render(request,'main_app/profileStudent/dist/assets/plugins/custom/apps/userprofile-1/overview.html')

def student_machine_coach_personal_information(request):
    return render(request,'main_app/profileStudent/dist/assets/plugins/custom/apps/user/profile-1/personal-information.html')
#________________________________________________________________________________

def forgot_password_assistant(request):
    return render(request,'main_app/profileAssistant/dist/custom/pages/login/login-3/forgot.html')

def lecturer_forgot(request):
    return render(request,'main_app/profileLecturer/dist/custom/pages/login/login-4/forgot.html')
