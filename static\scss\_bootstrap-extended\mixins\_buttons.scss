// Buttons
// *******************************************************************************

// Basic
@mixin button-variant(
  $background: null,
  $border: null,
  $hover-background: null,
  $hover-border: null,
  $active-background: null,
  $active-border: null
) {
}
@mixin template-button-variant($parent, $background, $color: null, $border: null) {
  $background: $background;
  $border: $background;
  $color: if($color, $color, color-contrast($background));
  $hover-background: if(
    $color == $color-contrast-light,
    shade-color($background, $btn-hover-bg-shade-amount),
    tint-color($background, $btn-hover-bg-tint-amount)
  );
  $hover-border: if(
    $color == $color-contrast-light,
    shade-color($border, $btn-hover-border-shade-amount),
    tint-color($border, $btn-hover-border-tint-amount)
  );
  $hover-color: color-contrast($hover-background);

  $active-background: if(
    $color == $color-contrast-light,
    shade-color($background, $btn-active-bg-shade-amount),
    tint-color($background, $btn-active-bg-tint-amount)
  );
  $active-border: if(
    $color == $color-contrast-light,
    shade-color($border, $btn-active-border-shade-amount),
    tint-color($border, $btn-active-border-tint-amount)
  );
  $active-color: color-contrast($active-background);

  #{$parent} {
    color: $color;
    @include gradient-bg($background);
    border-color: $border;
    @include box-shadow($btn-box-shadow);
    &:hover {
      color: $hover-color !important;
      @include gradient-bg($hover-background !important);
      border-color: $hover-border !important;
    }

    .btn-check:focus + &,
    &:focus,
    &.focus {
      color: $hover-color;
      @include gradient-bg($hover-background);
      border-color: $hover-border;
      box-shadow: none;
    }

    .btn-check:checked + &,
    .btn-check:active + &,
    &:active,
    &.active,
    &.show.dropdown-toggle,
    .show > &.dropdown-toggle {
      color: $active-color !important;
      background-color: $active-background !important;
      // Remove CSS gradients if they're enabled
      background-image: if($enable-gradients, none !important, null);
      border-color: $active-border !important;

      &:focus {
        box-shadow: none;
      }
    }
  }

  // Button groups
  .btn-group #{$parent},
  .input-group #{$parent} {
    border-right: $input-btn-border-width solid $active-background;
    border-left: $input-btn-border-width solid $active-background;
  }
  .btn-group-vertical #{$parent} {
    border-top-color: $active-background;
    border-bottom-color: $active-background;
  }
}

@mixin template-button-text-variant($parent, $background, $color: null, $border: null) {
  $border: transparent;

  $label-color: if($color, $color, $background);

  $hover-color: $background;
  $hover-background: $background;
  $hover-background: if(
    $hover-color == $color-contrast-light,
    shade-color($background, $btn-text-hover-shade-amount, $card-bg),
    tint-color($background, $btn-text-hover-tint-amount, $card-bg)
  );

  $focus-background: if(
    $hover-color == $color-contrast-light,
    shade-color($background, $btn-text-focus-shade-amount, $card-bg),
    tint-color($background, $btn-text-focus-tint-amount, $card-bg)
  );

  $active-color: $hover-color;
  $active-background: if(
    $active-color == $color-contrast-light,
    shade-color($background, $btn-text-active-shade-amount, $card-bg),
    tint-color($background, $btn-text-active-tint-amount, $card-bg)
  );

  #{$parent} {
    color: $label-color;
    &.waves-effect {
      .waves-ripple {
        background: radial-gradient(
          rgba($background, 0.2) 0,
          rgba($background, 0.3) 40%,
          rgba($background, 0.4) 50%,
          rgba($background, 0.5) 60%,
          rgba($white, 0) 70%
        );
      }
    }

    &:hover {
      border-color: $border;
      background: $hover-background;
      color: $hover-color;
    }

    &:focus,
    &.focus {
      color: $hover-color;
      background: $focus-background;
    }

    &:active,
    &.active,
    &.show.dropdown-toggle,
    .show > &.dropdown-toggle {
      color: $active-color !important;
      background: $active-background !important;
      // Remove CSS gradients if they're enabled
      background-image: if($enable-gradients, none !important, null);
      border-color: $border !important;
    }
  }
}

// Label
@mixin button-label-variant($background: null, $border: null, $active-background: null, $active-border: null) {
}

@mixin template-button-label-variant($parent, $background, $color: null, $border: null) {
  // Using the $dark-style variable for condition as in label style text color can't compare with $color-contrast-light/dark
  $border: transparent;

  $label-color: if($color, $color, $background);
  $hover-color: if($color, $color, color-contrast($background));

  $label-background: if(
    $hover-color == $color-contrast-light,
    shade-color($background, $btn-label-bg-shade-amount, $card-bg),
    tint-color($background, $btn-label-bg-tint-amount, $card-bg)
  );

  $hover-color: $background;
  $hover-background: $background;
  $hover-background: if(
    $hover-color == $color-contrast-light,
    shade-color($background, $btn-label-hover-shade-amount, $card-bg),
    tint-color($background, $btn-label-hover-tint-amount, $card-bg)
  );

  $focus-background: if(
    $hover-color == $color-contrast-light,
    shade-color($background, $btn-label-focus-shade-amount, $card-bg),
    tint-color($background, $btn-label-focus-tint-amount, $card-bg)
  );

  $active-color: $hover-color;
  $active-background: if(
    $active-color == $color-contrast-light,
    shade-color($background, $btn-label-active-shade-amount, $card-bg),
    tint-color($background, $btn-label-active-tint-amount, $card-bg)
  );

  #{$parent} {
    color: $label-color;
    border-color: $label-background;
    background: $label-background;
    &.waves-effect {
      .waves-ripple {
        background: radial-gradient(
          rgba($background, 0.2) 0,
          rgba($background, 0.3) 40%,
          rgba($background, 0.4) 50%,
          rgba($background, 0.5) 60%,
          rgba($white, 0) 70%
        );
      }
    }

    &:hover {
      border-color: $hover-background !important;
      background: $hover-background !important;
      color: $hover-color !important;
    }

    &:focus,
    &.focus {
      color: $hover-color;
      background: $focus-background;
      border-color: $focus-background;
    }

    &:active,
    &.active,
    &.show.dropdown-toggle,
    .show > &.dropdown-toggle {
      color: $active-color !important;
      background-color: $active-background !important;
      // Remove CSS gradients if they're enabled
      background-image: if($enable-gradients, none !important, null);
      border-color: $active-background !important;
    }
  }

  // Button groups
  .btn-group #{$parent},
  .input-group #{$parent} {
    border-right: $input-btn-border-width solid $active-background;
    border-left: $input-btn-border-width solid $active-background;
  }
  .btn-group-vertical #{$parent} {
    border-top-color: $active-background;
    border-bottom-color: $active-background;
  }
}

// Outline
@mixin button-outline-variant($color: null, $color-hover: null, $hover-color: null) {
}

@mixin template-button-outline-variant($parent, $color, $hover-color: null) {
  $color: $color;
  $color-hover: $color;

  $border-color: if(
    $color == $color-contrast-light,
    shade-color($color, $btn-outline-border-shade-amount, $card-bg),
    tint-color($color, $btn-outline-border-tint-amount, $card-bg)
  );

  $hover-background: if(
    $color-hover == $color-contrast-light,
    shade-color($color, $btn-outline-hover-bg-shade-amount, $card-bg),
    tint-color($color, $btn-outline-hover-bg-tint-amount, $card-bg)
  );

  $focus-background: if(
    $color == $color-contrast-light,
    shade-color($color, $btn-outline-focus-bg-shade-amount, $card-bg),
    tint-color($color, $btn-outline-focus-bg-tint-amount, $card-bg)
  );
  $active-background: if(
    $color == $color-contrast-light,
    shade-color($color, $btn-outline-active-bg-shade-amount, $card-bg),
    tint-color($color, $btn-outline-active-bg-tint-amount, $card-bg)
  );
  $active-border: $border-color;
  $active-color: $color;

  #{$parent} {
    color: $color;
    border-color: $border-color;
    background: transparent;
    &.waves-effect {
      .waves-ripple {
        background: radial-gradient(
          rgba($color, 0.2) 0,
          rgba($color, 0.3) 40%,
          rgba($color, 0.4) 50%,
          rgba($color, 0.5) 60%,
          rgba($white, 0) 70%
        );
      }
    }

    &:hover {
      color: $color-hover !important;
      background-color: $hover-background !important;
      border-color: $border-color !important;
    }

    .btn-check:focus + &,
    &:focus {
      color: $color-hover;
      background-color: $focus-background;
      border-color: $active-border;
    }

    .btn-check:checked + &,
    .btn-check:active + &,
    &:active,
    &.active,
    &.dropdown-toggle.show {
      color: $active-color !important;
      background-color: $active-background !important;
      border-color: $active-border !important;
    }
  }

  #{$parent} .badge {
    background: $color;
    border-color: $color;
    color: color-contrast($color);
  }

  #{$parent}:hover .badge,
  #{$parent}:focus:hover .badge,
  #{$parent}:active .badge,
  #{$parent}.active .badge,
  .show > #{$parent}.dropdown-toggle .badge {
    background: $color-hover;
    border-color: $color-hover;
    color: color-contrast($color-hover);
  }
}
