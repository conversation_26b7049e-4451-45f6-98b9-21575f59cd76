/**
 * Class to manage the camera view point selection.
 
 * <AUTHOR>
 * @version 1.0
 * @date 	25/03/2019
 *
 * @param camera	The camera object.
 */
fortes.webGL.env.camera.SelectionHandler = function(camera) {
	// Properties.
	this.camera = camera;			// The camera object.
	this.selectedPointX = 0.0;		// The selected X axis point in the space.
	this.selectedPointY = 0.0;		// The selected Y axis point in the space.
	this.origenX = 0.0;				// The canvas center X point.
	this.origenY = 0.0;				// The canvas center Y point.
	
	/**
	 * Calculate the point XY in the canvas space according to the mouse position in the screen.
	 *
	 * @canvas	The canvas to pan.
	 * @deltaX	The step length in the X axis.
	 * @deltaY	The step length in the Y axis.
	 */
	this.setSelectedPoint = function(event, canvas) {
		this.origenX = canvas.width / 2.0;		
		this.origenY = canvas.height / 2.0;
		this.selectedPointX = (event.offsetX - this.origenX) / this.origenX; 
		this.selectedPointY = ((event.offsetY - this.origenY) / this.origenY) * (-1);
	};
	
	/**
	 * Function to be overiden. It is called in the selection event to clean.
	 */
	this.cleanCall = function(event) {};
	
	/**
	 * Function to be overiden. It is called in the selection event to foward the event.
	 */
	this.selectPointCall = function(event) {};
	
	/**
	  * Get a point position in the current view-projection space.
	  *
	  * @param pointVec4	The point position in the absolute space vector.
	  */
	this.getViewProjPointPosition = function(pointVec4) {
	  var vec4Out = this.camera.projMatrix.multiplyVector4(this.camera.viewMatrix.multiplyVector4(pointVec4));
	  
	  vec4Out[0] /= vec4Out[3];
	  vec4Out[1] /= vec4Out[3];
	  vec4Out[2] /= vec4Out[3];
	  
	  return vec4Out;
  }
};