@use '../../scss/_bootstrap-extended/include' as light;
@use '../../scss/_bootstrap-extended/include-dark' as dark;
@import '../../node_modules/bs-stepper/dist/css/bs-stepper';
@import '../../scss/_custom-variables/libs';

$bs-stepper-header-padding-y: 1.8rem !default;
$bs-stepper-header-padding-x: 1.5rem !default;
$bs-stepper-content-padding-x: 1.25rem !default;
$bs-stepper-content-padding-y: 1.25rem !default;
$bs-stepper-trigger-padding: 1rem !default;
$bs-stepper-trigger-padding-vertical: 0.5rem !default;
$bs-stepper-icon-border-size: 3px !default;
$bs-stepper-svg-icon-height: 3.125rem !default;
$bs-stepper-svg-icon-width: 3.125rem !default;
$bs-stepper-icon-font-size: 1.6rem !default;
$bs-stepper-vertical-separator-height: 1.55rem !default;
$bs-stepper-vertical-header-min-width: 18rem !default;
$bs-stepper-icon-bg-scale: 88%;
$bs-stepper-check-icon-size: 0.875rem;

// Default Styles
.bs-stepper {
  border-radius: light.$border-radius-lg;
  .line {
    flex: 0;
    min-width: auto;
    min-height: auto;
    background-color: transparent;
    margin: 0;
  }

  .bs-stepper-header {
    padding: $bs-stepper-header-padding-y $bs-stepper-header-padding-x;
    .step {
      .step-trigger {
        padding: 0 $bs-stepper-trigger-padding;
        flex-wrap: nowrap;
        font-weight: light.$font-weight-medium;
        &:focus {
          color: inherit;
        }
        .bs-stepper-label {
          margin: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          text-align: start;
          display: inline-flex;
          align-items: center;
          .bs-stepper-number {
            font-weight: light.$font-weight-medium;
            font-size: light.$h2-font-size;
            margin-right: 0.25rem;
            line-height: 1;
          }
          .bs-stepper-title {
            line-height: 1;
            font-weight: light.$font-weight-medium;
            font-size: light.$font-size-base;
          }
          .bs-stepper-subtitle {
            font-size: light.$font-size-xs;
            line-height: light.$font-size-xs;
            font-weight: light.$font-weight-normal;
          }
          @include app-ltr {
            margin-left: 0.35rem;
          }
          @include app-rtl {
            margin-right: 0.35rem;
          }
        }

        &:hover {
          background-color: transparent;
        }
      }

      &:first-child {
        .step-trigger {
          @include app-ltr {
            padding-left: 0;
          }
          @include app-rtl {
            padding-right: 0;
          }
        }
      }
      &:last-child {
        .step-trigger {
          @include app-ltr {
            padding-right: 0;
          }
          @include app-rtl {
            padding-left: 0;
          }
        }
      }
      .bs-stepper-circle {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 1.25rem;
        width: 1.25rem;
        padding: unset;
        i {
          visibility: hidden;
          &::before {
            font-weight: light.$font-weight-medium;
            font-size: $bs-stepper-check-icon-size;
          }
        }
      }
    }
  }
  &:not(.wizard-icons) {
    .bs-stepper-header {
      .line {
        border-width: 1.75px;
        border-style: solid;
        border-radius: $bs-stepper-icon-border-size;
        width: 100%;
        flex-basis: auto;
      }
    }
  }
  .bs-stepper-content {
    padding: $bs-stepper-content-padding-y $bs-stepper-content-padding-x;
    border-radius: light.$border-radius-lg;
  }

  &.vertical {
    .bs-stepper-header {
      min-width: $bs-stepper-vertical-header-min-width;
      .step {
        .step-trigger {
          padding: $bs-stepper-trigger-padding-vertical 0;
        }
        &:first-child {
          .step-trigger {
            padding-top: 0;
          }
        }
        &:last-child {
          .step-trigger {
            padding-bottom: 0;
          }
        }
      }
      .line {
        position: relative;
        min-height: 1px;
        border: none;
        &:before {
          position: absolute;
          top: -0.75rem;
          left: 0.8rem;
          display: block;
          height: $bs-stepper-vertical-separator-height;
          width: $bs-stepper-icon-border-size;
          border-radius: $bs-stepper-icon-border-size;
          content: '';
        }
      }
    }
    .bs-stepper-content {
      width: 100%;
      .content {
        &:not(.active) {
          display: none;
        }
      }
    }

    &.wizard-icons {
      .step {
        text-align: center;
        padding: 0.75rem 0;
      }
      .bs-stepper-header {
        .line:before {
          top: -0.7rem;
          left: 50%;
          margin-left: -0.06rem;
        }
      }
    }
  }

  &.wizard-icons {
    .bs-stepper-header {
      justify-content: space-around;
      .step-trigger {
        flex-direction: column;
        .bs-stepper-icon {
          svg {
            height: $bs-stepper-svg-icon-height;
            width: $bs-stepper-svg-icon-width;
            margin-bottom: 0.5rem;
          }
          i {
            font-size: $bs-stepper-icon-font-size;
          }
        }
      }
    }
  }

  // Remove borders from wizard modern
  &.wizard-modern {
    .bs-stepper-header {
      border-bottom: none !important;
    }
    &.vertical {
      .bs-stepper-header {
        border-right: none !important;
      }
    }
  }
}

// Light style
@if $enable-light-style {
  .light-style {
    .bs-stepper {
      background-color: light.$card-bg;
      .bs-stepper-header {
        border-bottom: 1px solid light.$border-color;

        .step {
          &:not(.active) {
            .bs-stepper-circle {
              background-color: transparent;
              border: $bs-stepper-icon-border-size solid light.$gray-400;
              color: light.$gray-400;
            }
          }
          .step-trigger {
            .bs-stepper-label .bs-stepper-number {
              color: light.$headings-color;
            }
            &:disabled {
              .bs-stepper-label {
                .bs-stepper-number {
                  color: light.$text-muted;
                }
              }
            }
          }
        }
      }

      &.vertical {
        .bs-stepper-header {
          border-bottom: none;
          @include light.media-breakpoint-down(lg) {
            border-right: none !important;
            border-left: none !important;
            border-bottom: 1px solid light.$border-color;
          }
        }
      }

      &.wizard-modern {
        background-color: transparent;
        .bs-stepper-content {
          background-color: light.$card-bg;
          box-shadow: light.$card-box-shadow;
        }
      }

      &:not(.wizard-modern) {
        box-shadow: light.$card-box-shadow;
      }

      &.wizard-icons {
        .bs-stepper-header {
          .bs-stepper-icon {
            svg {
              fill: light.$body-color;
            }
          }
        }
      }
    }
  }

  // ! FIX: Vertical border issue in rtl and ltr
  @include app-rtl(false) {
    &.light-style {
      .bs-stepper {
        &.vertical {
          .bs-stepper-header {
            border-left: 1px solid light.$border-color;
          }
        }
      }
    }
  }
  @include app-ltr(false) {
    &.light-style {
      .bs-stepper {
        &.vertical {
          .bs-stepper-header {
            border-right: 1px solid light.$border-color;
          }
        }
      }
    }
  }
}

// Dark Style
@if $enable-dark-style {
  .dark-style {
    .bs-stepper {
      background-color: dark.$card-bg;

      .bs-stepper-header {
        border-bottom: 1px solid dark.$border-color;
        .bs-stepper-label {
          color: dark.$body-color;
        }
        .line {
          color: dark.$body-color;
        }

        .step {
          &:not(.active) {
            .bs-stepper-circle {
              background-color: transparent;
              border: $bs-stepper-icon-border-size solid dark.$body-color;
              color: dark.$body-color;
            }
          }
          .step-trigger {
            .bs-stepper-label .bs-stepper-number {
              color: dark.$headings-color;
            }
            &:disabled {
              .bs-stepper-label {
                .bs-stepper-number {
                  color: dark.$text-muted;
                }
              }
            }
          }
        }
      }

      &.vertical {
        .bs-stepper-header {
          border-bottom: none;
          @include light.media-breakpoint-down(lg) {
            border-right: none !important;
            border-left: none !important;
            border-bottom: 1px solid dark.$border-color;
          }
        }
      }

      &.wizard-modern {
        background-color: transparent;
        .bs-stepper-content {
          background-color: dark.$card-bg;
          box-shadow: dark.$card-box-shadow;
        }
      }

      &:not(.wizard-modern) {
        box-shadow: dark.$card-box-shadow;
      }

      &.wizard-icons {
        .bs-stepper-header {
          .bs-stepper-icon {
            i {
              color: dark.$body-color;
            }

            svg {
              fill: dark.$body-color;
            }
          }
          .bs-stepper-label {
            color: dark.$body-color;
          }
        }
      }
    }
  }

  // ! FIX: Vertical border issue in rtl and ltr
  @include app-rtl(false) {
    &.dark-style {
      .bs-stepper {
        &.vertical {
          .bs-stepper-header {
            border-left: 1px solid dark.$border-color;
          }
        }
      }
    }
  }
  @include app-ltr(false) {
    &.dark-style {
      .bs-stepper {
        &.vertical {
          .bs-stepper-header {
            border-right: 1px solid dark.$border-color;
          }
        }
      }
    }
  }
}

// RTL
@include app-rtl(false) {
  .bs-stepper {
    .bs-stepper-content {
      .btn-next,
      .btn-prev {
        &:not(.btn-submit) {
          i:before {
            transform: scaleX(-1) !important;
          }
        }
      }
    }

    &.vertical {
      .bs-stepper-header {
        .line:before {
          left: auto;
          right: 0.8rem;
        }
      }

      &.wizard-icons {
        .bs-stepper-header {
          .line:before {
            right: 50%;
          }
        }
      }
    }

    // Remove borders from wizard modern
    &.wizard-modern {
      &.vertical {
        .bs-stepper-header {
          border-left: none !important;
        }
      }
    }

    @include light.media-breakpoint-up(lg) {
      .bs-stepper-header {
        .line {
          i:before {
            transform: scaleX(-1) !important;
          }
        }
      }
    }

    @include light.media-breakpoint-down(lg) {
      .bs-stepper-header {
        .step {
          .step-trigger {
            .bs-stepper-label {
              margin-left: 0;
              margin-right: 0.35rem;
            }
          }
        }
        .line {
          &:before {
            left: 0;
            right: 1.2rem;
          }
        }
      }
      &.wizard-icons {
        .bs-stepper-header {
          .line {
            &:before {
              margin-right: 0.75rem;
            }
          }
        }
      }
    }
  }
}

// Media Queries
@include light.media-breakpoint-down(lg) {
  .bs-stepper {
    .bs-stepper-header {
      flex-direction: column;
      align-items: flex-start;
      .step {
        .step-trigger {
          padding: 0.5rem 0;
          flex-direction: row;
          .bs-stepper-label {
            margin-left: 0.35rem;
          }
        }
        &:first-child {
          .step-trigger {
            padding-top: 0;
          }
        }
        &:last-child {
          .step-trigger {
            padding-bottom: 0;
          }
        }
      }
    }
    &.vertical {
      flex-direction: column;
      .bs-stepper-header {
        align-items: flex-start;
      }

      &.wizard-icons {
        .bs-stepper-header {
          .line:before {
            left: 0.75rem;
            margin-left: 0;
          }
        }
      }
    }
    &:not(.vertical) {
      .bs-stepper-header {
        .line {
          i {
            display: none;
          }
        }
      }
      &:not(.wizard-icons) {
        .bs-stepper-header {
          .line {
            position: relative;
            min-height: 1px;
            border: none;
            &:before {
              position: absolute;
              top: -0.75rem;
              left: 0.8rem;
              display: block;
              height: $bs-stepper-vertical-separator-height;
              width: $bs-stepper-icon-border-size;
              border-radius: $bs-stepper-icon-border-size;
              content: '';
            }
          }
        }
      }
    }
    &.wizard-icons {
      .bs-stepper-header {
        .bs-stepper-icon {
          svg {
            margin-top: 0.5rem;
          }
        }
      }
    }
  }
}

@media (max-width: 520px) {
  .bs-stepper-header {
    margin: 0;
  }
}

// Styles for Create App Modal Wizard
.wizard-vertical-icons {
  &.vertical {
    .bs-stepper-header {
      min-width: $bs-stepper-vertical-header-min-width - 3;
      .step {
        .step-trigger {
          padding-top: 0.7125rem;
          padding-bottom: 0.7125rem;
        }
      }
    }
  }
}

@if $enable-light-style {
  .light-style {
    .wizard-vertical-icons {
      &.vertical {
        .bs-stepper-header {
          .step {
            .avatar-initial {
              background-color: light.shift-color(light.$secondary, $bs-stepper-icon-bg-scale, light.$rgba-to-hex-bg);
              color: light.$secondary;
            }
            &.active {
              .avatar-initial {
                background-color: light.$primary;
                color: light.$white;
              }
            }
          }
        }
      }
    }
  }
}

@if $enable-dark-style {
  .dark-style {
    .wizard-vertical-icons {
      &.vertical {
        .bs-stepper-header {
          .step {
            .avatar-initial {
              background-color: dark.shift-color(dark.$secondary, $bs-stepper-icon-bg-scale, dark.$rgba-to-hex-bg);
              color: dark.$secondary;
            }
            &.active {
              .avatar-initial {
                background-color: dark.$primary;
                color: dark.$white;
              }
            }
          }
        }
      }
    }
  }
}
