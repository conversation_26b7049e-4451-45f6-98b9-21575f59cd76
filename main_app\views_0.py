import json
from langchain.memory.token_buffer import ConversationTokenBufferMemory

# Example function to load chat history
def load_chat_history(filepath: str):
    with open(filepath, 'r') as file:
        chat_history = json.load(file)
    return chat_history

# Modify this part of the create_conversational_retrieval_agent function
# Assume chat_history is loaded using the load_chat_history function
chat_history = load_chat_history('path/to/your/chat_history.json')

if remember_intermediate_steps:
    memory: BaseMemory = AgentTokenBufferMemory(
        memory_key=memory_key, llm=llm, max_token_limit=max_token_limit,
        initial_content=chat_history  # Assuming the memory class supports initializing with content
    )
else:
    memory = ConversationTokenBufferMemory(
        memory_key=memory_key,
        return_messages=True,
        output_key="output",
        llm=llm,
        max_token_limit=max_token_limit,
        initial_content=chat_history  # Assuming the memory class supports initializing with content
    )
