/**
 * Matrix class with its operations.
 
 * <AUTHOR> <PERSON>
 * @version 2.0
 */
fortes.webGL.util.Matrix4x4 = function() {
	this.elements = new Float32Array([1,0,0,0, 0,1,0,0, 0,0,1,0, 0,0,0,1]);

	/**
	 * Set an identity, or the unit, matrix.
	 * @return The own matrix.
	 */
	this.setIdentity = function() {
		var e = this.elements;

		e[0] = 1;   e[4] = 0;   e[8]  = 0;   e[12] = 0;
		e[1] = 0;   e[5] = 1;   e[9]  = 0;   e[13] = 0;
		e[2] = 0;   e[6] = 0;   e[10] = 1;   e[14] = 0;
		e[3] = 0;   e[7] = 0;   e[11] = 0;   e[15] = 1;

		return this;
	};

	/**
	 * Copy matrix.
	 *
	 * @param source The source matrix to be copied.
	 * @return The own matrix.
	 */
	this.set = function(source) {
		var i, s, d;

		s = source.elements;
		d = this.elements;

		if (s === d)
			return;

		for (i = 0; i < 16; ++i) {
			d[i] = s[i];
		}

		return this;
	};

	/**
	 * Multiply the self matrix to the other matrix.
	 *
	 * @param other The multiply matrix.
	 * @return The own matrix.
	 */
	this.multiply = function(other) {
		var i, e, a, b, ai0, ai1, ai2, ai3;

		// Calculate e = a * b
		e = this.elements;
		a = this.elements;
		b = other.elements;

		// If e equals b, copy b to temporary matrix.
		if (e === b) {
			b = new Float32Array(16);

			for (i = 0; i < 16; ++i) {
				b[i] = e[i];
			}
		}

		for (i = 0; i < 4; i++) {
			ai0=a[i];  ai1=a[i+4];  ai2=a[i+8];  ai3=a[i+12];
			e[i]    = ai0 * b[0]  + ai1 * b[1]  + ai2 * b[2]  + ai3 * b[3];
			e[i+4]  = ai0 * b[4]  + ai1 * b[5]  + ai2 * b[6]  + ai3 * b[7];
			e[i+8]  = ai0 * b[8]  + ai1 * b[9]  + ai2 * b[10] + ai3 * b[11];
			e[i+12] = ai0 * b[12] + ai1 * b[13] + ai2 * b[14] + ai3 * b[15];
		}

		return this;
	};
	
	/**
	 * Multiply the other matrix to the self matrix.
	 *
	 * @param other The multiply matrix.
	 * @return The own matrix.
	 */
	this.multiplyLeft = function(other) {
		var i, e, a, b, bi0, bi1, bi2, bi3;

		// Calculate e = b * a
		e = this.elements;
		a = this.elements;
		b = other.elements;

		// If e equals b, copy b to temporary matrix.
		if (e === b) {
			b = new Float32Array(16);

			for (i = 0; i < 16; ++i) {
				b[i] = e[i];
			}
		}

		for (i = 0; i < 4; i++) {
			bi0=b[i];  bi1=b[i+4];  bi2=b[i+8];  bi3=b[i+12];
			e[i]    = bi0 * a[0]  + bi1 * a[1]  + bi2 * a[2]  + bi3 * a[3];
			e[i+4]  = bi0 * a[4]  + bi1 * a[5]  + bi2 * a[6]  + bi3 * a[7];
			e[i+8]  = bi0 * a[8]  + bi1 * a[9]  + bi2 * a[10] + bi3 * a[11];
			e[i+12] = bi0 * a[12] + bi1 * a[13] + bi2 * a[14] + bi3 * a[15];
		}

		return this;
	};

	/**
	 * Multiply the three-dimensional vector.
	 *
	 * @param vec3 The multiply vector
	 * @return The result of multiplication. An array of 3 elements.
	 */
	this.multiplyVector3 = function(vec3) {
		var e = this.elements;
		var result = [0.0, 0.0, 0.0];

		result[0] = vec3[0] * e[0] + vec3[1] * e[4] + vec3[2] * e[ 8] + e[11];
		result[1] = vec3[0] * e[1] + vec3[1] * e[5] + vec3[2] * e[ 9] + e[12];
		result[2] = vec3[0] * e[2] + vec3[1] * e[6] + vec3[2] * e[10] + e[13];

		return result;
	};

	/**
	 * Multiply the four-dimensional vector.
	 *
	 * @param vec4 The multiply vector
	 * @return The result of multiplication. An array of 4 elements.
	 */
	this.multiplyVector4 = function(vec4) {
		var e = this.elements;
		var result = [0.0, 0.0, 0.0, 0.0];

		result[0] = vec4[0] * e[0] + vec4[1] * e[4] + vec4[2] * e[ 8] + vec4[3] * e[12];
		result[1] = vec4[0] * e[1] + vec4[1] * e[5] + vec4[2] * e[ 9] + vec4[3] * e[13];
		result[2] = vec4[0] * e[2] + vec4[1] * e[6] + vec4[2] * e[10] + vec4[3] * e[14];
		result[3] = vec4[0] * e[3] + vec4[1] * e[7] + vec4[2] * e[11] + vec4[3] * e[15];

		return result;
	};

	/**
	 * Set the orthographic projection matrix.
	 *
	 * @param left The coordinate of the left of clipping plane.
	 * @param right The coordinate of the right of clipping plane.
	 * @param bottom The coordinate of the bottom of clipping plane.
	 * @param top The coordinate of the top top clipping plane.
	 * @param near The distances to the nearer depth clipping plane. This value is minus if the plane is to be behind the viewer.
	 * @param far The distances to the farther depth clipping plane. This value is minus if the plane is to be behind the viewer.
	 * @return the own matrix.
	 */
	this.setOrtho = function(left, right, bottom, top, near, far) {
		var e, rw, rh, rd;

		if (left === right || bottom === top || near === far) {
			throw 'null frustum';
		}

		rw = 1 / (right - left);
		rh = 1 / (top - bottom);
		rd = 1 / (far - near);

		e = this.elements;

		e[0]  = 2 * rw;
		e[1]  = 0;
		e[2]  = 0;
		e[3]  = 0;

		e[4]  = 0;
		e[5]  = 2 * rh;
		e[6]  = 0;
		e[7]  = 0;

		e[8]  = 0;
		e[9]  = 0;
		e[10] = -2 * rd;
		e[11] = 0;

		e[12] = -(right + left) * rw;
		e[13] = -(top + bottom) * rh;
		e[14] = -(far + near) * rd;
		e[15] = 1;

		return this;
	};

	/**
	 * Set the perspective projection matrix by fovy and aspect.
	 *
	 * @param fovy The angle between the upper and lower sides of the frustum.
	 * @param aspect The aspect ratio of the frustum. (width/height).
	 * @param near The distances to the nearer depth clipping plane. This value must be plus value.
	 * @param far The distances to the farther depth clipping plane. This value must be plus value.
	 * @return The own matrix.
	 */
	this.setPerspective = function(fovy, aspect, near, far) {
		var e, rd, s, ct;

		if (near === far || aspect === 0)
			throw 'null frustum';

		if (near <= 0)
			throw 'near <= 0';

		if (far <= 0)
			throw 'far <= 0';

		fovy = Math.PI * fovy / 180 / 2;
		s = Math.sin(fovy);

		if (s === 0)
			throw 'null frustum';

		rd = 1 / (far - near);
		ct = Math.cos(fovy) / s;
		e = this.elements;

		e[0]  = ct / aspect;
		e[1]  = 0;
		e[2]  = 0;
		e[3]  = 0;

		e[4]  = 0;
		e[5]  = ct;
		e[6]  = 0;
		e[7]  = 0;

		e[8]  = 0;
		e[9]  = 0;
		e[10] = -(far + near) * rd;
		e[11] = -1;

		e[12] = 0;
		e[13] = 0;
		e[14] = -2 * near * far * rd;
		e[15] = 0;

		return this;
	};

	/**
	 * Set the matrix for translation.
	 *
	 * @param x The X value of a translation.
	 * @param y The Y value of a translation.
	 * @param z The Z value of a translation.
	 * @return The own matrix.
	 */
	this.setTranslate = function(x, y, z) {
		var e = this.elements;

		e[0] = 1;  e[4] = 0;  e[8]  = 0;  e[12] = x;
		e[1] = 0;  e[5] = 1;  e[9]  = 0;  e[13] = y;
		e[2] = 0;  e[6] = 0;  e[10] = 1;  e[14] = z;
		e[3] = 0;  e[7] = 0;  e[11] = 0;  e[15] = 1;

		return this;
	};

	/**
	 * Multiply the matrix for translation from the right.
	 *
	 * @param x The X value of a translation.
	 * @param y The Y value of a translation.
	 * @param z The Z value of a translation.
	 * @return The own matrix.
	 */
	this.translate = function(x, y, z) {
		var e = this.elements;

		e[12] += e[0] * x + e[4] * y + e[8]  * z;
		e[13] += e[1] * x + e[5] * y + e[9]  * z;
		e[14] += e[2] * x + e[6] * y + e[10] * z;
		e[15] += e[3] * x + e[7] * y + e[11] * z;

		return this;
	};

	/**
	 * Set the matrix for scaling.
	 *
	 * @param x The scale factor along the X axis.
	 * @param y The scale factor along the Y axis.
	 * @param z The scale factor along the Z axis.
	 * @return The own matrix.
	 */
	this.setScale = function(x, y, z) {
		var e = this.elements;

		e[0] = x;  e[4] = 0;  e[8]  = 0;  e[12] = 0;
		e[1] = 0;  e[5] = y;  e[9]  = 0;  e[13] = 0;
		e[2] = 0;  e[6] = 0;  e[10] = z;  e[14] = 0;
		e[3] = 0;  e[7] = 0;  e[11] = 0;  e[15] = 1;

		return this;
	};

	/**
	 * Multiply the matrix for scaling from the right.
	 *
	 * @param x The scale factor along the X axis.
	 * @param y The scale factor along the Y axis.
	 * @param z The scale factor along the Z axis.
	 * @return The own matrix.
	 */
	this.scale = function(x, y, z) {
		var e = this.elements;

		e[0] *= x;  e[4] *= y;  e[8]  *= z;
		e[1] *= x;  e[5] *= y;  e[9]  *= z;
		e[2] *= x;  e[6] *= y;  e[10] *= z;
		e[3] *= x;  e[7] *= y;  e[11] *= z;

		return this;
	};

	/**
	 * Set the matrix for rotation.
	 * The vector of rotation axis may not be normalized.
	 * @param angle The angle of rotation (degrees)
	 * @param x The X coordinate of vector of rotation axis.
	 * @param y The Y coordinate of vector of rotation axis.
	 * @param z The Z coordinate of vector of rotation axis.
	 * @return The own matrix.
	 */
	this.setRotate = function(angle, x, y, z) {
		var e, s, c, len, rlen, nc, xy, yz, zx, xs, ys, zs;
		angle = Math.PI * angle / 180;
		e = this.elements;
		s = Math.sin(angle);
		c = Math.cos(angle);

		if (0 !== x && 0 === y && 0 === z) {
			// Rotation around X axis
			if (x < 0)
			  s = -s;

			e[0] = 1;  e[4] = 0;  e[ 8] = 0;  e[12] = 0;
			e[1] = 0;  e[5] = c;  e[ 9] =-s;  e[13] = 0;
			e[2] = 0;  e[6] = s;  e[10] = c;  e[14] = 0;
			e[3] = 0;  e[7] = 0;  e[11] = 0;  e[15] = 1;
		} else if (0 === x && 0 !== y && 0 === z) {
			// Rotation around Y axis
			if (y < 0)
			  s = -s;

			e[0] = c;  e[4] = 0;  e[ 8] = s;  e[12] = 0;
			e[1] = 0;  e[5] = 1;  e[ 9] = 0;  e[13] = 0;
			e[2] =-s;  e[6] = 0;  e[10] = c;  e[14] = 0;
			e[3] = 0;  e[7] = 0;  e[11] = 0;  e[15] = 1;
		} else if (0 === x && 0 === y && 0 !== z) {
			// Rotation around Z axis
			if (z < 0)
			  s = -s;

			e[0] = c;  e[4] =-s;  e[ 8] = 0;  e[12] = 0;
			e[1] = s;  e[5] = c;  e[ 9] = 0;  e[13] = 0;
			e[2] = 0;  e[6] = 0;  e[10] = 1;  e[14] = 0;
			e[3] = 0;  e[7] = 0;  e[11] = 0;  e[15] = 1;
		} else {
			// Rotation around another axis
			len = Math.sqrt(x*x + y*y + z*z);

			if (len !== 1) {
				rlen = 1 / len;
				x *= rlen;
				y *= rlen;
				z *= rlen;
			}

			nc = 1 - c;
			xy = x * y;
			yz = y * z;
			zx = z * x;
			xs = x * s;
			ys = y * s;
			zs = z * s;

			e[ 0] = x*x*nc +  c;
			e[ 1] = xy *nc + zs;
			e[ 2] = zx *nc - ys;
			e[ 3] = 0;

			e[ 4] = xy *nc - zs;
			e[ 5] = y*y*nc +  c;
			e[ 6] = yz *nc + xs;
			e[ 7] = 0;

			e[ 8] = zx *nc + ys;
			e[ 9] = yz *nc - xs;
			e[10] = z*z*nc +  c;
			e[11] = 0;

			e[12] = 0;
			e[13] = 0;
			e[14] = 0;
			e[15] = 1;
		}

		return this;
	};

	/**
	 * Multiply the matrix for rotation from the right.
	 * The vector of rotation axis may not be normalized.
	 *
	 * @param angle The angle of rotation (degrees)
	 * @param x The X coordinate of vector of rotation axis.
	 * @param y The Y coordinate of vector of rotation axis.
	 * @param z The Z coordinate of vector of rotation axis.
	 * @return The own matrix.
	 */
	this.rotate = function(angle, x, y, z) {
		return this.multiply(new fortes.webGL.util.Matrix4x4().setRotate(angle, x, y, z));
	};

	/**
	 * Transpose the matrix.
	 * @return The own matrix.
	 */
	this.transpose = function() {
		var e, t;
		e = this.elements;

		t = e[1];  e[1] =  e[4];  e[4] = t;
		t = e[2];  e[2] =  e[8];  e[8] = t;
		t = e[3];  e[3] =  e[12]; e[12] = t;
		t = e[6];  e[6] =  e[9];  e[9] = t;
		t = e[7];  e[7] =  e[13]; e[13] = t;
		t = e[11]; e[11] = e[14]; e[14] = t;

		return this;
	};

	/**
	 * Calculate the inverse matrix of specified matrix, and set to this.
	 *
	 * @param other The source matrix
	 * @return The own matrix.
	 */
	this.setInverseOf = function(other) {
		var i, s, d, inv, det;
		s = other.elements;
		d = this.elements;
		inv = new Float32Array(16);

		inv[0]  =   s[5]*s[10]*s[15] - s[5] *s[11]*s[14] - s[9] *s[6]*s[15]
				+ s[9]*s[7] *s[14] + s[13]*s[6] *s[11] - s[13]*s[7]*s[10];
		inv[4]  = - s[4]*s[10]*s[15] + s[4] *s[11]*s[14] + s[8] *s[6]*s[15]
				- s[8]*s[7] *s[14] - s[12]*s[6] *s[11] + s[12]*s[7]*s[10];
		inv[8]  =   s[4]*s[9] *s[15] - s[4] *s[11]*s[13] - s[8] *s[5]*s[15]
				+ s[8]*s[7] *s[13] + s[12]*s[5] *s[11] - s[12]*s[7]*s[9];
		inv[12] = - s[4]*s[9] *s[14] + s[4] *s[10]*s[13] + s[8] *s[5]*s[14]
				- s[8]*s[6] *s[13] - s[12]*s[5] *s[10] + s[12]*s[6]*s[9];

		inv[1]  = - s[1]*s[10]*s[15] + s[1] *s[11]*s[14] + s[9] *s[2]*s[15]
				- s[9]*s[3] *s[14] - s[13]*s[2] *s[11] + s[13]*s[3]*s[10];
		inv[5]  =   s[0]*s[10]*s[15] - s[0] *s[11]*s[14] - s[8] *s[2]*s[15]
				+ s[8]*s[3] *s[14] + s[12]*s[2] *s[11] - s[12]*s[3]*s[10];
		inv[9]  = - s[0]*s[9] *s[15] + s[0] *s[11]*s[13] + s[8] *s[1]*s[15]
				- s[8]*s[3] *s[13] - s[12]*s[1] *s[11] + s[12]*s[3]*s[9];
		inv[13] =   s[0]*s[9] *s[14] - s[0] *s[10]*s[13] - s[8] *s[1]*s[14]
				+ s[8]*s[2] *s[13] + s[12]*s[1] *s[10] - s[12]*s[2]*s[9];

		inv[2]  =   s[1]*s[6]*s[15] - s[1] *s[7]*s[14] - s[5] *s[2]*s[15]
				+ s[5]*s[3]*s[14] + s[13]*s[2]*s[7]  - s[13]*s[3]*s[6];
		inv[6]  = - s[0]*s[6]*s[15] + s[0] *s[7]*s[14] + s[4] *s[2]*s[15]
				- s[4]*s[3]*s[14] - s[12]*s[2]*s[7]  + s[12]*s[3]*s[6];
		inv[10] =   s[0]*s[5]*s[15] - s[0] *s[7]*s[13] - s[4] *s[1]*s[15]
				+ s[4]*s[3]*s[13] + s[12]*s[1]*s[7]  - s[12]*s[3]*s[5];
		inv[14] = - s[0]*s[5]*s[14] + s[0] *s[6]*s[13] + s[4] *s[1]*s[14]
				- s[4]*s[2]*s[13] - s[12]*s[1]*s[6]  + s[12]*s[2]*s[5];

		inv[3]  = - s[1]*s[6]*s[11] + s[1]*s[7]*s[10] + s[5]*s[2]*s[11]
				- s[5]*s[3]*s[10] - s[9]*s[2]*s[7]  + s[9]*s[3]*s[6];
		inv[7]  =   s[0]*s[6]*s[11] - s[0]*s[7]*s[10] - s[4]*s[2]*s[11]
				+ s[4]*s[3]*s[10] + s[8]*s[2]*s[7]  - s[8]*s[3]*s[6];
		inv[11] = - s[0]*s[5]*s[11] + s[0]*s[7]*s[9]  + s[4]*s[1]*s[11]
				- s[4]*s[3]*s[9]  - s[8]*s[1]*s[7]  + s[8]*s[3]*s[5];
		inv[15] =   s[0]*s[5]*s[10] - s[0]*s[6]*s[9]  - s[4]*s[1]*s[10]
				+ s[4]*s[2]*s[9]  + s[8]*s[1]*s[6]  - s[8]*s[2]*s[5];

		det = s[0]*inv[0] + s[1]*inv[4] + s[2]*inv[8] + s[3]*inv[12];

		if (det === 0)
			return this;

		det = 1 / det;

		for (i = 0; i < 16; i++)
			d[i] = inv[i] * det;

		return this;
	};

	/**
	 * Calculate the inverse matrix of this, and set to this.
	 *
	 * @return The own matrix.
	 */
	this.invert = function() {
		return this.setInverseOf(this);
	}
};