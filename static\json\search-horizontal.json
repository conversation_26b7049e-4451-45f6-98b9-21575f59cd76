{"pages": [{"name": "Dashboard Analytics", "icon": "mdi-home-outline", "url": "index.html"}, {"name": "Dashboard CRM", "icon": "mdi-chart-donut", "url": "dashboards-crm.html"}, {"name": "Layout Without menu", "icon": "mdi-window-maximize", "url": "layouts-without-menu.html"}, {"name": "Layout Fluid", "icon": "mdi-window-maximize", "url": "layouts-fluid.html"}, {"name": "Layout Container", "icon": "mdi-window-maximize", "url": "layouts-container.html"}, {"name": "Layout Blank", "icon": "mdi-window-maximize", "url": "layouts-blank.html"}, {"name": "Email", "icon": "mdi-email-outline", "url": "app-email.html"}, {"name": "Cha<PERSON>", "icon": "mdi-message-outline", "url": "app-chat.html"}, {"name": "Calendar", "icon": "mdi-calendar-blank-outline", "url": "app-calendar.html"}, {"name": "Ka<PERSON><PERSON>", "icon": "mdi-view-grid-outline", "url": "app-kanban.html"}, {"name": "eCommerce Dashboard", "icon": "mdi-home-outline", "url": "app-ecommerce-dashboard.html"}, {"name": "eCommerce - Product List", "icon": "mdi-list-box", "url": "app-ecommerce-product-list.html"}, {"name": "eCommerce - Add Product", "icon": "mdi-plus-circle-outline", "url": "app-ecommerce-product-add.html"}, {"name": "eCommerce - Category List", "icon": "mdi-format-list-bulleted", "url": "app-ecommerce-category-list.html"}, {"name": "eCommerce - Order List", "icon": "mdi-format-list-bulleted", "url": "app-ecommerce-order-list.html"}, {"name": "eCommerce - Orders Details", "icon": "mdi-playlist-check", "url": "app-ecommerce-order-details.html"}, {"name": "eCommerce - Customers", "icon": "mdi-account-outline", "url": "app-ecommerce-customer-all.html"}, {"name": "eCommerce - Customers Overview", "icon": "mdi-format-list-bulleted", "url": "app-ecommerce-customer-details-overview.html"}, {"name": "eCommerce - Customers Security", "icon": "mdi-shield-plus-outline", "url": "app-ecommerce-customer-details-security.html"}, {"name": "eCommerce - Customers Address and Billing", "icon": "mdi-map-marker-radius", "url": "app-ecommerce-customer-details-billing.html"}, {"name": "eCommerce - Customers Notifications", "icon": "mdi-checkbox-blank-badge-outline", "url": "app-ecommerce-customer-details-notifications.html"}, {"name": "eCommerce - Manage Reviews", "icon": "mdi-message-badge-outline", "url": "app-ecommerce-manage-reviews.html"}, {"name": "eCommerce - Referrals", "icon": "mdi-domain", "url": "app-ecommerce-referral.html"}, {"name": "eCommerce - Settings Store Details", "icon": "mdi-details", "url": "app-ecommerce-settings-detail.html"}, {"name": "eCommerce - Settings Store Payments", "icon": "mdi-cash", "url": "app-ecommerce-settings-payments.html"}, {"name": "eCommerce - Settings Store Checkout", "icon": "mdi-cart-outline", "url": "app-ecommerce-settings-checkout.html"}, {"name": "eCommerce - Settings Shipping & Delivery", "icon": "mdi-truck", "url": "app-ecommerce-settings-shipping.html"}, {"name": "eCommerce - Settings Locations", "icon": "mdi-map-marker-plus", "url": "app-ecommerce-settings-locations.html"}, {"name": "eCommerce - Settings Notifications", "icon": "mdi-checkbox-blank-badge-outline", "url": "app-ecommerce-settings-notifications.html"}, {"name": "Academy - Dashboard", "icon": "mdi-book-open-outline", "url": "app-academy-dashboard.html"}, {"name": "Academy - My Course", "icon": "mdi-format-list-checkbox", "url": "app-academy-course.html"}, {"name": "Academy - Course Details", "icon": "mdi-details", "url": "app-academy-course-details.html"}, {"name": "User List", "icon": "mdi-format-list-checkbox", "url": "app-user-list.html"}, {"name": "User View - Account", "icon": "mdi-account-outline", "url": "app-user-view-account.html"}, {"name": "User View - Security", "icon": "mdi-shield-account-outline", "url": "app-user-view-security.html"}, {"name": "User View - Billing & Plans", "icon": "mdi-file-document-outline", "url": "app-user-view-billing.html"}, {"name": "User View - Notifications", "icon": "mdi-checkbox-blank-badge-outline", "url": "app-user-view-notifications.html"}, {"name": "User View - Connections", "icon": "mdi-link-variant", "url": "app-user-view-connections.html"}, {"name": "Roles", "icon": "mdi-security", "url": "app-access-roles.html"}, {"name": "Permission", "icon": "mdi-block-helper", "url": "app-access-permission.html"}, {"name": "Logistics Dashboard", "icon": "mdi-notebook-multiple", "url": "app-logistics-dashboard.html"}, {"name": "Logistics Fleet", "icon": "mdi-car", "url": "app-logistics-fleet.html"}, {"name": "Invoice List", "icon": "mdi-format-list-checkbox", "url": "app-invoice-list.html"}, {"name": "Invoice Preview", "icon": "mdi-file-document-outline", "url": "app-invoice-preview.html"}, {"name": "Invoice Edit", "icon": "mdi-pencil-outline", "url": "app-invoice-edit.html"}, {"name": "Invoice Add", "icon": "mdi-account-plus-outline", "url": "app-invoice-add.html"}, {"name": "User Profile", "icon": "mdi-account-circle-outline", "url": "pages-profile-user.html"}, {"name": "User Profile - Teams", "icon": "mdi-account-group-outline", "url": "pages-profile-teams.html"}, {"name": "User Profile - Projects", "icon": "mdi-view-grid-outline", "url": "pages-profile-projects.html"}, {"name": "User Profile - Connections", "icon": "mdi-link-variant", "url": "pages-profile-connections.html"}, {"name": "Account <PERSON><PERSON><PERSON> - Account", "icon": "mdi-account-outline", "url": "pages-account-settings-account.html"}, {"name": "Account <PERSON> - Security", "icon": "mdi-shield-account-outline", "url": "pages-account-settings-security.html"}, {"name": "Account Settings - Billing & Plans", "icon": "mdi-file-document-outline", "url": "pages-account-settings-billing.html"}, {"name": "Account Settings - Notifications", "icon": "mdi-checkbox-blank-badge-outline", "url": "pages-account-settings-notifications.html"}, {"name": "Account <PERSON><PERSON><PERSON> - Connections", "icon": "mdi-link-variant", "url": "pages-account-settings-connections.html"}, {"name": "FAQ", "icon": "mdi-help-circle-outline", "url": "pages-faq.html"}, {"name": "Pricing", "icon": "mdi-file-document-outline", "url": "pages-pricing.html"}, {"name": "Error", "icon": "mdi-alert-circle-outline", "url": "pages-misc-error.html"}, {"name": "Under Maintenance", "icon": "mdi-medical-bag", "url": "pages-misc-under-maintenance.html"}, {"name": "Coming Soon", "icon": "mdi-clock-outline", "url": "pages-misc-comingsoon.html"}, {"name": "Not Authorized", "icon": "mdi-account-remove-outline", "url": "pages-misc-not-authorized.html"}, {"name": "Server Error", "icon": "mdi-server-remove", "url": "pages-misc-server-error.html"}, {"name": "Login Basic", "icon": "mdi-login", "url": "auth-login-basic.html"}, {"name": "<PERSON>gin <PERSON>", "icon": "mdi-login", "url": "auth-login-cover.html"}, {"name": "Register Basic", "icon": "mdi-account-plus-outline", "url": "auth-register-basic.html"}, {"name": "Register Cover", "icon": "mdi-account-plus-outline", "url": "auth-register-cover.html"}, {"name": "Register Multi-steps", "icon": "mdi-account-plus-outline", "url": "auth-register-multisteps.html"}, {"name": "Verify Email Basic", "icon": "mdi-email-outline", "url": "auth-verify-email-basic.html"}, {"name": "Verify Email Cover", "icon": "mdi-email-outline", "url": "auth-verify-email-cover.html"}, {"name": "Reset Password Basic", "icon": "mdi-help-circle-outline", "url": "auth-reset-password-basic.html"}, {"name": "Reset Password Cover", "icon": "mdi-help-circle-outline", "url": "auth-reset-password-cover.html"}, {"name": "Forgot Password Basic", "icon": "mdi-help", "url": "auth-forgot-password-basic.html"}, {"name": "Forgot Password Cover", "icon": "mdi-help", "url": "auth-forgot-password-cover.html"}, {"name": "Two Steps Verification Basic", "icon": "mdi-help", "url": "auth-two-steps-basic.html"}, {"name": "Two Steps Verification Cover", "icon": "mdi-help", "url": "auth-two-steps-cover.html"}, {"name": "Help Center Front", "icon": "mdi-help-circle-outline", "url": "../front-pages/help-center-landing.html"}, {"name": "Landing Front", "icon": "mdi-file-document-outline", "url": "../front-pages/landing-page.html"}, {"name": "Pricing Front", "icon": "mdi-file-document-outline", "url": "../front-pages/pricing-page.html"}, {"name": "Checkout Front", "icon": "mdi-cart-outline", "url": "../front-pages/checkout-page.html"}, {"name": "Payment Front", "icon": "mdi-credit-card-outline", "url": "../front-pages/payment-page.html"}, {"name": "Modal Examples", "icon": "mdi-open-in-app", "url": "modal-examples.html"}, {"name": "Checkout Wizard", "icon": "mdi-cart-outline", "url": "wizard-ex-checkout.html"}, {"name": "Property Listing Wizard", "icon": "mdi-home-city-outline", "url": "wizard-ex-property-listing.html"}, {"name": "Create Deal Wizard", "icon": "mdi-gift-outline", "url": "wizard-ex-create-deal.html"}, {"name": "Icons", "icon": "mdi-cube-outline", "url": "icons-mdi.html"}, {"name": "Basic Cards", "icon": "mdi-card-bulleted-outline", "url": "cards-basic.html"}, {"name": "Advance Cards", "icon": "mdi-id-card", "url": "cards-advance.html"}, {"name": "Statistics Cards", "icon": "mdi-chart-line", "url": "cards-statistics.html"}, {"name": "Analytics Cards", "icon": "mdi-chart-line", "url": "cards-analytics.html"}, {"name": "Gamifications Cards", "icon": "mdi-layers-outline", "url": "cards-gamifications.html"}, {"name": "Actions Cards", "icon": "mdi-mouse", "url": "cards-actions.html"}, {"name": "Accordion", "icon": "mdi-arrow-collapse", "url": "ui-accordion.html"}, {"name": "<PERSON><PERSON><PERSON>", "icon": "mdi-alert-circle-outline", "url": "ui-alerts.html"}, {"name": "Badges", "icon": "mdi-checkbox-blank-badge-outline", "url": "ui-badges.html"}, {"name": "Buttons", "icon": "mdi-plus-box-outline", "url": "ui-buttons.html"}, {"name": "Carousel", "icon": "mdi-image-outline", "url": "ui-carousel.html"}, {"name": "Collapse", "icon": "mdi-arrow-collapse", "url": "ui-collapse.html"}, {"name": "Dropdowns", "icon": "mdi-archive-arrow-down-outline", "url": "ui-dropdowns.html"}, {"name": "Footer", "icon": "mdi-dock-bottom", "url": "ui-footer.html"}, {"name": "List Groups", "icon": "mdi-format-list-checkbox", "url": "ui-list-groups.html"}, {"name": "Modals", "icon": "mdi-window-maximize", "url": "ui-modals.html"}, {"name": "<PERSON><PERSON><PERSON>", "icon": "mdi-dock-top", "url": "ui-navbar.html"}, {"name": "<PERSON><PERSON><PERSON>", "icon": "mdi-window-maximize", "url": "ui-offcanvas.html"}, {"name": "Pagination & Breadcrumbs", "icon": "mdi-chevron-double-right", "url": "ui-pagination-breadcrumbs.html"}, {"name": "Progress", "icon": "mdi-tune", "url": "ui-progress.html"}, {"name": "Spinners", "icon": "mdi-reload", "url": "ui-spinners.html"}, {"name": "Tabs & Pills", "icon": "mdi-tab", "url": "ui-tabs-pills.html"}, {"name": "Toasts", "icon": "mdi-message-processing-outline", "url": "ui-toasts.html"}, {"name": "Tooltips & Popovers", "icon": "mdi-message-processing-outline", "url": "ui-tooltips-popovers.html"}, {"name": "Typography", "icon": "mdi-format-size", "url": "ui-typography.html"}, {"name": "Avatar", "icon": "mdi-account-circle-outline", "url": "extended-ui-avatar.html"}, {"name": "BlockUI", "icon": "mdi-fullscreen", "url": "extended-ui-blockui.html"}, {"name": "Drag & Drop", "icon": "mdi-content-copy", "url": "extended-ui-drag-and-drop.html"}, {"name": "Media Player", "icon": "mdi-music", "url": "extended-ui-media-player.html"}, {"name": "Perfect Scrollbar", "icon": "mdi-swap-vertical", "url": "extended-ui-perfect-scrollbar.html"}, {"name": "Star Ratings", "icon": "mdi-star-outline", "url": "extended-ui-star-ratings.html"}, {"name": "SweetAlert2", "icon": "mdi-alert-circle-outline", "url": "extended-ui-sweetalert2.html"}, {"name": "Text Divider", "icon": "mdi-format-vertical-align-center", "url": "extended-ui-text-divider.html"}, {"name": "Timeline Basic", "icon": "mdi-arrow-expand-horizontal", "url": "extended-ui-timeline-basic.html"}, {"name": "Timeline Fullscreen", "icon": "mdi-arrow-expand-horizontal", "url": "extended-ui-timeline-fullscreen.html"}, {"name": "Tour", "icon": "mdi-send-variant-outline", "url": "extended-ui-tour.html"}, {"name": "Treeview", "icon": "mdi-source-fork mdi-rotate-180", "url": "extended-ui-treeview.html"}, {"name": "Miscellaneous", "icon": "mdi-sitemap-outline", "url": "extended-ui-misc.html"}, {"name": "Basic Inputs", "icon": "mdi-form-dropdown", "url": "forms-basic-inputs.html"}, {"name": "Input groups", "icon": "mdi-form-select", "url": "forms-input-groups.html"}, {"name": "Custom Options", "icon": "mdi-circle-outline", "url": "forms-custom-options.html"}, {"name": "Editors", "icon": "mdi-file-document-edit-outline", "url": "forms-editors.html"}, {"name": "File Upload", "icon": "mdi-upload-outline", "url": "forms-file-upload.html"}, {"name": "Pickers", "icon": "mdi-calendar-range", "url": "forms-pickers.html"}, {"name": "Select & Tags", "icon": "mdi-form-select", "url": "forms-selects.html"}, {"name": "Sliders", "icon": "mdi-tune", "url": "forms-sliders.html"}, {"name": "Switches", "icon": "mdi-toggle-switch-outline", "url": "forms-switches.html"}, {"name": "Extras", "icon": "mdi-plus-box-outline", "url": "forms-extras.html"}, {"name": "Vertical Form", "icon": "mdi-file-document-outline", "url": "form-layouts-vertical.html"}, {"name": "Horizontal Form", "icon": "mdi-file-document-outline", "url": "form-layouts-horizontal.html"}, {"name": "Sticky Actions", "icon": "mdi-file-document-outline", "url": "form-layouts-sticky.html"}, {"name": "Numbered Wizard", "icon": "mdi-transit-connection-horizontal", "url": "form-wizard-numbered.html"}, {"name": "Icons Wizard", "icon": "mdi-transit-connection-horizontal", "url": "form-wizard-icons.html"}, {"name": "Form Validation", "icon": "mdi-checkbox-marked-circle-outline", "url": "form-validation.html"}, {"name": "Tables", "icon": "mdi-table", "url": "tables-basic.html"}, {"name": "Datatable Basic", "icon": "mdi-grid", "url": "tables-datatables-basic.html"}, {"name": "Datatable Advanced", "icon": "mdi-grid", "url": "tables-datatables-advanced.html"}, {"name": "Datatable Extensions", "icon": "mdi-grid", "url": "tables-datatables-extensions.html"}, {"name": "Apex Charts", "icon": "mdi-chart-line", "url": "charts-apex.html"}, {"name": "ChartJS", "icon": "mdi-poll", "url": "charts-chartjs.html"}, {"name": "Leaflet Maps", "icon": "mdi-map-outline", "url": "maps-leaflet.html"}], "files": [{"name": "Class Attendance", "subtitle": "By <PERSON>", "src": "img/icons/misc/search-xls.png", "meta": "17kb", "url": "app-file-manager.html"}, {"name": "Passport Image", "subtitle": "By <PERSON>", "src": "img/icons/misc/search-jpg.png", "meta": "35kb", "url": "app-file-manager.html"}, {"name": "Class Notes", "subtitle": "By <PERSON>", "src": "img/icons/misc/search-doc.png", "meta": "153kb", "url": "app-file-manager.html"}, {"name": "Receipt", "subtitle": "By <PERSON><PERSON>", "src": "img/icons/misc/search-jpg.png", "meta": "25kb", "url": "app-file-manager.html"}, {"name": "Social Guide", "subtitle": "By <PERSON>", "src": "img/icons/misc/search-doc.png", "meta": "39kb", "url": "app-file-manager.html"}, {"name": "Expenses", "subtitle": "By <PERSON>", "src": "img/icons/misc/search-xls.png", "meta": "15kb", "url": "app-file-manager.html"}, {"name": "Documentation", "subtitle": "By <PERSON>", "src": "img/icons/misc/search-doc.png", "meta": "200kb", "url": "app-file-manager.html"}, {"name": "Avatar", "subtitle": "<PERSON> <PERSON>", "src": "img/icons/misc/search-jpg.png", "meta": "100kb", "url": "app-file-manager.html"}, {"name": "Data", "subtitle": "By <PERSON>", "src": "img/icons/misc/search-xls.png", "meta": "5kb", "url": "app-file-manager.html"}, {"name": "Gardening Guide", "subtitle": "By <PERSON>", "src": "img/icons/misc/search-doc.png", "meta": "25kb", "url": "app-file-manager.html"}], "members": [{"name": "<PERSON>", "subtitle": "Admin", "src": "img/avatars/1.png", "url": "app-user-view-account.html"}, {"name": "<PERSON><PERSON><PERSON>", "subtitle": "Customer", "src": "img/avatars/2.png", "url": "app-user-view-account.html"}, {"name": "<PERSON><PERSON>", "subtitle": "Staff", "src": "img/avatars/5.png", "url": "app-user-view-account.html"}, {"name": "<PERSON>", "subtitle": "Staff", "src": "img/avatars/7.png", "url": "app-user-view-account.html"}, {"name": "<PERSON>", "subtitle": "Customer", "src": "img/avatars/3.png", "url": "app-user-view-account.html"}, {"name": "<PERSON>", "subtitle": "Admin", "src": "img/avatars/10.png", "url": "app-user-view-account.html"}, {"name": "<PERSON>", "subtitle": "Admin", "src": "img/avatars/12.png", "url": "app-user-view-account.html"}]}