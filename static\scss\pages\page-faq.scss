// * FAQ
// *******************************************************************************

@use '../_bootstrap-extended/include' as light;
@use '../_bootstrap-extended/include-dark' as dark;
@import '../_custom-variables/pages';

$faq-header-min-height: 300px !default;

.faq-header {
  background-repeat: no-repeat;
  background-size: cover !important;
  min-height: $faq-header-min-height !important;
  border-radius: light.$border-radius-lg;
  .input-wrapper {
    position: relative;
    width: 100%;
    max-width: calc(100% - 45%);
  }

  @include light.media-breakpoint-down(sm) {
    .input-wrapper {
      max-width: calc(100% - 30%);
    }
  }
}
@if $enable-light-style {
  .light-style {
    .faq-header {
      background: url('../../../img/pages/header-light.png');
    }
    .bg-faq-section {
      background-color: light.$gray-50;
    }
  }
}

@if $enable-dark-style {
  .dark-style {
    .faq-header {
      background: url('../../../img/pages/header-dark.png');
    }
    .bg-faq-section {
      background-color: dark.$gray-50;
    }
  }
}
