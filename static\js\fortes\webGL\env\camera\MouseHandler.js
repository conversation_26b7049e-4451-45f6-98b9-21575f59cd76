/**
 * Class to manage the camera mouse events.
 
 * <AUTHOR>
 * @version 1.0
 * @date 	22/03/2019
 *
 * @param camera	The camera object.
 */
fortes.webGL.env.camera.MouseHandler = function(camera) {
	// Properties.
	this.camera = camera;			// The camera object.
	this.drag = false;				// A flag to activate the mouse dragging mode.
	this.positionX = 0; 			// The mouse position in the X axis.
	this.positionY = 0;				// The mouse position in the Y axis.
	this.rotationStep = 2.0;		// The step for each rotation iteration.
	this.zoomStep = 0.1;			// The step for each zoom iteration.
	this.rotationEnabled = true;	// Rotation events flag to enable/disable.
	this.panEnabled = true;			// Panning events flag to enable/disable.
	this.zoomEnabled = true;		// Zooming events flag to enable/disable.
	this.selectionEnabled = false;	// Selection events flag to enable/disable.
	
	/**
	 * Add the mouse events for the passed canvas.
	 *
	 * @param canvas	The canvas to set the mouse events.
	 */
	this.addEvents = function(canvas) {
		var _this = this;

		if (canvas != null) {
			canvas.onmousedown = function(event) { _this.mouseDown(event, canvas); };
			canvas.onmouseup = function(event) { _this.mouseUp(event); };
			canvas.onmouseout = function(event) { _this.mouseUp(event); };
			canvas.onmousemove = function(event) { _this.mouseMove(event, canvas); };
			canvas.onwheel = function(event) { _this.mouseWheel(event); return false; };
		}
	};
	
	/**
	 * Remove the mouse events from the passed canvas.
	 *
	 * @canvas	The canvas to set the mouse events.
	 */
	this.removeEvents = function(canvas) {
		if (canvas != null) {
			canvas.onmousedown = null;
			canvas.onmouseup = null;
			canvas.onmouseout = null;
			canvas.onmousemove = null;
			canvas.onwheel = null;
		}
	};
	
	/**
	 * Canvas mouse down event handler.
	 * It starts the selection, rotation and pan event.
	 *
	 * @param event		The mouse event object.
	 * @param canvas	The canvas to handle with.
	 */
	this.mouseDown = function(event, canvas) {
		try {
			if (this.selectionEnabled === true) {
				this.camera.selectionHandler.cleanCall(event);
			
				if (event.button === 1 || (event.button === 0 && event.ctrlKey === true)) {
					this.selectPoint(event, canvas);
				}
			}
			
			if (this.rotationEnabled === true || this.panEnabled === true) {
				if (event.button === 0 && event.ctrlKey !== true) {
					this.positionX = event.clientX; 
					this.positionY = event.clientY; 
					this.drag = true;
				}
			}
		} catch (exp) {
			console.log(exp);
		}
	};
	
	/**
	 * Canvas mouse out event handler.
	 * Rotate or pan the camera around. Panning needs the altKey pressed to perform the dragging.
	 *
	 * @param event		The mouse event object.
	 * @param canvas	The canvas to handle with.
	 */
	this.mouseMove = function(event, canvas) {
		try {
			if (this.drag === true) {
				if (event.altKey === true) {
					this.pan(event, canvas);
				} else {
					this.rotate(event);
				}
			}
		} catch (exp) {
			console.log(exp);
		}
	};

	/**
	 * Canvas mouse up event handler.
	 * Deactivate camera dragging mode.
	 *
	 * @param event	The mouse event object.
	 */
	this.mouseUp = function(event) {
		try {
			if (event.button === 0) {
				this.drag = false;
			}
		} catch (exp) {
			console.log(exp);
		}
	};

	/**
	 * Canvas mouse wheel event handler.
	 * Zoom in/out the view.
	 *
	 * @param event	The mouse event object.
	 */
	this.mouseWheel = function(event) {
		try {
			if (this.selectionEnabled === true) {
				this.camera.selectionHandler.cleanCall(event);
			}
			
			if (this.zoomEnabled === true) {
				this.zoom(event);
			}
		} catch (exp) {
			console.log(exp);
		}
	};
	
	/**
	 * Pan the view to left, right, up and down according to the mouse direction.
	 *
	 * @param The generated event object.
	 */
	this.pan = function(event, canvas) {
		this.camera.panZoomHandler.pan(canvas, event.clientX - this.positionX, this.positionY - event.clientY);
		
		this.positionX = event.clientX;		
		this.positionY = event.clientY;
	};
	
	/**
	 * Rotate the view around the yaw and pitch angles when dragging the mouse.
	 *
	 * @param The generated event object.
	 */
	this.rotate = function(event) {
		var deltaX = event.clientX - this.positionX;
		var deltaY = event.clientY - this.positionY;
		
		this.positionX = event.clientX;		
		this.positionY = event.clientY;
		
		if (deltaX > 0) {
			this.camera.yaw(-this.rotationStep);
		} else if (deltaX < 0) {
			this.camera.yaw(this.rotationStep);
		}

		if (deltaY > 0) {
			this.camera.pitch(this.rotationStep);
		} else if (deltaY < 0) {
			this.camera.pitch(-this.rotationStep);
		}
	};
	
	/**
     * Zoom in/out the view.
     *
     * @param The generated event object.
     */
	this.zoom = function(event) {
		var delta = event.wheelDelta === undefined ? event.deltaY : event.wheelDelta;
		var panZoomHandler = this.camera.panZoomHandler;
	
		if (delta > 0) {
			panZoomHandler.zoom(-this.zoomStep);
		} else if (delta < 0) {
			panZoomHandler.zoom(this.zoomStep);
		}
	};
	
	/**
	 * Select a point in the view-projection space.
	 *
	 * @param event		The mouse event object.
	 * @param canvas	The canvas to handle with.
	 */
	this.selectPoint = function(event, canvas) {
		var selectionHandler = this.camera.selectionHandler;
		
		selectionHandler.setSelectedPoint(event, canvas);
		selectionHandler.selectPointCall(event);
	};
};