/**
 * Submodel to assembly Machine Couch.
 * This class is generated by "WF3DConverter". My application to convert Wavefront objects.
 *
 * <AUTHOR>
 * @version 2.0
 */
machineCouch.model.env.AxeXModel = function(parent) {
	// Inheritance.
	fortes.webGL.util.BasicModel.extendTo(this);
	
	this.parent = parent;												// The parent model.
	this.vertices = new Float32Array([0.0, 0.0, 0.0, 3.0, 0.0, 0.0]);	// The array of vertices.
	this.color = new Float32Array([0.5, 0.0, 0.0, 1.0]);				// Model color.
	this.modelMatrix = new fortes.webGL.util.Matrix4x4();  				// The model matrix.

	this.createBuffers = function(gl) {
		try {
			this.verticesBuffer = this.createVertexBuffer(gl, this.vertices);
			this.clean();
		} catch (exp) {
			console.log(exp);
		}
	};

	/**
	 * Clean the arrays. This must be done after creating the buffers to release unnecessary data.
	 */
	this.clean = function() {
		this.vertices = null;
	};

	this.render = function(world) {
		world.gl.uniform4f(world.u_color, this.color[0], this.color[1], this.color[2], this.color[3]);

		world.gl.bindBuffer(world.gl.ARRAY_BUFFER, this.verticesBuffer);
		world.gl.vertexAttribPointer(world.a_position, 3, world.gl.FLOAT, false, 0, 0); 
		world.gl.enableVertexAttribArray(world.a_position);

		world.gl.drawArrays(world.gl.LINES, 0, 2);
	};
	
	/**
	 * Overridden method to transform and send the model data to the GPU.
	 *
	 * @param world The world object with shared data and the context.
	 */
	this.move = function(world) {
		this.modelMatrix.setIdentity();
		
		if (world.cameraManager.selectedView == "XZ") {
			this.modelMatrix.translate(0.0, 1.01, 0.0);
		} else if (world.cameraManager.selectedView == "YX") {
			this.modelMatrix.translate(0.0, 0.0, 1.01);
		}
		
		world.gl.uniformMatrix4fv(world.u_modelMatrix, false, this.modelMatrix.elements);
	}
};