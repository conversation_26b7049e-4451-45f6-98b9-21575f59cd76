@import './_components/include-dark';
@import './_theme/common';
@import './_theme/libs';
@import './_theme/pages';

$primary-color: #666cff;

body {
  background: $body-bg;
}

.bg-body {
  background: $body-bg !important;
}

@include template-common-theme($primary-color);
@include template-libs-dark-theme($primary-color);
@include template-pages-theme($primary-color);

// Navbar
// ---------------------------------------------------------------------------
@include template-navbar-style('.bg-navbar-theme', $card-bg, $color: $headings-color, $active-color: $headings-color);

.layout-horizontal .layout-navbar {
  box-shadow: 0 1px 0 $border-color;
}

.layout-navbar-fixed .layout-page:not(.window-scrolled) .layout-navbar.navbar-detached {
  background: $body-bg;
}

// Menu
// ---------------------------------------------------------------------------
@include template-menu-style(
  '.bg-menu-theme',
  #222438,
  $color: $headings-color,
  $active-color: $headings-color,
  $active-bg: $primary-color
);

.bg-menu-theme {
  &.menu-horizontal {
    background-color: $card-bg !important;
    .menu-inner .menu-item:not(.menu-item-closing) > .menu-sub {
      background: $card-bg;
    }
  }
}
// Footer
// ---------------------------------------------------------------------------
@include template-footer-style('.bg-footer-theme', #1c1f24, $color: $text-muted, $active-color: $body-color);
