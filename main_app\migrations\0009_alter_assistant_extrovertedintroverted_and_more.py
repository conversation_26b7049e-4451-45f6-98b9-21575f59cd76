# Generated by Django 4.2.5 on 2024-04-17 04:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('main_app', '0008_alter_studentinfo_anxietyfear_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='assistant',
            name='extrovertedIntroverted',
            field=models.IntegerField(choices=[(1, 'Extroverted'), (2, 'Introverted')]),
        ),
        migrations.AlterField(
            model_name='assistant',
            name='judgingPerceiving',
            field=models.IntegerField(choices=[(1, 'Judging'), (2, 'Perceiving')]),
        ),
        migrations.AlterField(
            model_name='assistant',
            name='ps2clhModel',
            field=models.CharField(max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='assistant',
            name='sensingIntuition',
            field=models.IntegerField(choices=[(1, 'Sensing'), (2, 'Intuition')]),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='assistant',
            name='thinkingFeeling',
            field=models.Integer<PERSON>ield(choices=[(1, 'Thinking'), (2, 'Feeling')]),
        ),
        migrations.AlterField(
            model_name='lecturer',
            name='extrovertedIntroverted',
            field=models.IntegerField(choices=[(1, 'Extroverted'), (2, 'Introverted')]),
        ),
        migrations.AlterField(
            model_name='lecturer',
            name='judgingPerceiving',
            field=models.IntegerField(choices=[(1, 'Judging'), (2, 'Perceiving')]),
        ),
        migrations.AlterField(
            model_name='lecturer',
            name='sensingIntuition',
            field=models.IntegerField(choices=[(1, 'Sensing'), (2, 'Intuition')]),
        ),
        migrations.AlterField(
            model_name='lecturer',
            name='thinkingFeeling',
            field=models.IntegerField(choices=[(1, 'Thinking'), (2, 'Feeling')]),
        ),
        migrations.AlterField(
            model_name='student',
            name='countryOrig',
            field=models.CharField(max_length=25, null=True),
        ),
        migrations.AlterField(
            model_name='student',
            name='gender',
            field=models.IntegerField(choices=[(0, 'Masculino'), (1, 'Femenino')]),
        ),
        migrations.AlterField(
            model_name='student',
            name='studentNumber',
            field=models.CharField(max_length=25, null=True),
        ),
    ]
