/**
 * Class to manage the view object, or the camera position in a walking mode.
 
 * <AUTHOR>
 * @version	1.0
 * @date	25/03/2019
 *
 * @param u_viewMatrix		The pointer for the view matrix in the GLSL.
 * @param u_projMatrix		The pointer for the projection matrix in the GLSL.
 * @param upVectorAxis		The up vector axis: CAMERA_UP_VECTOR_AXIS_X, CAMERA_UP_VECTOR_AXIS_Y, CAMERA_UP_VECTOR_AXIS_Z.
 */
fortes.webGL.env.WalkingCamera = function(u_viewMatrix, u_projMatrix, upVectorAxis) {
	// Inheritance.
	var camera = new fortes.webGL.env.Camera(u_viewMatrix, u_projMatrix, upVectorAxis);
	
	camera.extendTo(this);
	delete this.mouseHandler;
	delete this.panZoomHandler;
	delete this.selectionHandler;
	delete this.setMoveFromTarget;
	
	// Constants.
	this.WALKING	= 1;
	this.ROTATING	= 2;

	// Camara properties.
	this.action = this.ROTATING;
	this.calculatePosition = false;
	this.prevAngleX = 0.0;				// The previous absolute rotation angle around the X axis.
	this.prevAngleY = 0.0;				// The previous absolute rotation angle around the Y axis.
	this.prevAngleZ = 0.0;				// The previous absolute rotation angle around the Z axis.

	// Event handlers.
	if (fortes.webGL.env.camera.KeyHandler !== undefined) {
		this.keyHandler = new fortes.webGL.env.camera.KeyHandler(this);		// The keyboard event handler.
	}
	
	/**
	 * Activate the camera setting up the keyboard events in the canvas.
	 * The camera projection matrix is passed to GPU.
	 *
	 * @gl		The webGL context to regist the projection matrix.
	 * @canvas	The canvas to set the keyboard events.
	 */
	this.activate = function(gl, canvas) {
		try {
			if (this.keyHandler !== undefined) {
				this.keyHandler.addEvents(canvas);
			}
			
			gl.uniformMatrix4fv(this.u_projMatrix, false, this.projMatrix.elements);
			this.isActive = true;
		} catch (exp) {
			console.log(exp);
    	}
	};
	
	/**
	 * Deactivate the camera setting up the mouse events in the canvas.
	 *
	 * @canvas		The canvas to set the mouse events.
	 */
	this.deactivate = function(canvas) {
		try {
			if (this.keyHandler !== undefined) {
				this.keyHandler.removeEvents(canvas);
			}
			
			this.isActive = false;
		} catch (exp) {
			console.log(exp);
    	}
	};
	
	/**
	 * Set the camera position looking at a target point.
	 *
	 * @param eyeX			The X axis eye position.
	 * @param eyeY			The Y axis eye position.
	 * @param eyeZ			The Z axis eye position.
	 * @param targetX		The X axis target position.
	 * @param targetY		The Y axis target position.
	 * @param targetZ		The Z axis target position.
	 */
	this.lookAt = function(eyeX, eyeY, eyeZ, targetX, targetY, targetZ) {
		try {
			var deltaX = eyeX - targetX;
			var deltaY = eyeY - targetY;
			var deltaZ = eyeZ - targetZ;
			var pkg = fortes.webGL.env;

			if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_X) {
				this.calculateAnglesForUpVectorAxisX(deltaX, deltaY, deltaZ);
			} else if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_Y) {
				this.calculateAnglesForUpVectorAxisY(deltaX, deltaY, deltaZ);
			} else if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_Z) {
				this.calculateAnglesForUpVectorAxisZ(deltaX, deltaY, deltaZ);
			}

			this.setTranslate(eyeX, eyeY, eyeZ);
		} catch (exp) {
			console.log(exp);
		}
	};
	
	/**
	 * Move the camera (translation, rotation and walking).
	 * Set up the view matrix and pass it to the GPU.
	 *
	 * @param gl The webGL context object.
	 */
	this.move = function(gl) {
		try {
			var pkg = fortes.webGL.env;
			
			if (this.calculatePosition === true) {
				this.findPosition();
			}
			
			// Initialize the view matrix.
			this.viewMatrix.setIdentity();
			
			if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_X) {
				this.rotateWithUpVectorAxisX();
			} else if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_Y) {
				this.rotateWithUpVectorAxisY();
			} else if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_Z) {
				this.rotateWithUpVectorAxisZ();
			}
			
			this.viewMatrix.translate(this.moveX, this.moveY, this.moveZ);

			// Send it to the GPS.
			gl.uniformMatrix4fv(this.u_viewMatrix, false, this.viewMatrix.elements);
		} catch (exp) {
			console.log(exp);
		}
	};
	
	/**
	 * Move on the Z axis.
	 */
	this.walk = function(value) {
		this.moveFromTarget += value;
	};
	
	/**
	 * Copy the absolute angles values to the previous ones.
	 */
	this.setPreviousAngles = function() {
		this.prevAngleX = this.angleX;
		this.prevAngleY = this.angleY;
		this.prevAngleZ = this.angleZ;	
	};
	
	/**
	 * Compute the absolute position according to the yaw angle.
	 */
	this.findPosition = function() {
		var pkg = fortes.webGL.env;
		var radAngle;
		
		if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_X) {
			radAngle = (this.prevAngleX * Math.PI) / 180;
			this.moveY += this.moveFromTarget * Math.cos(radAngle);
			this.moveZ -= this.moveFromTarget * Math.sin(radAngle);
		} else if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_Y) {
			radAngle = (this.prevAngleY * Math.PI) / 180;
			this.moveZ += this.moveFromTarget * Math.cos(radAngle);
			this.moveX -= this.moveFromTarget * Math.sin(radAngle);
		} else if (this.upVectorAxis === pkg.CAMERA_UP_VECTOR_AXIS_Z) {
			radAngle = (this.prevAngleZ * Math.PI) / 180;
			this.moveX += this.moveFromTarget * Math.cos(radAngle);
			this.moveY -= this.moveFromTarget * Math.sin(radAngle);
		}
		
		this.moveFromTarget = 0.0;
		this.calculatePosition = false;
	};
	
	/**
	 * Rotate the camera with the X axis as the up vector.
	 */
	this.rotateWithUpVectorAxisX = function() {
		// Set the initial rotations in the space.
		this.viewMatrix.rotate(90, 0.0, 0.0, 1.0);
		this.viewMatrix.rotate(90, 1.0, 0.0, 0.0);

		// Roll rotation.
		if (this.angleY !== 0.0) {
			this.viewMatrix.rotate(this.angleY, 0.0, 1.0, 0.0);
		}

		// Pitch rotation.
		if (this.angleZ !== 0.0) {
			this.viewMatrix.rotate(this.angleZ, 0.0, 0.0, 1.0);
		}
		
		this.viewMatrix.translate(0.0, this.moveFromTarget, 0.0);

		// Yaw rotation.
		if (this.angleX !== 0.0) {
			this.viewMatrix.rotate(this.angleX, 1.0, 0.0, 0.0);
		}
	};
	
	/**
	 * Rotate the camera with the Y axis as the up vector.
	 */
	this.rotateWithUpVectorAxisY = function() {
		// Roll rotation.
		if (this.angleZ !== 0.0) {
			this.viewMatrix.rotate(this.angleZ, 0.0, 0.0, 1.0);
		}

		// Pitch rotation.
		if (this.angleX !== 0.0) {
			this.viewMatrix.rotate(this.angleX, 1.0, 0.0, 0.0);
		}
		
		this.viewMatrix.translate(0.0, 0.0, this.moveFromTarget);

		// Yaw rotation.
		if (this.angleY !== 0.0) {
			this.viewMatrix.rotate(this.angleY, 0.0, 1.0, 0.0);
		}
	};
	
	/**
	 * Rotate the camera with the Z axis as the up vector.
	 */
	this.rotateWithUpVectorAxisZ = function() {
		// Set the initial rotations in the space.
		this.viewMatrix.rotate(-90, 1.0, 0.0, 0.0);
		this.viewMatrix.rotate(-90, 0.0, 0.0, 1.0);

		// Roll rotation.
		if (this.angleX !== 0.0) {
			this.viewMatrix.rotate(this.angleX, 1.0, 0.0, 0.0);
		}

		// Pitch rotation.
		if (this.angleY !== 0.0) {
			this.viewMatrix.rotate(this.angleY, 0.0, 1.0, 0.0);
		}
		
		this.viewMatrix.translate(this.moveFromTarget, 0.0, 0.0);

		// Yaw rotation.
		if (this.angleZ !== 0.0) {
			this.viewMatrix.rotate(this.angleZ, 0.0, 0.0, 1.0);
		}
	};
};