/**
 * Ajax util object.
 *
 * <AUTHOR>
 * @date		2019-04-21
 * @version		1.0
 */
maria_design.util.AjaxUtil = {
	ajaxRequests: [],
	
	/**
	 * Send data in a GET method request. Then, calls the callback with the returned data.
	 *
	 * @param url					The link to make que request.
	 * @param parameters			The URL parameters to go along.
	 * @param callback				The function to be called after in response.
	 * @param callbackParameters	It can be one or more (array) parameters to pass into the callback function.
	 * @param block			        Flag to block and prevent it to the be called again before response.
	 */
	getRequest: function(url, parameters, callback, callbackParameters, block) {
		if (block !== true || this.isInProgress(url) === false) {
			var xhttp = new XMLHttpRequest();
			var parameter;
			var urlParameters = "";
			var assembledUrl = url;
			var _this = this;

			if (parameters !== undefined && parameters !== null) {
				for (parameter in parameters) {
					if (urlParameters !== "") {
						urlParameters += "&";
					}
					
					urlParameters += parameter + "=" + parameters[parameter];
				}
				
				if (urlParameters !== "") {
					assembledUrl += "?" + urlParameters;
				}
			}

			xhttp.onreadystatechange = function() {
				if (this.readyState == 4 && this.status == 200) {
					if (block === true) {
						_this.clearRequest(url, _this);
					}
					
					if (callback !== undefined && callback !== null) {
					    callback(this.responseText, callbackParameters);
					}
				} else if (this.status >= 400 && this.status <= 511) {
					if (block === true) {
						_this.clearRequest(url, _this);
					}
					
					console.log("ERROR in url: " + assembledUrl + ", Response Status: " + this.status);
				}
			};

			xhttp.open("GET", assembledUrl, true);
			xhttp.send();
			
			return true;
		}
		
		return false;
	},
	
	/**
	 * Send data in a POST method request. Then, calls the callback with the returned data.
	 *
	 * @param url					The link to make que request.
	 * @param formElement			The HTML form tag element.
	 * @param callback				The function to be called after in response.
	 * @param callbackParameters	It can be one or more (array) parameters to pass into the callback function.
	 * @param block			        Flag to block and prevent it to the be called again before response.
	 */
	postRequest: function(url, formElement, callback, callbackParameters, block) {
		if (block !== true || this.isInProgress(url) === false) {
			var xhttp = new XMLHttpRequest();
			var formData = null;
			var _this = this;

			if (formElement !== undefined && formElement !== null) {
				formData = new FormData(formElement);
			}

			xhttp.onreadystatechange = function() {
				if (this.readyState == 4 && this.status == 200) {
					if (block === true) {
						_this.clearRequest(url, _this);
					}
					
					if (callback !== undefined && callback !== null) {
					    callback(this.responseText, callbackParameters);
					}
				} else if (this.status >= 400 && this.status <= 511) {
					if (block === true) {
						_this.clearRequest(url, _this);
					}
					
					console.log("ERROR in url: " + url + ", Response Status: " + this.status);
				}
			};

			xhttp.open("POST", url, true);

			if (formData !== null) {
				xhttp.send(formData);
			} else {
				xhttp.send();
			}
			
			return true;
		}
		
		return false;
	},
	
	/**
	 * Send file or files int the file element. Then, calls the callback with the returned data.
	 *
	 * @param url					The link to make que request.
	 * @param formElement			The HTML form tag element.
	 * @param fileElement			The HTML file tag element.
	 * @param callback				The function to be call after being responded.
	 * @param callbackParameters	It can be one or more (array) parameters to pass into the callback function.
	 * @param block			        Flag to block and prevent it to the be called again before response.
	 */
	/*sendFile: function(url, formElement, fileElement, callback, callbackParameters, block) {
		if (block !== true || this.isInProgress(url) === false) {
			var xhttp = new XMLHttpRequest();
			var formData = new FormData(formElement);
			var file;
			var index = 0;
			var _this = this;

			for (file in fileElement.files) {
				formData.append("file" + (index++), file);
			}

			xhttp.onreadystatechange = function() {
				if (this.readyState == 4 && this.status == 200) {
					if (block === true) {
						_this.clearRequest(url, _this);
					}
					
					if (callback !== undefined && callback !== null) {
					    callback(this.responseText, callbackParameters);
					}
				} else if (this.status >= 400 && this.status <= 511) {
					if (block === true) {
						_this.clearRequest(url, _this);
					}
					
					console.log("ERROR in url: " + url + ", Response Status: " + this.status);
				}
			};

			xhttp.open("POST", url, true);
			xhttp.send(formData);
			
			return true;
		}
		
		return false;
	},*/
	
	/**
	 * Check whether the URL is or not in progress. In progress means, it was requested and still waiting for the answer.
	 * If the url is not in progress, it is set in progress mode.
	 *
	 * @param url	The request link.
	 * @return	True or false if it is or not in progress.	
	 */
	isInProgress: function(url) {
		var inProgress = false;
		var i;
			
		for (i in this.ajaxRequests) {
			if (this.ajaxRequests[i].url === url) {
				inProgress = true;
				break;
			}
		}
		
		if (inProgress === false) {
			this.ajaxRequests.push({url: url, clearHandle: setTimeout(this.clearRequest, 120000, url, this)});
		}
		
		return inProgress;
	},
	
	/**
	 * Clear the request in the request repository.
	 *
	 * @param url	The request link.
	 * @param _this	The own maria_design.util.AjaxUtil object.	
	 */
	clearRequest: function(url, _this) {
		var request;
		
		for (var i in _this.ajaxRequests) {
			request = _this.ajaxRequests[i];
			
			if (request.url === url) {
				clearTimeout(request.clearHandle);
				delete _this.ajaxRequests[i];
				break;
			}
		}
	}
};
