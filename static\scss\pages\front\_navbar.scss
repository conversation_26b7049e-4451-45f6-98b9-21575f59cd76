nav.layout-navbar {
  backdrop-filter: unset !important;
  height: auto !important;
  z-index: 999 !important;
}
.navbar {
  &.landing-navbar {
    box-shadow: none;
    transition: light.$btn-transition;
    transform: unset !important;
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
    @include light.border-bottom-radius(light.$border-radius-xl);
    .navbar-nav {
      .nav-link {
        padding: 0.5rem 0.9375rem;
        @include light.media-breakpoint-down(xl) {
          padding-left: 0.5rem;
          padding-right: 0.5rem;
        }
      }
      .nav-item {
        &.mega-dropdown {
          > .dropdown-menu {
            @include light.media-breakpoint-up(lg) {
              max-width: 1300px;
              inset-inline-start: 50% !important;
              transform: translateX(-50%);
              top: 100%;
            }
            @include light.media-breakpoint-down(lg) {
              background: transparent;
              box-shadow: none;
              border: none;
            }
            .mega-dropdown-link {
              padding-left: 0;
              padding-right: 0;
            }
          }
        }
        .nav-img-col {
          &,
          img {
            border-radius: 0.625rem;
          }
        }
      }
    }
    .landing-nav-menu {
      .navbar-nav {
        @include light.media-breakpoint-down(lg) {
          .nav-item.dropdown {
            .dropdown-menu {
              position: unset !important;
              box-shadow: none;
              border: none;
              margin: 0;
              margin-left: 1rem;
            }
          }
        }
      }
    }
    @include light.media-breakpoint-down(lg) {
      .landing-menu-overlay {
        position: fixed;
        display: none;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(light.$black, 0.78);
        transition: light.$btn-transition;
        z-index: 9998;
      }
      .landing-nav-menu {
        position: fixed;
        display: block !important;
        height: 100%;
        max-width: 300px;
        width: 80%;
        inset-inline-start: -100%;
        top: 0;
        overflow-y: auto;
        transition: all 0.3s ease-in-out;
        z-index: 9999;
        &.show {
          inset-inline-start: 0;
          ~ .landing-menu-overlay {
            display: block;
          }
        }
      }
    }
  }
}

// Light style
@if $enable-light-style {
  .light-style {
    .navbar {
      &.landing-navbar {
        background-color: light.$card-bg;
        border: 1px solid rgba(light.$card-bg, 0.78);
        &.navbar-active {
          box-shadow: light.$box-shadow-sm;
        }
        .navbar-nav {
          .nav-link {
            color: light.$headings-color;
          }
        }
        @include light.media-breakpoint-down(lg) {
          .landing-nav-menu {
            background-color: light.$card-bg;
          }
        }
      }
    }
    .menu-text {
      color: light.$headings-color;
    }
  }
}

// Dark style
@if $enable-dark-style {
  .dark-style {
    .navbar {
      &.landing-navbar {
        background-color: dark.$card-bg;
        border: 1px solid rgba(dark.$card-bg, 0.78);
        &.navbar-active {
          box-shadow: dark.$box-shadow-sm;
        }
        .navbar-nav {
          .nav-link {
            color: dark.$headings-color;
          }
        }
        @include light.media-breakpoint-down(lg) {
          .landing-nav-menu {
            background-color: dark.$card-bg;
          }
        }
      }
      .menu-text {
        color: dark.$headings-color;
      }
    }
  }
}

// RTL
@if $enable-rtl-support {
  [dir='rtl'] {
    .navbar {
      &.landing-navbar {
        .navbar-nav {
          .nav-item {
            &.mega-dropdown {
              > .dropdown-menu {
                @include light.media-breakpoint-up(lg) {
                  transform: translateX(+50%);
                }
              }
            }
          }
        }
      }
    }
  }
}
